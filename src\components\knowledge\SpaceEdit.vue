<template>
  <el-dialog v-model="dialogVisible" :title="title" width="500px" :before-close="handleClose">
    <el-form :model="form" label-position="top">
      <el-form-item label="空间名称" required>
        <el-input v-model="form.name" placeholder="空间名称" maxlength="40" show-word-limit />
        <div class="form-item-desc">1-40 字符</div>
      </el-form-item>
      
      <el-form-item label="空间描述">
        <el-input 
          v-model="form.description" 
          type="textarea" 
          rows="4" 
          placeholder="空间描述" 
          maxlength="400" 
          show-word-limit 
        />
        <div class="form-item-desc">最多400字符</div>
      </el-form-item>
      
      <el-form-item label="LOGO">
        <div class="space-logo-uploader">
          <el-image 
            v-if="form.logo" 
            :src="form.logo" 
            class="space-logo-preview" 
            fit="cover"
          />
          <div v-else class="space-logo-placeholder" @click="handleLogoClick">
            <el-icon><Plus /></el-icon>
            <span>上传LOGO</span>
          </div>
          <input
            ref="logoInput"
            type="file"
            accept="image/*"
            class="logo-input"
            @change="handleLogoChange"
          />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { Plus } from '@element-plus/icons-vue';

export default {
  name: 'SpaceEdit',
  components: {
    Plus
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    spaceData: {
      type: Object,
      default: () => ({
        id: '',
        name: '',
        description: '',
        logo: ''
      })
    },
    isCreate: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        id: '',
        name: '',
        description: '',
        logo: ''
      }
    };
  },
  computed: {
    title() {
      return this.isCreate ? '新建知识空间' : '编辑知识空间';
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.initForm();
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false);
      }
    }
  },
  methods: {
    initForm() {
      this.form = {
        id: this.spaceData.id || '',
        name: this.spaceData.name || '',
        description: this.spaceData.description || '',
        logo: this.spaceData.logo || ''
      };
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleSave() {
      if (!this.form.name) {
        this.$message.warning('请输入空间名称');
        return;
      }
      
      if (this.form.name.length < 1 || this.form.name.length > 40) {
        this.$message.warning('空间名称长度应为1-40个字符');
        return;
      }
      
      if (this.form.description && this.form.description.length > 400) {
        this.$message.warning('空间描述不能超过400字符');
        return;
      }
      
      this.$emit('save', {...this.form});
      this.handleClose();
    },
    handleLogoClick() {
      this.$refs.logoInput.click();
    },
    handleLogoChange(e) {
      const file = e.target.files[0];
      if (!file) return;
      
      // 检查文件类型
      if (!file.type.includes('image/')) {
        this.$message.error('请上传图片文件');
        return;
      }
      
      // 文件大小限制（2MB）
      if (file.size > 2 * 1024 * 1024) {
        this.$message.error('图片大小不能超过2MB');
        return;
      }
      
      const reader = new FileReader();
      reader.onload = (e) => {
        this.form.logo = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  }
};
</script>

<style scoped lang="scss">
.form-item-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.space-logo-uploader {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  
  &:hover {
    border-color: #409EFF;
  }
  
  .space-logo-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .space-logo-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #f5f7fa;
    color: #909399;
    
    .el-icon {
      font-size: 24px;
      margin-bottom: 8px;
    }
    
    span {
      font-size: 12px;
    }
  }
  
  .logo-input {
    display: none;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
