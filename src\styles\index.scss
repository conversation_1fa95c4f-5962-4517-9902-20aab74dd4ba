@use "./reset.scss" as *;

// 在这里可以添加其他全局样式
.flex-h {
  display: flex;
  align-items: center;
}

.flex-v {
  display: flex;
  flex-direction: column;
}

.flex-h-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-ellipsis {
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

//需line-clamp-x组合一起用
.line-clamp {
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
  word-wrap: break-word;
}
// 1行
.line-clamp-1 {
  -webkit-line-clamp: 1;
}
// 2行
.line-clamp-2 {
  -webkit-line-clamp: 2;
}

// 3行
.line-clamp-3 {
  -webkit-line-clamp: 3;
}
