<template>
  <el-dialog
    v-model="dialogVisible"
    title="权限设置"
    width="620px"
    :before-close="handleClose"
  >
    <div class="permission-dialog-content">
      <div class="content-header">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索成员"
          :prefix-icon="Search"
          clearable
          @input="handleSearch"
          @clear="handleSearch"
        />
        <el-button type="primary" @click="handleAddMember">
          <el-icon><Plus /></el-icon>
          添加成员
        </el-button>
      </div>

      <div class="member-list">
        <el-table :data="memberList" style="width: 100%">
          <el-table-column prop="name" label="成员信息" />
          <el-table-column prop="role" label="角色">
            <template #default="scope">
              <span>{{ scope.row.role == 'admin' ? '管理员' : '成员' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="joinTime" label="加入时间" />
          <el-table-column label="操作" width="180">
            <template #default="scope">
              <el-button 
                v-if="scope.row.role !== 'admin'"
                type="primary" 
                size="small" 
                plain 
                @click="handleSetAdmin(scope.row)"
              >
                设为管理员
              </el-button>
              <el-button 
                v-else
                type="warning" 
                size="small" 
                plain 
                @click="handleCancelAdmin(scope.row)"
              >
                取消管理员
              </el-button>
              <el-button 
                type="danger" 
                size="small" 
                plain 
                @click="handleRemoveMember(scope.row)"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="empty-tip" v-if="memberList.length === 0 && !loading">
        <el-empty description="暂无成员" />
      </div>

      <div class="loading" v-if="loading">
        <el-skeleton :rows="3" animated />
      </div>
    </div>
      
    <!-- 添加成员对话框 -->
    <el-dialog
      v-model="addMemberDialogVisible"
      title="添加成员"
      width="620px"
      :before-close="() => addMemberDialogVisible = false"
      append-to-body
    >
      <div class="member-dialog-content">
        <el-select
          v-model="selectedMemberIds"
          multiple
          filterable
          placeholder="请选择成员"
          style="width: 100%"
          @change="handleSelectedMembersChange"
        >
          <el-option
            v-for="member in workspaceMembers"
            :key="member.id"
            :label="member.name"
            :value="member.id"
            :disabled="isExistingMember(member.id)"
          >
            <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
              <div style="display: flex; align-items: center;">
                <el-avatar 
                  :size="28" 
                  style="margin-right: 12px; background-color: #409EFF; color: white; font-size: 14px;"
                >
                  {{ member.avatar_url ? '' : member.name.charAt(0).toUpperCase() }}
                </el-avatar>
                <span style="font-size: 14px;">{{ member.name }}</span>
              </div>
              <el-tag size="small" type="success" v-if="isExistingMember(member.id)">已添加</el-tag>
            </div>
          </el-option>
        </el-select>
        
        <div class="selected-members-container" v-if="selectedMembers.length > 0">
          <div class="selected-title">已选择 {{ selectedMembers.length }} 位成员</div>
          <div class="selected-members-list">
            <div v-for="member in selectedMembers" :key="member.id" class="selected-member-item">
              <div class="selected-member-info">
                <el-avatar 
                  :size="28"
                  class="selected-member-avatar"
                  style="background-color: #409EFF; color: white; font-size: 14px;"
                >
                  {{ member.avatar_url ? '' : member.name.charAt(0).toUpperCase() }}
                </el-avatar>
                <span class="selected-member-name">{{ member.name }}</span>
              </div>
              <el-icon class="remove-selected" @click="removeSelectedMember(member)">
                <Close />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addMemberDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddMembers">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { Search, Plus, Close } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getDocumentMembers, addDocumentMembers, updateDocumentMemberRole, removeDocumentMember, fetchWorkspaceMembers } from '@/api/knowledge/document';

export default {
  name: 'DocumentPermission',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    documentId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      searchKeyword: '',
      loading: false,
      memberList: [],
      addMemberDialogVisible: false,
      selectedMemberIds: [],
      selectedMembers: [],
      workspaceMembers: []
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.fetchMembers();
        this.fetchWorkspaceMembers();
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false);
      }
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false;
    },
    handleSearch() {
      this.fetchMembers();
    },
    fetchMembers() {
      this.loading = true;
      getDocumentMembers(this.documentId)
        .then(response => {
          if (response && response.members) {
            this.memberList = response.members.map(member => {
              // 从workspaceMembers中查找匹配的成员信息
              const workspaceMember = this.workspaceMembers.find(wm => wm.id === member.account_id);
              
              return {
                id: member.id,
                account_id: member.account_id,
                name: workspaceMember ? workspaceMember.name : (member.name || '未命名用户'),
                avatar: workspaceMember ? (workspaceMember.avatar_url || '') : (member.avatar_url || ''),
                role: member.role || 'member',
                joinTime: this.formatTimestamp(member.created_at)
              };
            });
          } else {
            this.memberList = [];
          }
        })
        .catch(error => {
          console.error('获取文档成员列表失败', error);
          ElMessage.error('获取文档成员列表失败');
          this.memberList = [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    fetchWorkspaceMembers() {
      fetchWorkspaceMembers()
        .then(response => {
          if (response && response.accounts) {
            this.workspaceMembers = response.accounts.map(member => ({
              id: member.id,
              name: member.name || '未命名用户',
              avatar_url: member.avatar_url || ''
            }));
          } else {
            this.workspaceMembers = [];
          }
        })
        .catch(error => {
          console.error('获取工作空间成员列表失败', error);
          ElMessage.error('获取工作空间成员列表失败');
          this.workspaceMembers = [];
        });
    },
    handleAddMember() {
      this.selectedMemberIds = [];
      this.selectedMembers = [];
      this.addMemberDialogVisible = true;
    },
    handleSelectedMembersChange(ids) {
      this.selectedMembers = this.workspaceMembers.filter(member => ids.includes(member.id));
    },
    removeSelectedMember(member) {
      this.selectedMemberIds = this.selectedMemberIds.filter(id => id !== member.id);
      this.selectedMembers = this.selectedMembers.filter(m => m.id !== member.id);
    },
    confirmAddMembers() {
      if (this.selectedMemberIds.length === 0) {
        ElMessage.warning('请选择要添加的成员');
        return;
      }

      this.loading = true;
      
      // 创建一个Promise数组来跟踪所有添加成员的请求
      const addPromises = this.selectedMemberIds.map(accountId => {
        // 默认添加成员的角色为'member'
        return addDocumentMembers(this.documentId, accountId, 'member')
          .catch(error => {
            console.error(`添加成员 ${accountId} 失败`, error);
            return Promise.reject(error);
          });
      });
      
      // 等待所有请求完成
      Promise.all(addPromises)
        .then(responses => {
          ElMessage.success(`已添加 ${this.selectedMembers.length} 位成员`);
          this.addMemberDialogVisible = false;
          this.fetchMembers();
        })
        .catch(error => {
          console.error('添加成员失败', error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSetAdmin(member) {
      ElMessageBox.confirm(
        `确定将 ${member.name} 设为管理员吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        this.loading = true;
        updateDocumentMemberRole(this.documentId, member.account_id, member.role == 'admin' ? 'member' : 'admin')
          .then(response => {
            ElMessage.success(`已将 ${member.name} 设为管理员`);
            this.fetchMembers();
          })
          .catch(error => {
            console.error('设置管理员失败', error);
            ElMessage.error('设置管理员失败');
          })
          .finally(() => {
            this.loading = false;
          });
      }).catch(() => {});
    },
    handleCancelAdmin(member) {
      ElMessageBox.confirm(
        `确定取消 ${member.name} 的管理员权限吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        this.loading = true;
        updateDocumentMemberRole(this.documentId, member.account_id, member.role == 'admin' ? 'member' : 'admin')
          .then(response => {
            ElMessage.success(`已取消 ${member.name} 的管理员权限`);
            this.fetchMembers();
          })
          .catch(error => {
            console.error('取消管理员失败', error);
            ElMessage.error('取消管理员失败');
          })
          .finally(() => {
            this.loading = false;
          });
      }).catch(() => {});
    },
    handleRemoveMember(member) {
      ElMessageBox.confirm(
        `确定将 ${member.name} 从文档权限中移除吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'danger'
        }
      ).then(() => {
        this.loading = true;
        removeDocumentMember(this.documentId, member.account_id)
          .then(response => {
            ElMessage.success(`已将 ${member.name} 从文档权限中移除`);
            this.fetchMembers();
          })
          .catch(error => {
            console.error('移除成员失败', error);
            ElMessage.error('移除成员失败');
          })
          .finally(() => {
            this.loading = false;
          });
      }).catch(() => {});
    },
    isExistingMember(id) {
      return this.memberList.some(member => member.id === id);
    },
    formatTimestamp(timestamp) {
      if (!timestamp) return '-';
      const date = new Date(timestamp * 1000);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    }
  }
};
</script>

<style scoped lang="scss">
.permission-dialog-content {
  max-height: 500px;
  overflow-y: auto;
  
  .content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .el-input {
      max-width: 300px;
    }
  }
  
  .member-list {
    margin-bottom: 24px;
  }
  
  .empty-tip {
    margin: 48px 0;
    display: flex;
    justify-content: center;
  }
}

.member-dialog-content {
  max-height: 500px;
  overflow-y: auto;
}

.selected-members-container {
  margin-top: 20px;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.selected-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.selected-members-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.selected-member-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 10px;
  background-color: #f0f2f5;
  border-radius: 4px;
  font-size: 14px;
  min-width: 120px;
}

.selected-member-info {
  display: flex;
  align-items: center;
}

.selected-member-avatar {
  margin-right: 8px;
}

.selected-member-name {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-selected {
  cursor: pointer;
  color: #909399;
  margin-left: 5px;
}

.remove-selected:hover {
  color: #f56c6c;
}
</style>
