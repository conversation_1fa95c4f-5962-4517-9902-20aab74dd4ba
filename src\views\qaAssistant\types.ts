export interface IMessage {
  answer?: string;
  rawAnswer?: string;
  conversation_id: string;
  created_at?: number;
  files?: any[];
  id?: string;
  inputs?: {};
  parent_message_id?: null | string;
  query: string;
  response_mode?: string;
  status?: string;
  message_files?: any;
  [key: string]: unknown;
}

export interface IConversation {
  id: string;
  inputs: {};
  introduction: string;
  name: string;
  status: string;
  created_at: number;
  updated_at: number;
  pinned?: boolean;
}

export interface IConversationResponse {
  data: IConversation[];
  has_more: boolean;
  limit: number;
}

export interface IMessageResponse {
  data: IMessage[];
  has_more: boolean;
  limit: number;
}

export interface ChatParams {
  conversation_id: string;
  parent_message_id: string | null;
  files: string[];
  inputs: Record<string, unknown>;
  response_mode: string;
  query: string;
  dataset_ids?: string[];
  [key: string]: unknown;
}

export interface IDataset {
  id: string;
  name: string;
  description?: string;
  created_at?: number;
  updated_at?: number;
  status?: string;
}

export interface IDatasetResponse {
  data: IDataset[];
  has_more: boolean;
  limit: number;
}
