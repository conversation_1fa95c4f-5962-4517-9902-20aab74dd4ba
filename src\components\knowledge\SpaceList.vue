<template>
  <div class="space-sidebar">
    <div class="space-header">
      <h3 class="space-title">知识空间</h3>
    </div>
    
    <div class="space-search">
      <el-input
        v-model="keyword"
        placeholder="搜索空间名称"
        :prefix-icon="Search"
        size="small"
        clearable
        @clear="handleSearch"
        @input="handleSearch"
      />
    </div>
    
    <div class="space-list">
      <div 
        v-for="space in spaceList" 
        :key="space.id" 
        class="space-item"
        :class="{ active: activeSpaceId === space.id }"
      >
        <div class="space-content">
          <el-icon><UserFilled /></el-icon>
          <span class="space-name" @click="handleSpaceClick(space)">{{ space.name }}</span>
          <!-- <span class="space-count">{{ space.memberCount }}人</span> -->
        </div>
        <div class="space-actions-menu">
          <el-dropdown trigger="hover" placement="bottom-end" @command="(command) => handleCommand(command, space)">
            <span class="action-trigger">
              <i class="el-icon-more">⋮</i>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="view">管理</el-dropdown-item>
                <el-dropdown-item command="edit">编辑</el-dropdown-item>
                <el-dropdown-item command="delete" divided>删除空间</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
    
    <div class="space-actions">
      <el-button 
        class="create-space-btn" 
        type="primary" 
        @click="handleCreateSpace"
      >
        <el-icon><Plus /></el-icon>
        新建知识空间
      </el-button>
    </div>
    
    <!-- 使用SpaceEdit组件替代内嵌的对话框 -->
    <space-edit
      v-model:visible="createDialogVisible"
      :space-data="createForm"
      :is-create="true"
      @save="handleSpaceSave"
    />
    
    <!-- 编辑知识空间 -->
    <space-edit
      v-model:visible="editDialogVisible"
      :space-data="editForm"
      :is-create="false"
      @save="handleEditSave"
    />
  </div>
</template>

<script>
import { Search, Plus, UserFilled } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import SpaceEdit from '@/components/knowledge/SpaceEdit.vue';
import { 
  fetchSpaceList, 
  createSpace, 
  updateSpace, 
  deleteSpace, 
  getSpaceDetail 
} from '@/api/knowledge/space';

export default {
  components: {
    SpaceEdit
  },
  name: 'SpaceList',
  data() {
    return {
      keyword: '',
      spaceList: [],
      loading: false,
      createDialogVisible: false,
      createForm: {
        name: '',
        description: ''
      },
      editDialogVisible: false,
      editForm: {
        id: '',
        name: '',
        description: ''
      }
    };
  },
  computed: {
    activeSpaceId() {
      return this.$route.query.spaceId || '';
    }
  },
  methods: {
    handleSearch() {
      this.getSpaceList();
    },
    handleSpaceClick(space) {
      this.$router.push({
        path: '/knowledge/space/' + space.id,
        query: {
          spaceName: space.name,
          spaceId: space.id
        }
      });
    },
    getSpaceList() {
      this.loading = true;
      fetchSpaceList({
        keyword: this.keyword
      })
        .then(res => {
          this.spaceList = res.data.map(item => ({
            id: item.id,
            name: item.name,
            description: item.description,
            logo: item.logo,
            memberCount: 0, // 暂时没有成员数量字段
            createdAt: item.created_at
          }));
        })
        .catch(error => {
          console.error('获取知识空间列表失败', error);
          ElMessage.error('获取知识空间列表失败');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleCommand(command, space) {
      switch (command) {
        case 'view':
          // 跳转到空间管理页面
          this.$router.push(`/knowledge/space?id=${space.id}`);
          break;
        case 'delete':
          this.handleDeleteSpace(space);
          break;
        case 'edit':
          this.handleEditSpace(space);
          break;
      }
    },
    handleDeleteSpace(space) {
      ElMessageBox.confirm(
        `确定要删除"${space.name}"空间吗？此操作将删除该空间下的所有知识库。`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        this.loading = true;
        deleteSpace(space.id)
          .then(() => {
            ElMessage.success(`已删除空间: ${space.name}`);
            this.getSpaceList(); // 刷新列表
          })
          .catch(error => {
            console.error('删除空间失败', error);
            ElMessage.error('删除空间失败');
          })
          .finally(() => {
            this.loading = false;
          });
      }).catch(() => {
        // 用户取消操作
      });
    },
    handleCreateSpace() {
      this.createDialogVisible = true;
      this.createForm = {
        name: '',
        description: ''
      };
    },
    handleSpaceSave(data) {
      this.loading = true;
      createSpace({
        name: data.name,
        description: data.description || '',
        logo: data.logo || ''
      })
        .then(res => {
          ElMessage.success('创建知识空间成功');
          this.createDialogVisible = false;
          this.getSpaceList(); // 刷新列表
        })
        .catch(error => {
          console.error('创建知识空间失败', error);
          ElMessage.error('创建知识空间失败');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleEditSpace(space) {
      this.loading = true;
      // 先获取空间详情，以便编辑时显示完整信息
      getSpaceDetail(space.id)
        .then(res => {
          this.editDialogVisible = true;
          this.editForm = {
            id: res.id,
            name: res.name,
            description: res.description || '',
            logo: res.logo || ''
          };
        })
        .catch(error => {
          console.error('获取空间详情失败', error);
          ElMessage.error('获取空间详情失败');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleEditSave(data) {
      this.loading = true;
      updateSpace(data.id, {
        name: data.name,
        description: data.description || '',
        logo: data.logo || ''
      })
        .then(res => {
          ElMessage.success('编辑知识空间成功');
          this.editDialogVisible = false;
          this.getSpaceList(); // 刷新列表
        })
        .catch(error => {
          console.error('编辑知识空间失败', error);
          ElMessage.error('编辑知识空间失败');
        })
        .finally(() => {
          this.loading = false;
        });
    }
  },
  mounted() {
    this.getSpaceList();
  }
};
</script>

<style scoped lang="scss">
.space-sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-bottom: 16px;
  
  .space-header {
    padding: 8px 16px;
    
    .space-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }
  
  .space-search {
    padding: 0 16px 16px;
  }
  
  .space-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 8px;
    
    .space-item {
      padding: 8px 12px;
      margin-bottom: 4px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .space-content {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
        overflow: hidden;
        
        .el-icon {
          color: #409eff;
          flex-shrink: 0;
        }
        
        .space-name {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 14px;
          cursor: pointer;
        }
        
        .space-count {
          font-size: 12px;
          color: #909399;
          margin-left: 4px;
          flex-shrink: 0;
        }
      }
      
      .space-actions-menu {
        margin-left: 8px;
        
        .action-trigger {
          cursor: pointer;
          color: #909399;
          font-size: 16px;
          padding: 4px;
          
          &:hover {
            color: #409eff;
          }
          
          .el-icon-more {
            font-size: 20px;
            font-weight: bold;
            display: inline-block;
            vertical-align: middle;
            line-height: 1;
            
            &:hover {
              color: #409eff;
            }
          }
        }
      }
      
      &:hover {
        background-color: #f5f7fa;
      }
      
      &.active {
        background-color: #ecf5ff;
        
        .space-name {
          color: #409eff;
        }
      }
    }
  }
  
  .space-actions {
    padding: 16px;
    margin-top: auto;
    
    .create-space-btn {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 4px;
    }
  }
}
</style>
