<template>
  <div class="document-detail-container">
    <div class="document-detail-header">
      <el-button @click="goBack" :icon="ArrowLeft">返回</el-button>
      <div class="document-title">
        {{ documentName || '文档详情' }}
        <span class="document-segment-count" v-if="totalCount > 0">({{ totalCount }} 段落)</span>
      </div>
    </div>

    <div class="document-detail-content">
      <div class="segments-list-container">
        <!-- 搜索和筛选区域 -->
        <div class="segments-header">
          <div class="filters-container">
            <div class="search-container">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索段落"
                prefix-icon="Search"
                clearable
                @input="handleSearch"
              />
            </div>
            <div class="filter-container">
              <el-select 
                v-model="selectedStatus" 
                placeholder="状态"
                style="width: 120px"
                @change="handleStatusChange"
              >
                <el-option value="all" label="全部" />
                <el-option :value="true" label="可用" />
                <el-option :value="false" label="禁用" />
              </el-select>
            </div>
            <div class="expand-button">
              <el-button type="primary" size="small" plain @click="toggleExpandAll">
                {{ isExpandAll ? '收起分段' : '展开分段' }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 段落列表 -->
        <div class="segments-list" v-loading="loading">
          <div v-for="(segment, index) in segmentsList" :key="segment.id" class="segment-item">
            <div class="segment-header">
              <div class="segment-info">
                <span class="segment-id">段分段-{{ String(index + 1).padStart(2, '0') }}</span>
                <span class="segment-stats">{{ segment.tokens || 0 }} 字符</span>
                <span class="segment-stats">{{ segment.hit_count || 0 }} 召回次数</span>
                <el-tag size="small" type="info">已索引</el-tag>
              </div>
              <div class="segment-status">
                <el-tag 
                  :type="segment.enabled ? 'success' : 'info'" 
                  size="small"
                >
                  {{ segment.enabled ? '已启用' : '已禁用' }}
                </el-tag>
              </div>
            </div>
            
            <div class="segment-content" :class="{ 'expanded': isExpandAll }">
              {{ segment.content }}
            </div>
            
            <div class="segment-tags" v-if="segment.tags && segment.tags.length">
              <el-tag 
                v-for="tag in segment.tags" 
                :key="tag" 
                size="small" 
                class="segment-tag"
                effect="plain"
              >
                #{{ tag }}
              </el-tag>
            </div>
            <div class="segment-tags" v-else>
              <!-- 模拟一些标签 -->
              <el-tag 
                v-for="tag in getMockTags(segment)" 
                :key="tag" 
                size="small" 
                class="segment-tag"
                effect="plain"
              >
                #{{ tag }}
              </el-tag>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="segmentsList.length === 0 && !loading" class="empty-state">
            <el-empty description="没有找到相关段落" />
          </div>
        </div>

        <!-- 分页 -->
        <div class="segments-pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
            background
          />
        </div>
      </div>

      <!-- 元数据侧边栏 -->
      <div class="metadata-sidebar">
        <div v-if="documentLoading" class="metadata-loading">
          <el-skeleton :rows="10" animated />
        </div>
        <template v-else>
          <!-- 元数据标题和描述 -->
          <div class="metadata-header">
            <h2 class="metadata-title">元数据</h2>
            <p class="metadata-description">标记文档的元数据允许 AI 及时访问它们并为用户公开参考来源。</p>
          </div>
          
          <!-- 基本信息部分 -->
          <div class="metadata-content">
            <div class="metadata-item">
              <div class="metadata-label">原始文件名称</div>
              <div class="metadata-value">{{ documentName }}</div>
            </div>
            <div class="metadata-item">
              <div class="metadata-label">原始文件大小</div>
              <div class="metadata-value">{{ documentMeta.fileSize || '1.47MB' }}</div>
            </div>
            <div class="metadata-item">
              <div class="metadata-label">上传日期</div>
              <div class="metadata-value">{{ formatDate(documentMeta.created_at) }}</div>
            </div>
            <div class="metadata-item">
              <div class="metadata-label">最后更新日期</div>
              <div class="metadata-value">{{ formatDate(documentMeta.updated_at) }}</div>
            </div>
            <div class="metadata-item">
              <div class="metadata-label">来源</div>
              <div class="metadata-value">{{ documentMeta.source || '文件上传' }}</div>
            </div>
            
            <!-- 分割线 -->
            <div class="metadata-divider"></div>
            
            <!-- 技术参数部分 -->
            <div class="metadata-section-title">技术参数</div>
            <div class="metadata-item">
              <div class="metadata-label">分段规则</div>
              <div class="metadata-value">{{ documentMeta.doc_form || '自定义' }}</div>
            </div>
            <div class="metadata-item">
              <div class="metadata-label">段落长度</div>
              <div class="metadata-value">{{ documentMeta.max_segment_length || '500' }}</div>
            </div>
            <div class="metadata-item">
              <div class="metadata-label">平均段落长度</div>
              <div class="metadata-value">{{ documentMeta.avg_segment_length || '303 characters' }}</div>
            </div>
            <div class="metadata-item">
              <div class="metadata-label">段落数量</div>
              <div class="metadata-value">{{ totalCount }} paragraphs</div>
            </div>
            <div class="metadata-item">
              <div class="metadata-label">召回次数</div>
              <div class="metadata-value">{{ documentMeta.hit_rate || '0.00%' }} ({{ documentMeta.hit_count || '0' }}/{{ totalCount }})</div>
            </div>
            <div class="metadata-item">
              <div class="metadata-label">嵌入时间</div>
              <div class="metadata-value">{{ documentMeta.embedding_time || '1.65 sec' }}</div>
            </div>
            <div class="metadata-item">
              <div class="metadata-label">嵌入花费</div>
              <div class="metadata-value">{{ documentMeta.embedding_tokens || '94,166 tokens' }}</div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ArrowLeft } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { fetchDocumentSegments, fetchDocumentDetail, DocumentDetail } from '@/api/knowledge';

const router = useRouter();
const route = useRoute();

// 文档ID和知识库ID
const knowledgeId = ref('');
const documentId = ref('');
const documentName = ref('');

// 文档元数据
const documentMeta = ref<Partial<DocumentDetail>>({
  fileSize: '1.47MB',
  created_at: Date.now() / 1000 - 86400, // 昨天
  updated_at: Date.now() / 1000,          // 现在
  source: '文件上传',
  doc_form: '自定义',
  max_segment_length: 500,
  avg_segment_length: '303 characters',
  hit_rate: '0.00%',
  hit_count: 0,
  embedding_time: '1.65 sec',
  embedding_tokens: '94,166 tokens',
  indexing_status: 'available'
});

// 段落列表相关
const loading = ref(false);
const documentLoading = ref(false);
const segmentsList = ref<any[]>([]);
const searchKeyword = ref('');
const selectedStatus = ref<'all' | boolean>('all');
const currentPage = ref(1);
const pageSize = ref(10);
const totalCount = ref(0);
const isExpandAll = ref(false);

// 常见标签库 - 用于模拟标签数据
const commonTags = [
  'Java', '软件', '知识库', '开发', '算法', '数据库', 
  '设计', '架构', '开发手册', '代码', '规范', '测试',
  '技术', 'MySQL', '性能测试', '约束', '函数', '模块',
  '安全', '实现方式', '说明', '手册', '反例', '正例',
  '软件架构', '不做万能', '协同', '规则', '代码质量', 
  'IDE', '编译', '系统'
];

// 获取模拟标签 - 根据内容生成一些相关标签
const getMockTags = (segment: any) => {
  // 实际应用中，这里应该是从后端获取的标签
  // 这里我们模拟一些标签，从共同标签库中随机选择3-6个
  let tags = [];
  
  // 先尝试从内容中提取关键词作为标签
  for (const tag of commonTags) {
    if (segment.content && segment.content.includes(tag) && tags.length < 4) {
      tags.push(tag);
    }
  }
  
  // 如果标签不足，随机补充一些
  if (tags.length < 3) {
    const randomTags = [...commonTags]
      .sort(() => 0.5 - Math.random())
      .slice(0, 6 - tags.length);
    tags = [...tags, ...randomTags];
  }
  
  return tags.slice(0, Math.floor(Math.random() * 4) + 3); // 返回3-6个标签
};

// 格式化日期
const formatDate = (timestamp: number | undefined) => {
  if (!timestamp) return '未知';
  
  const date = new Date(timestamp * 1000); // 转换为毫秒
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 获取文档详情
const fetchDocumentInfo = async () => {
  if (!knowledgeId.value || !documentId.value) return;
  
  documentLoading.value = true;
  try {
    const response = await fetchDocumentDetail(knowledgeId.value, documentId.value);
    
    if (response && response.data) {
      // 设置文档名称
      documentName.value = response.data.name || `文档详情 (ID: ${documentId.value})`;
      
      // 设置文档元数据
      documentMeta.value = {
        ...documentMeta.value,
        ...response.data,
        // 格式化一些数据，确保显示一致
        hit_rate: typeof response.data.hit_count === 'number' && response.data.segment_count 
          ? `${((response.data.hit_count / response.data.segment_count) * 100).toFixed(2)}%` 
          : '0.00%',
        avg_segment_length: response.data.avg_segment_length || '303 characters'
      };
    }
  } catch (error) {
    console.error('获取文档详情失败:', error);
    ElMessage.error('获取文档详情失败');
  } finally {
    documentLoading.value = false;
  }
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 展开/收起所有段落
const toggleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value;
};

// 获取段落列表
const fetchSegmentsList = async () => {
  if (!knowledgeId.value || !documentId.value) return;
  
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      keyword: searchKeyword.value,
      enabled: selectedStatus.value
    };
    
    const response = await fetchDocumentSegments(knowledgeId.value, documentId.value, params);
    
    // 直接赋值
    segmentsList.value = response.data || [];
    totalCount.value = response.total || 0;
  } catch (error) {
    console.error('获取段落列表失败:', error);
    ElMessage.error('获取段落列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  currentPage.value = 1;
  fetchSegmentsList();
};

// 状态筛选
const handleStatusChange = () => {
  currentPage.value = 1;
  fetchSegmentsList();
};

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  fetchSegmentsList();
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchSegmentsList();
};

// 在组件挂载时获取数据
onMounted(() => {
  console.log('DocumentDetail mounted, params:', route.params);
  console.log('DocumentDetail mounted, query:', route.query);
  
  if (route.params.knowledgeId && route.params.documentId) {
    knowledgeId.value = route.params.knowledgeId as string;
    documentId.value = route.params.documentId as string;
    
    // 可以从路由中获取文档名称，如果有的话
    documentName.value = route.query.name as string || `文档详情 (ID: ${documentId.value})`;
    
    // 获取文档元数据
    fetchDocumentInfo();
    
    // 获取段落列表
    fetchSegmentsList();
  } else {
    console.error('缺少必要参数:', route.params);
    ElMessage.error('缺少必要的参数');
    goBack();
  }
});

// 监听路由变化，当路由参数变化时重新加载
watch(
  () => route.params,
  (newParams: { [key: string]: any }) => {
    console.log('Route params changed:', newParams);
    
    if (newParams.knowledgeId && newParams.documentId) {
      knowledgeId.value = newParams.knowledgeId as string;
      documentId.value = newParams.documentId as string;
      
      // 可以从路由中获取文档名称，如果有的话
      documentName.value = route.query.name as string || `文档详情 (ID: ${documentId.value})`;
      
      // 获取文档元数据
      fetchDocumentInfo();
      
      // 获取段落列表
      fetchSegmentsList();
    }
  },
  { deep: true }
);

// 监听查询参数变化
watch(
  () => route.query,
  (newQuery: { [key: string]: any }) => {
    console.log('Route query changed:', newQuery);
    // 如果是时间戳变化，刷新数据
    if (newQuery.t && knowledgeId.value && documentId.value) {
      // 重新获取数据
      fetchDocumentInfo();
      fetchSegmentsList();
    }
  },
  { deep: true }
);
</script>

<style scoped>
/* 页面整体布局 */
.document-detail-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px);
  background-color: #f0f2f5;
}

.document-detail-header {
  padding: 16px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 12px;
}

.document-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.document-segment-count {
  font-size: 14px;
  font-weight: normal;
  color: #909399;
}

.document-detail-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.segments-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: calc(100vh - 170px);
  background: #fff;
}

/* 搜索和筛选区域样式 */
.segments-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 16px;
}

.filters-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-container {
  width: 300px;
}

/* 段落列表样式 */
.segments-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.segment-item {
  padding: 16px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.segment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px dashed #dcdfe6;
  padding-bottom: 12px;
}

.segment-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.segment-id {
  font-weight: 500;
  color: #606266;
}

.segment-stats {
  color: #909399;
  font-size: 13px;
}

.segment-status {
  margin-left: auto;
}

.segment-content {
  white-space: pre-line;
  line-height: 1.6;
  color: #303133;
  font-size: 14px;
  padding: 0 8px;
  max-height: 3.2em; /* 两行的高度 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 限制为两行 */
  line-clamp: 2;
  -webkit-box-orient: vertical;
  transition: max-height 0.3s ease, -webkit-line-clamp 0.3s ease;
}

.segment-content.expanded {
  max-height: none;
  -webkit-line-clamp: unset;
  line-clamp: unset;
}

.segment-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 0 8px;
}

.segment-tag {
  cursor: pointer;
}

.segments-pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 16px 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  padding: 40px 0;
  color: #909399;
}

/* 元数据侧边栏样式 */
.metadata-sidebar {
  border-left: 1px solid #e4e7ed;
  padding: 20px;
  background-color: #fff;
  height: calc(100vh - 170px);
  overflow-y: auto;
  width: 350px;
}

.metadata-header {
  margin-bottom: 20px;
}

.metadata-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.metadata-description {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  margin: 0;
}

.metadata-content {
  background-color: #fff;
}

.metadata-section-title {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin: 16px 0;
}

.metadata-item {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  font-size: 14px;
  line-height: 20px;
}

.metadata-label {
  color: #606266;
  flex-shrink: 0;
}

.metadata-value {
  color: #333;
  word-break: break-all;
  text-align: right;
  max-width: 60%;
}

.metadata-divider {
  height: 1px;
  background-color: #ebeef5;
  margin: 16px 0;
}

.metadata-loading {
  padding: 20px;
}
</style>
