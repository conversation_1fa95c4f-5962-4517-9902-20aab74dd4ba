{"root": ["./src/env.d.ts", "./src/main.ts", "./src/vite-env.d.ts", "./src/api/agents/index.ts", "./src/api/chatbot/index.ts", "./src/api/knowledge/document.ts", "./src/api/knowledge/index.ts", "./src/api/knowledge/space.ts", "./src/config/menu.ts", "./src/enums/storage.ts", "./src/plugins/svgicon.ts", "./src/router/index.ts", "./src/types/auth.ts", "./src/types/common.ts", "./src/types/menu.ts", "./src/types/shims-vue.d.ts", "./src/utils/chatbot.ts", "./src/utils/request.ts", "./src/utils/storage.ts", "./src/utils/svgicons.ts", "./src/utils/svgprocessor.ts", "./src/utils/time.ts", "./src/utils/user.ts", "./src/views/agents/types.ts", "./src/views/chatbot/utils/markdown.ts", "./src/views/home/<USER>", "./src/views/qaassistant/types.ts", "./src/views/system/models/types.ts", "./src/views/system/structure/types.ts", "./src/views/toolbox/workflow/types.ts", "./src/app.vue", "./src/components/avatar.vue", "./src/components/svgicon.vue", "./src/components/knowledge/documentpermission.vue", "./src/components/knowledge/spaceedit.vue", "./src/components/knowledge/spacelist.vue", "./src/components/knowledge/spacemember.vue", "./src/layouts/blank.vue", "./src/layouts/index.vue", "./src/layouts/components/header.vue", "./src/layouts/components/sidebar.vue", "./src/views/activate.vue", "./src/views/login.vue", "./src/views/agents/chat.vue", "./src/views/agents/create.vue", "./src/views/agents/index.vue", "./src/views/chatbot/embedexample.vue", "./src/views/chatbot/index.vue", "./src/views/chatbot/components/chatmessages.vue", "./src/views/chatbot/components/chatbotprovider.vue", "./src/views/chatbot/components/configpanel.vue", "./src/views/chatbot/components/embeddedchatbot.vue", "./src/views/chatbot/components/messageinput.vue", "./src/views/home/<USER>", "./src/views/knowledge/create.vue", "./src/views/knowledge/detail.vue", "./src/views/knowledge/documentdetail.vue", "./src/views/knowledge/edit.vue", "./src/views/knowledge/index.vue", "./src/views/knowledge/space.vue", "./src/views/knowledge/components/knowledgecard.vue", "./src/views/knowledge/components/tagfilter.vue", "./src/views/qaassistant/index.vue", "./src/views/qaassistant/components/renamedialog.vue", "./src/views/system/datasources/index.vue", "./src/views/system/models/index.vue", "./src/views/system/models/components/configdrawer.vue", "./src/views/system/models/components/installdialog.vue", "./src/views/system/models/components/modeldrawer.vue", "./src/views/system/models/components/modelsettingspopover.vue", "./src/views/system/models/components/showmodeldialog.vue", "./src/views/system/structure/index.vue", "./src/views/system/structure/components/memberadd.vue", "./src/views/system/structure/components/organizationdialog.vue", "./src/views/toolbox/chatflow/index.vue", "./src/views/toolbox/workflow/index.vue", "./src/views/toolbox/workflow/components/createworkflowdialog.vue", "./src/views/toolbox/workflow/components/detail.vue", "./src/views/toolbox/workflow/components/flowlist.vue"], "errors": true, "version": "5.7.3"}