<template>
  <el-dialog v-model="dialogVisible" title="" width="500" @close="handleClose" @open="initOpen">
    <template v-if="showSuccess">
      <div class="success-title">邀请已发送</div>
      <div class="success-text">邀请已发送，对方登录后即可访问你的团队数据。</div>
      <div class="success-header">邀请链接</div>
      <el-input v-model="successUrl" readonly />
    </template>
    <template v-else>
      <div class="dialog-title">添加团队成员</div>
      <div class="mb-10">对方在登录后可以访问你的团队数据。</div>
      <el-form :model="form" :rules="rules" ref="formRef" label-position="top">
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="输入邮箱" type="textarea" :rows="6" />
        </el-form-item>
        <el-form-item label="" prop="role">
          <el-popover ref="popverRef" placement="bottom" :width="310" trigger="click">
            <ul class="role-list">
              <li class="role-item" v-for="item in RoleList" :key="item.role" @click="selectRole(item)">
                <el-icon color="#1890ff" :class="[form.role == item.role ? 'is-show' : 'un-show']"><Check /></el-icon>
                <div class="role-item-right">
                  <div class="role-title">{{ item.title }}</div>
                  <div class="role-text">{{ item.text }}</div>
                </div>
              </li>
            </ul>
            <template #reference>
              <div class="role-button">
                <el-button type="info" block>
                  邀请为{{ RoleEnum[form.role as keyof typeof RoleEnum] }}用户
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
              </div>
            </template>
          </el-popover>
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <div class="dialog-footer" v-if="!showSuccess">
        <el-button class="footer-button" type="primary" @click="handleSubmit" size="large" :disabled="!form.email"
          >发送邀请</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import type { FormInstance } from "element-plus";
import { Check, ArrowDown } from "@element-plus/icons-vue";
import { RoleEnum } from "../types";
import { post } from "@/utils/request";
import type { InviteRsp } from "../types";
import { ElMessage } from "element-plus";

// 定义props
const props = defineProps<{
  visible: boolean;
  organizationId?: string | null; // 添加组织ID属性
}>();

// 定义emit
const emit = defineEmits<{
  "update:visible": [value: boolean];
  refresh: [];
}>();

// 表单ref
const formRef = ref<FormInstance>();
// popver ref
const popverRef = ref();

// 是否显示成功
let showSuccess = ref(false);
let successUrl = ref("");

// 计算属性：控制弹窗显示隐藏
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit("update:visible", value),
});

// 初始化弹窗
const initOpen = () => {
  formRef.value?.resetFields();
  popverRef.value.hide();
};

const RoleList = [
  {
    title: "成员",
    text: "只能使用应用程序，不能建立应用程序",
    role: "normal",
  },
  {
    title: "编辑",
    text: "能够建立并编辑应用程序，不能管理团队设置",
    role: "editor",
  },
  {
    title: "管理员",
    text: "能够建立应用程序和管理团队设置",
    role: "admin",
  },
];

// 表单数据
const form = reactive({
  email: "",
  role: "normal",
});

// 表单验证规则
const rules = {
  email: [{ required: true, message: "请输入邮箱", trigger: "blur" }],
  role: [{ required: true, message: "请选择角色", trigger: "change" }],
};

//选择角色
const selectRole = (item: any) => {
  form.role = item.role;
  popverRef.value.hide();
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  // 重置表单
  Object.assign(form, {
    email: "",
    role: "normal",
  });
  successUrl.value = "";
  showSuccess.value = false;
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (valid) {
      // 准备请求参数
      const requestData: any = {
        emails: [form.email],
        language: "zh-Hans",
        role: form.role,
      };

      // 如果有组织ID，则添加到请求参数中
      if (props.organizationId) {
        requestData.organization_id = props.organizationId;
      }

      let res: InviteRsp = await post("/workspaces/current/members/lg-invite-email", requestData);
      if (res.result == "success" && res.invitation_results[0].status != "failed") {
        showSuccess.value = true;
        successUrl.value = `${import.meta.env.VITE_API_WEB_URL}${
          res.invitation_results[0].url == "/signin" ? "/login" : res.invitation_results[0].url
        }`;
        ElMessage.success("添加成功");
        emit("refresh");
      } else if (res.invitation_results[0].status == "failed") {
        ElMessage.error(res.invitation_results[0]?.message);
      }
    }
  });
};
</script>

<style scoped lang="scss">
.dialog-footer {
  width: 100%;
  :deep(.footer-button) {
    width: 100%;
  }
}
.role-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  padding: 6px;
  cursor: pointer;
  &:hover {
    background-color: #f5f7f9;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .un-show {
    opacity: 0;
  }
  .is-show {
    opacity: 1;
  }
  .role-item-right {
    margin-left: 10px;
    .role-title {
      font-size: 14px;
      font-weight: 600;
    }
    .role-text {
      font-size: 12px;
      color: #676f83;
    }
  }
}
.role-button {
  width: 100%;
  :deep(.el-button) {
    width: 100%;
  }
}
.mb-10 {
  margin-bottom: 10px;
}

.dialog-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
}

.success-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 10px;
}

.success-header {
  margin: 40px 0 20px 0;
}
</style>
