<template>
  <el-dialog
    v-model="dialogVisible"
    title="安装插件"
    width="560"
    @close="handleClose"
    align-center
    :close-on-click-modal="!installing"
    :close-on-press-escape="!installing"
    :show-close="!installing"
  >
    <div
      v-loading="installing"
      element-loading-text="正在安装插件..."
      element-loading-background="rgba(255, 255, 255, 0.9)"
    >
      <div class="install-desc">即将安装以下插件</div>
      <div class="install-card">
        <div class="install-header">
          <img
            class="header-img"
            :src="`https://marketplace.dify.ai/api/v1/plugins/langgenius/${plugin?.model?.provider}/icon`"
            alt="logo"
          />
          <div class="header-box">
            <div class="header-title">
              {{ plugin?.label?.zh_Hans }}
              <el-icon class="verified-icon" color="#4080FF"><CircleCheckFilled /></el-icon>
              <span class="version">{{ plugin?.latest_version }}</span>
            </div>
            <div class="header-provider">{{ plugin?.model?.provider }}</div>
          </div>
        </div>
        <div class="install-desc-box">
          {{ plugin?.model?.description?.zh_Hans }}
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="installing">取消</el-button>
        <el-button type="primary" @click="handleInstall" :loading="installing">安装</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { CircleCheckFilled } from "@element-plus/icons-vue";
import type { PluginRecord } from "../types";
import { ElMessage } from "element-plus";
import { post } from "@/utils/request";

defineOptions({
  name: "InstallDialog",
});

// Props 定义
const props = defineProps<{
  visible: boolean;
  plugin: PluginRecord | null;
}>();

// Emits 定义
const emit = defineEmits<{
  "update:visible": [value: boolean];
  success: [];
}>();

// 计算属性：控制弹窗显示隐藏
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit("update:visible", value),
});

// 安装中状态
const installing = ref(false);

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
};

// 安装插件
const handleInstall = async () => {
  if (!props.plugin) return;
  installing.value = true;
  try {
    // 这里添加实际的安装逻辑
    await post("/workspaces/current/plugin/install/marketplace", {
      plugin_unique_identifiers: [props.plugin?.latest_package_identifier],
    });
    ElMessage.success("插件安装中，请稍后刷新查看");
    emit("success");
    handleClose();
  } catch (error) {
    ElMessage.error("安装插件失败，请重试");
  } finally {
    installing.value = false;
  }
};
</script>

<style scoped lang="scss">
:deep(.el-dialog) {
  --el-dialog-padding-primary: 24px;
  border-radius: 24px;
  margin-top: 15vh !important;
  overflow: hidden;

  .el-dialog__header {
    margin: 0;
    padding: 24px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    margin-right: 0;
    background-color: #fafafa;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }
  }

  .el-dialog__body {
    padding: 24px;
  }

  .el-dialog__footer {
    padding: 20px 24px;
    border-top: 1px solid var(--el-border-color-lighter);
    background-color: #fafafa;
  }
}

.install-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

.install-card {
  background-color: var(--el-fill-color-blank);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }
}

.install-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;

  .header-img {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    margin-right: 16px;
  }

  .header-box {
    flex: 1;

    .header-title {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 6px;

      .verified-icon {
        margin: 0 6px;
      }

      .version {
        font-size: 12px;
        color: #666;
        font-weight: normal;
        border: 1px solid #e5e7eb;
        padding: 3px 6px;
        border-radius: 4px;
      }
    }

    .header-provider {
      font-size: 13px;
      color: #666;
    }
  }
}

.install-desc-box {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  word-break: break-all;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  :deep(.el-button) {
    min-width: 88px;
    margin: 0;
    padding: 10px 20px;
    font-size: 14px;
  }
}

:deep(.el-loading-spinner) {
  .el-loading-text {
    color: var(--el-color-primary);
    font-size: 14px;
    margin-top: 8px;
  }
}

.el-dialog__body {
  position: relative;
  min-height: 200px;
}
</style>
