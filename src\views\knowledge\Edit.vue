<template>
  <div class="edit-knowledge-container">
    <div class="header">
      <div class="back" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
      </div>
      <div class="title">知识库设置</div>
    </div>

    <!-- 左侧标签栏和右侧内容区域 -->
    <div class="edit-content">
      <!-- 左侧标签栏 -->
      <div class="tab-sidebar">
        <div
          class="tab-item"
          :class="{ active: activeTab === 'documents' }"
          @click="activeTab = 'documents'"
        >
          文档
        </div>
        <div
          class="tab-item"
          :class="{ active: activeTab === 'settings' }"
          @click="activeTab = 'settings'"
        >
          知识库设置
        </div>
        <div
          class="tab-item"
          :class="{ active: activeTab === 'permission' }"
          @click="activeTab = 'permission'"
        >
          权限管理
        </div>
        <div
          class="tab-item"
          :class="{ active: activeTab === 'test' }"
          @click="activeTab = 'test'"
        >
        检索测试
        </div>


      </div>

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 文档管理内容 -->
        <div v-if="activeTab == 'documents'" class="documents-content">
          <div class="documents-header">
            <div class="search-container">
              <el-input
                v-model="documentsSearchKeyword"
                placeholder="搜索文档"
                prefix-icon="Search"
                clearable
                @input="handleDocumentsSearch"
              />
            </div>
            <el-button type="primary" @click="handleAddDocument">
              <el-icon><Plus /></el-icon>添加文档
            </el-button>
          </div>

          <div class="documents-table-container">
            <el-table
              v-loading="documentsLoading"
              :data="documentsList"
              style="width: 100%"
              border
              height="calc(100vh - 450px)"
            >
              <el-table-column label="文档名称" prop="name" min-width="200">
                <template #default="{ row }">
                  <div class="document-name-cell" @click="handleDocumentClick(row)" style="cursor: pointer;">
                    <el-icon><Document /></el-icon>
                    <span>{{ row.name }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="分段模式" width="120">
                <template #default="{ row }">
                  <span>{{ getDocForm(row.doc_form) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="字符数" width="100">
                <template #default="{ row }">
                  <span>{{ row.tokens || 0 }}</span>
                </template>
              </el-table-column>
              <el-table-column label="召回次数" width="100">
                <template #default="{ row }">
                  <span>{{ row.hit_count || 0 }}</span>
                </template>
              </el-table-column>
              <el-table-column label="上传时间" width="180">
                <template #default="{ row }">
                  {{ formatTimestamp(row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column label="状态" width="120">
                <template #default="{ row }">
                  <el-tag
                    :type="getDocumentStatusType(row.display_status)"
                    size="small"
                  >
                    {{ getDocumentStatusText(row.display_status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="250">
                <template #default="{ row }">
                  <div class="operation-buttons">
                    <el-button
                      type="primary"
                      size="small"
                      link
                      @click.stop="handlePermissionSetting(row)"
                    >
                      权限设置
                    </el-button>
                    <el-button
                      type="primary"
                      size="small"
                      link
                      @click.stop="handleRenameDocument(row)"
                    >
                      重命名
                    </el-button>
                    <el-button
                      type="primary"
                      size="small"
                      link
                      @click.stop="handleArchiveDocument(row)"
                    >
                      {{ row.archived ? '撤销归档' : '归档' }}
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      link
                      @click.stop="handleDeleteDocument(row)"
                    >
                      删除
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="documents-pagination" style="position: absolute; bottom: 20px; right: 20px;">
            <el-pagination
              v-model:current-page="documentsCurrentPage"
              v-model:page-size="documentsPageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="documentsTotalCount"
              @size-change="handleDocumentsSizeChange"
              @current-change="handleDocumentsPageChange"
            />
          </div>
        </div>

        <!-- 知识库设置内容 -->
        <div v-if="activeTab == 'settings'" class="settings-content">
          <div class="config-container">
            <div class="left-panel">
              <div class="section-card">
                <div class="section-title">基础信息</div>
                <div class="document-name-content">
                  <div class="form-wrapper">
                    <div class="form-item">
                      <div class="form-label required">知识库名称</div>
                      <el-input v-model="knowledgeDetail.name" placeholder="请输入知识库名称" />
                    </div>
                    <div class="form-item">
                      <div class="form-label">描述</div>
                      <el-input
                        v-model="knowledgeDetail.description"
                        type="textarea"
                        placeholder="对该知识库的简要描述，用途等信息..."
                        :rows="6"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="section-card">
                <div class="section-title">索引方式设置</div>
                <div class="index-options">
                  <div
                    class="index-option-card"
                    :class="{ active: indexType === 'high_quality' }"
                    @click="indexType = 'high_quality'"
                  >
                    <div class="icon-container crown">
                      <el-icon><Star /></el-icon>
                    </div>
                    <div class="option-name">高质量模式</div>
                    <div class="option-desc">适合需要精确匹配</div>
                  </div>
                  <div
                    class="index-option-card"
                    :class="{ active: indexType === 'economy' }"
                    @click="indexType = 'economy'"
                  >
                    <div class="icon-container lightning">
                      <el-icon><Lightning /></el-icon>
                    </div>
                    <div class="option-name">经济模式</div>
                    <div class="option-desc">适合快速检索</div>
                  </div>
                </div>
              </div>
              <div class="section-card">
                <div class="section-title">Embedding 模型</div>
                <el-select
                  v-model="embeddingModel"
                  placeholder="请选择Embedding模型"
                  class="full-width-select"
                  value-key="model"
                  filterable
                >
                  <el-option-group
                    v-for="provider in embeddingModelList"
                    :key="provider.provider"
                    :label="provider.label.zh_Hans || provider.label.en_US"
                  >
                    <el-option
                      v-for="model in provider.models"
                      :key="model.model"
                      :label="model.model"
                      :value="{provider: provider.provider, model: model.model}"
                    />
                  </el-option-group>
                </el-select>
              </div>
              <div class="section-card">
                <div class="section-title">检索策略设置</div>
                <div class="retrieval-strategy">
                  <div class="strategy-tabs">
                    <template v-if="indexType === 'high_quality'">
                      <div
                        class="strategy-tab"
                        :class="{ active: retrievalStrategy === 'vector' }"
                        @click="retrievalStrategy = 'vector'"
                      >
                        <el-icon><Compass /></el-icon>
                        <span>向量检索</span>
                        <div class="tab-desc">基于语义相似度的智能匹配</div>
                      </div>
                      <div
                        class="strategy-tab"
                        :class="{ active: retrievalStrategy === 'fulltext' }"
                        @click="retrievalStrategy = 'fulltext'"
                      >
                        <el-icon><Search /></el-icon>
                        <span>全文检索</span>
                        <div class="tab-desc">基于关键词的精确匹配</div>
                      </div>
                      <div
                        class="strategy-tab"
                        :class="{ active: retrievalStrategy === 'hybrid' }"
                        @click="retrievalStrategy = 'hybrid'"
                      >
                        <el-icon><Connection /></el-icon>
                        <span>混合检索</span>
                        <div class="tab-desc">综合语义与关键词的平衡检索</div>
                      </div>
                    </template>
                    <template v-else>
                      <div class="strategy-tab active">
                        <el-icon><Document /></el-icon>
                        <span>倒排索引</span>
                        <div class="tab-desc">倒排索引是一种用于高效检索的结构，按关键词索引文档或网页</div>
                      </div>
                    </template>
                  </div>

                  <div class="retrieval-settings">
                    <!-- 高质量模式下的设置 -->
                    <template v-if="indexType === 'high_quality'">
                      <!-- 混合检索模式下的Tab切换 -->
                      <div v-if="retrievalStrategy === 'hybrid'" class="settings-tabs">
                        <div class="settings-header">
                          <div
                            class="settings-tab"
                            :class="{ active: activeSettingsTab === 'weight' }"
                            @click="activeSettingsTab = 'weight'"
                          >
                            权重设置
                          </div>
                          <div
                            class="settings-tab"
                            :class="{ active: activeSettingsTab === 'rerank' }"
                            @click="activeSettingsTab = 'rerank'"
                          >
                            Rerank设置
                          </div>
                        </div>

                        <div class="settings-content">
                          <div v-if="activeSettingsTab === 'weight'">
                            <div class="weights-display-container">
                            <div class="weights-bar">
                              <div class="weight-display-item">
                                <div class="weight-label">语义权重</div>
                                <div class="weight-value">{{ semanticWeight.toFixed(1) }}</div>
                              </div>
                              <div class="weight-display-item">
                                <div class="weight-label">关键词权重</div>
                                <div class="weight-value">{{ keywordWeight.toFixed(1) }}</div>
                              </div>
                            </div>
                          </div>

                            <div class="slider-section">
                              <div class="slider-with-value">
                                <el-slider
                                  v-model="semanticWeight"
                                  :min="0"
                                  :max="1"
                                  :step="0.1"
                                  :format-tooltip="formatWeightTooltip"
                                  @input="updateWeights"
                                />
                              </div>

                              <div class="threshold-label">相似度阈值：{{ Math.round(scoreThreshold * 100) / 100 }}</div>
                              <div class="slider-with-value">
                                <el-slider v-model="scoreThreshold" :min="0" :max="1" :step="0.1" :format-tooltip="formatThresholdTooltip" />
                                <el-input-number v-model="scoreThreshold" :min="0" :max="1" :step="0.1" controls-position="right" size="small" :precision="1" />
                              </div>
                            </div>

                            <div class="slider-section top-k-section">
                              <div class="threshold-label">Top K 结果数</div>
                              <div class="slider-with-value">
                                <el-slider v-model="topK" :min="1" :max="50" :step="1" />
                                <el-input-number v-model="topK" :min="1" :max="50" controls-position="right" size="small" />
                              </div>
                            </div>
                          </div>

                          <div v-else>
                            <div class="option-item" v-if="enableReranking">
                              <div class="threshold-label">关键词权重：{{ crossEncoderThreshold }}</div>
                              <div class="slider-with-value">
                                <el-slider v-model="crossEncoderThreshold" :min="0" :max="1" :step="0.1" :format-tooltip="formatThresholdTooltip" />
                                <el-input-number v-model="crossEncoderThreshold" :min="0" :max="1" :step="0.1" controls-position="right" size="small" :precision="1" />
                              </div>
                            </div>
                            <div class="slider-section top-k-section">
                              <div class="threshold-label">Top K 结果数</div>
                              <div class="slider-with-value">
                                <el-slider v-model="topK" :min="1" :max="50" :step="1" />
                                <el-input-number v-model="topK" :min="1" :max="50" controls-position="right" size="small" />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 非混合检索模式的相似度阈值设置 -->
                      <div v-else class="single-setting">
                        <div class="option-item">
                          <div class="threshold-label">相似度阈值：{{ Math.round(scoreThreshold * 100) / 100 }}</div>
                          <div class="slider-with-value">
                            <el-slider v-model="scoreThreshold" :min="0" :max="1" :step="0.1" :format-tooltip="formatThresholdTooltip" />
                            <el-input-number v-model="scoreThreshold" :min="0" :max="1" :step="0.1" controls-position="right" size="small" :precision="1" />
                          </div>
                        </div>
                      </div>

                      <!-- 所有检索模式下都显示的Top K设置 -->
                      <div v-if="retrievalStrategy !== 'hybrid'" class="single-setting settings-row top-k-row">
                        <div class="top-k-settings">
                          <div class="threshold-label">Top K 结果数</div>
                          <div class="slider-with-value">
                            <el-slider v-model="topK" :min="1" :max="50" :step="1" />
                            <el-input-number v-model="topK" :min="1" :max="50" controls-position="right" size="small" />
                          </div>
                        </div>
                      </div>
                    </template>

                    <!-- 经济模式下只显示Top K设置 -->
                    <template v-else>
                      <div class="single-setting settings-row top-k-row">
                        <div class="top-k-settings">
                          <div class="threshold-label">Top K <el-tooltip content="检索返回结果的最大数量" placement="top"><el-icon class="info-icon"><InfoFilled /></el-icon></el-tooltip></div>
                          <div class="slider-with-value">
                            <el-slider v-model="topK" :min="1" :max="50" :step="1" />
                            <el-input-number v-model="topK" :min="1" :max="50" controls-position="right" size="small" />
                          </div>
                        </div>
                      </div>
                    </template>

                     <!-- Rerank模型选择，只在高质量模式下显示 -->
                    <div class="single-setting settings-row rerank-model top-k-row" v-if="indexType === 'high_quality' && (retrievalStrategy !== 'hybrid' || activeSettingsTab !== 'weight')">
                      <div class="threshold-label">Rerank 模型</div>
                      <el-select
                        v-model="rerankModel"
                        placeholder="请选择Rerank模型"
                        class="full-width-select"
                        value-key="model"
                        filterable
                      >
                        <el-option-group
                          v-for="provider in rerankModelList"
                          :key="provider.provider"
                          :label="provider.label.zh_Hans || provider.label.en_US"
                        >
                          <el-option
                            v-for="model in provider.models"
                            :key="model.model"
                            :label="model.model"
                            :value="{provider: provider.provider, model: model.model}"
                          />
                        </el-option-group>
                      </el-select>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 权限管理部分 -->
              <!-- <div class="section-card">
                <div class="section-title">权限管理</div>
                <div class="permission-content">
                  <div class="search-bar">
                    <el-input
                      v-model="searchKeyword"
                      placeholder="搜索成员"
                      :prefix-icon="Search"
                      size="default"
                      clearable
                    />
                    <el-button type="primary" @click="handleAddMember">
                      <el-icon><Plus /></el-icon>
                      添加成员
                    </el-button>
                  </div>

                  <el-table :data="memberList" style="width: 100%" border>
                    <el-table-column label="成员" min-width="180">
                      <template #default="{ row }">
                        <div class="member-info">
                          <el-avatar :size="32" class="member-avatar">{{ row.avatar ? '' : row.name.charAt(0) }}</el-avatar>
                          <div class="member-name-email">
                            <div class="member-name">{{ row.name }}</div>
                            <div class="member-email">{{ row.email }}</div>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="加入时间" width="150">
                      <template #default="{ row }">
                        {{ formatDate(row.joinDate) }}
                      </template>
                    </el-table-column>
                    <el-table-column label="角色" width="150">
                      <template #default="{ row }">
                        <el-select v-model="row.role" placeholder="选择角色" size="small">
                          <el-option label="管理员" value="admin" />
                          <el-option label="编辑者" value="editor" />
                          <el-option label="所有者" value="owner" />
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" align="center">
                      <template #default="{ row }">
                        <el-button
                          type="danger"
                          text
                          size="small"
                          @click="handleRemoveMember(row)"
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div> -->
            </div>
          </div>

          <div class="bottom-actions">
            <div class="button-container">
              <el-button @click="goBack" class="cancel-btn">取消</el-button>
              <el-button type="primary" @click="saveKnowledge" class="save-btn">保存修改</el-button>
            </div>
          </div>
        </div>

        <!-- 知识库召回测试内容 -->
        <div v-if="activeTab == 'test'" class="test-content">
          <div class="test-header">
            <div class="back-button" @click="activeTab = 'settings'">
              <el-icon><ArrowLeft /></el-icon>
              检索测试
            </div>
          </div>

          <div class="test-container">
            <!-- 左侧测试输入区域 -->
            <div class="test-input-area">
              <div class="query-input-container">
                <el-input
                  v-model="testQuery"
                  type="textarea"
                  :rows="10"
                  placeholder="输入问题或文本词（支持长文本，最多 500 字符）"
                  :maxlength="500"
                  show-word-limit
                />
                <!-- <div class="input-footer">
                  <span class="char-count">{{ testQuery.length }}/500</span>
                </div> -->
              </div>

              <div class="test-mode-selector">
                <el-select v-model="testMode" placeholder="混合检索" class="mode-select">
                  <el-option label="混合检索" value="hybrid_search" />
                  <el-option label="向量检索" value="semantic_search" />
                  <el-option label="全文检索" value="full_text_search" />
                </el-select>
                <!-- <el-tooltip content="混合模式会同时使用语义和关键词的方式进行检索" placement="top">
                  <el-icon class="info-icon"><InfoFilled /></el-icon>
                </el-tooltip> -->
                <el-button type="primary" @click="startTest">开始测试</el-button>
              </div>

              <!-- <div class="test-description">
                <el-icon><InfoFilled /></el-icon>
                <span>混合模式会同时使用语义检索和全文检索，并对搜索结果进行智能排序</span>
              </div> -->

              <div class="history-records">
                <div class="history-header">
                  <div class="history-title">历史记录</div>
                  <el-button link type="primary" @click="showAllHistory">查看全部</el-button>
                </div>

                <div class="history-list">
                  <div v-for="(record, index) in testHistory" :key="index" class="history-item" @click="loadHistoryItem(record)">
                    <div class="history-query">{{ record.query }}</div>
                    <div class="history-meta">
                      <span class="history-time">{{ formatTimestamp(record.time) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧测试结果区域 -->
            <div class="test-results-area">
              <div class="results-count" v-if="testResults.length > 0">
                <span>{{ testResults.length }} 个召回结果</span>
              </div>

              <div class="results-list">
                <div v-for="(result, index) in testResults" :key="index" class="result-item">
                  <div class="result-header">
                    <div class="chunk-id">
                      <el-icon><Document /></el-icon>
                      <span class="result-title">分段</span>
                      <span class="page-count">· {{ result.pageCount }} 字符</span>
                    </div>
                    <div class="result-score">
                      <span class="score-badge">SCORE <span class="score-number">{{ formatScore(result.score) }}</span></span>
                    </div>
                  </div>

                  <div class="result-content">
                    {{ result.content }}
                  </div>

                  <div class="result-footer">
                    <div class="result-tags" v-if="result.tags && result.tags.length > 0">
                      <span v-for="tag in result.tags" :key="tag" class="tag-item"># {{ tag }}</span>
                    </div>

                    <div class="result-file">
                      <div class="file-info">
                        <el-icon style="color: #dc2626"><Document /></el-icon>
                        <span class="file-name">{{ result.docFileName }}</span>
                      </div>
                      <span class="file-action" @click="openDetailDialog(result)">打开 <el-icon><ArrowRight /></el-icon></span>
                    </div>
                  </div>
                </div>

                <div v-if="testResults.length === 0" class="no-results">
                  <el-empty description="暂无召回结果" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="activeTab == 'permission'" key="permission" class="permission-content">
          <div class="config-container">
            <div class="left-panel">
              <div class="section-card">
                <div class="section-title">权限管理</div>
                <div class="permission-content">
                  <div class="search-bar">
                    <el-input
                      v-model="searchKeyword"
                      placeholder="搜索成员"
                      :prefix-icon="Search"
                      size="default"
                      clearable
                    />
                    <el-button type="primary" @click="handleAddMember">
                      <el-icon><Plus /></el-icon>
                      添加成员
                    </el-button>
                  </div>

                  <el-table :data="filteredMemberList" style="width: 100%" border v-loading="memberListLoading" v-if="activeTab === 'permission'">
                    <el-table-column label="成员" min-width="180">
                      <template #default="{ row }">
                        <div class="member-info">
                          <el-avatar :size="32" class="member-avatar">{{ row.avatar ? '' : row.name.charAt(0) }}</el-avatar>
                          <div class="member-name-email">
                            <div class="member-name">{{ row.name }}</div>
                            <div class="member-email">{{ row.email }}</div>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="角色" width="150">
                      <template #default="{ row }">
                        <span>{{ row.role == "member" ? "成员" : "管理员" }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="180" align="center">
                      <template #default="{ row }">
                        <el-button
                          type="danger"
                          text
                          size="small"
                          @click="handleRemoveMember(row)"
                        >
                          移除
                        </el-button>
                        <el-button
                          type="primary"
                          text
                          size="small"
                          @click="handleRoleChange(row)"
                        >
                          {{ row.role == "member" ? "设置管理员" : "移除管理员" }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </div>
            <!-- <div class="bottom-actions">
              <div class="button-container">
                <el-button type="primary" @click="saveKnowledge" class="save-btn">保存修改</el-button>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加成员对话框 -->
  <el-dialog
    v-model="memberDialogVisible"
    title="添加成员"
    width="620px"
    :before-close="() => memberDialogVisible = false"
  >
    <div class="member-dialog-content">
      <el-select
        v-model="selectedMemberIds"
        multiple
        filterable
        placeholder="请选择成员"
        style="width: 100%"
        @change="handleSelectedMembersChange"
      >
        <el-option
          v-for="member in filteredWorkspaceMembers"
          :key="member.id"
          :label="member.name"
          :value="member.id"
        >
          <div style="display: flex; align-items: center; padding: 0;">
            <el-avatar
              :size="28"
              style="margin-right: 12px; background-color: #409EFF; color: white; font-size: 14px;"
            >
              {{ member.avatar_url ? '' : member.name.charAt(0).toUpperCase() }}
            </el-avatar>
            <span style="font-size: 14px;">{{ member.name }}</span>
          </div>
        </el-option>
      </el-select>

      <div class="selected-members-container" v-if="selectedMembers.length > 0">
        <div class="selected-title">已选择 {{ selectedMembers.length }} 位成员</div>
        <div class="selected-members-list">
          <div v-for="member in selectedMembers" :key="member.id" class="selected-member-item">
            <div class="selected-member-info">
              <el-avatar
                :size="28"
                class="selected-member-avatar"
                style="background-color: #409EFF; color: white; font-size: 14px;"
              >
                {{ member.avatar_url ? '' : member.name.charAt(0).toUpperCase() }}
              </el-avatar>
              <span class="selected-member-name">{{ member.name }}</span>
            </div>
            <el-icon class="remove-selected" @click="removeSelectedMember(member)">
              <Close />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="memberDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAddMembers">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 查看全部历史记录对话框 -->
  <el-dialog
    v-model="historyDialogVisible"
    title="历史测试记录"
    width="800px"
    :before-close="() => historyDialogVisible = false"
  >
    <el-table :data="allHistoryRecords" style="width: 100%">
      <el-table-column prop="query" label="测试内容" min-width="300">
        <template #default="{ row }">
          <span class="history-query-text">{{ row.query }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="time" label="时间" width="180">
        <template #default="{ row }">
          <span>{{ formatTimestamp(row.time) }}</span>
        </template>
      </el-table-column>
      <el-table-column width="100">
        <template #default="{ row }">
          <el-button type="primary" link @click="loadHistoryItem(row)">使用</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container" style="margin-top: 20px; text-align: right;">
      <el-pagination
        v-model:current-page="historyCurrentPage"
        v-model:page-size="historyPageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="historyTotal"
        @size-change="handleHistorySizeChange"
        @current-change="handleHistoryPageChange"
      />
    </div>
  </el-dialog>

  <!-- 段落详情对话框 -->
  <el-dialog
    v-model="detailDialogVisible"
    title="段落详情"
    width="80%"
    :before-close="() => detailDialogVisible = false"
    destroy-on-close
  >
    <div class="file-detail-content" v-if="currentDetailFile">
      <div class="detail-header">
        <div class="chunk-info">
          <el-icon><Document /></el-icon>
          <span>{{ currentDetailFile.fileName }}</span>
          <span class="document-source">· {{ currentDetailFile.docFileName }}</span>
        </div>
        <div class="score-info">
          <span class="score-badge">SCORE <span class="score-number">{{ formatScore(currentDetailFile.score) }}</span></span>
        </div>
      </div>

      <div class="detail-content">
        <p>{{ currentDetailFile.content }}</p>
      </div>

      <div class="detail-keywords" v-if="currentDetailFile.tags && currentDetailFile.tags.length > 0">
        <div class="keyword-title">关键词</div>
        <div class="keyword-list">
          <span v-for="tag in currentDetailFile.tags" :key="tag" class="keyword-item"># {{ tag }}</span>
        </div>
      </div>
    </div>
  </el-dialog>

  <!-- 添加知识库权限设置组件 -->
  <document-permission
    v-model:visible="permissionDialogVisible"
    :document-id="currentDocumentId"
  />

  <!-- 添加文件上传对话框 -->
  <el-dialog
    v-model="fileUploadDialogVisible"
    title="上传文本文件"
    width="600px"
    :destroy-on-close="true"
    :close-on-click-modal="false"
  >
    <div class="upload-dialog-content">
      <div class="upload-area">
        <el-upload
          class="upload-dragger"
          drag
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :limit="1"
          :file-list="uploadFileList"
        >
          <el-icon class="upload-icon"><Upload /></el-icon>
          <div class="upload-text">拖拽文件至此，或者 <em>点击上传</em></div>
          <div class="upload-supported-types">
            已支持 TXT、MARKDOWN、MDX、PDF、HTML、XLSX、XLS、DOCX、CSV、MD、HTM，每个文件不超过 15MB。
          </div>
        </el-upload>

        <!-- 添加上传进度显示 -->
        <div v-if="uploadProgress > 0" class="upload-progress">
          <el-progress :percentage="uploadProgress" :format="percentageFormat" />
          <div class="progress-text">正在上传: {{ uploadFileList[0]?.name }}</div>
        </div>
      </div>
      <div class="upload-action-buttons">
        <el-button @click="fileUploadDialogVisible = false" :disabled="uploading">取消</el-button>
        <el-button
          type="primary"
          :disabled="!uploadFileList.length || uploading"
          :loading="uploading"
          @click="handleUploadSubmit"
        >
          {{ uploading ? '上传中...' : '确认上传' }}
        </el-button>
      </div>
    </div>
  </el-dialog>
  <!-- Rename Dialog -->
  <el-dialog
    v-model="renameDialogVisible"
    title="重命名"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="rename-dialog-content">
      <div class="form-item">
        <div class="form-label">名称</div>
        <el-input v-model="newDocumentName" placeholder="请输入文档名称" />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="renameDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmRenameDocument">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
// @ts-nocheck
import { ref, onMounted, watch, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ArrowLeft, Plus, Search, Document, Star, Lightning, Compass, Connection, InfoFilled, Close, Upload } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import DocumentPermission from '@/components/knowledge/DocumentPermission.vue';
import {
  fetchDatasets,
  fetchDatasetDetail,
  updateDataset,
  fetchDocuments,
  enableDocument,
  disableDocument,
  deleteDocument,
  archiveDocument,
  unarchiveDocument,
  renameDocument,
  getDatasetDetail,
  updateDatasetSettings,
  fetchRerankModelList,
  fetchTextEmbeddingModelList,
  fetchDefaultRerankModel,
  fetchDefaultTextEmbeddingModel,
  fetchWorkspaceMembers,
  hitTesting,
  uploadFile, // 引入上传文件接口
  addDocument,    // 如果需要添加文档到知识库的接口
  fetchTestingRecords, // 添加测试记录API引入
  fetchDatasetMembers, // 获取知识库成员列表
  addDatasetMember, // 添加知识库成员
  deleteDatasetMember, // 删除知识库成员
  updateDatasetMember, // 更新知识库成员角色
  fetchDocumentProcessRule,
} from '@/api/knowledge';
import { getUser } from '@/utils/user';

defineOptions({
  name: 'KnowledgeEdit',
});

// 路由相关
const router = useRouter();
const route = useRoute();
const knowledgeId = ref<string>(route.params.id as string);

// 标签页状态
const activeTab = ref('documents');

// 直接使用高质量模式
const indexType = ref('high_quality');
const embeddingModel = ref<{provider: string, model: string} | null>(null);
const embeddingModelList = ref([
  {
    provider: 'huggingface',
    label: {
      zh_Hans: 'Hugging Face',
      en_US: 'Hugging Face',
    },
    models: [
      {
        model: 'sentence-transformers/all-MiniLM-L6-v2',
      },
      {
        model: 'sentence-transformers/all-distilroberta-v1',
      },
    ],
  },
  {
    provider: 'sentence-transformers',
    label: {
      zh_Hans: 'Sentence Transformers',
      en_US: 'Sentence Transformers',
    },
    models: [
      {
        model: 'all-MiniLM-L6-v2',
      },
      {
        model: 'all-distilroberta-v1',
      },
    ],
  },
]);

const retrievalStrategy = ref('vector');
const activeSettingsTab = ref('weight');
const semanticWeight = ref(0.5);
const keywordWeight = ref(0.5);
const scoreThreshold = ref(0.5);
const topK = ref(10);
const crossEncoderThreshold = ref(0.5);
const enableReranking = ref(true);
const rerankModel = ref<{provider: string, model: string}>({provider: '', model: ''});
const rerankModelList = ref<any[]>([]);

// 权限管理部分
const searchKeyword = ref('');
const memberList = ref<any[]>([]);
const memberListLoading = ref(false);
const userInfo = ref<any>(null);
const permissionDialogVisible = ref(false);
const currentDocumentId = ref('');

// 成员对话框相关
const memberDialogVisible = ref(false);
const selectedMemberIds = ref<string[]>([]);
const selectedMembers = ref<any[]>([]);
const workspaceMembers = ref<any[]>([]);
const filteredWorkspaceMembers = ref<any[]>([]);

// 基本函数
const goBack = () => {
  // 尝试使用带哈希刷新的方式，有时这能解决SPA导航问题
  // window.location.href = '/knowledge#' + new Date().getTime();
  router.push('/knowledge');
};

onMounted(() => {
  if (route.params.id) {
    console.log("knowledgeId", route.params.id);
    knowledgeId.value = route.params.id as string;
    getKnowledgeDetail();
    // 获取文档列表
    fetchDocumentsList();
    // 获取嵌入和Rerank模型列表
    fetchTextEmbeddingModelListData();
    fetchRerankModelListData();
    fetchWorkspaceMembersList();
  }
});

// 知识库详情数据
const knowledgeDetail = ref<any>({});
const permissionType = ref('');
const scoreThresholdEnabled = ref(true);
// 获取知识库详情
const getKnowledgeDetail = async () => {
  try {
    // 这里应该调用API获取知识库详情
    const res = await getDatasetDetail(knowledgeId.value);
    if (res) {
      knowledgeDetail.value = res;

      // 更新索引方式
      indexType.value = res.indexing_technique || 'high_quality';

      // 更新嵌入模型
      // embeddingModel.value = res.embedding_model || '';
      if (res.embedding_model) {
        embeddingModel.value = {provider: res.embedding_model_provider, model: res.embedding_model};
      }
      // 更新检索策略
      if (res.retrieval_model_dict) {
        const retrievalModelDict = res.retrieval_model_dict;

        // 设置检索策略
        if (retrievalModelDict.search_method === 'semantic_search') {
          retrievalStrategy.value = 'vector';
        } else if (retrievalModelDict.search_method === 'full_text_search') {
          retrievalStrategy.value = 'fulltext';
        } else if (retrievalModelDict.search_method === 'hybrid_search') {
          retrievalStrategy.value = 'hybrid';

          // 设置混合检索的权重
          if (retrievalModelDict.weights) {
            const weights = retrievalModelDict.weights;
            // 处理weights里的keyword_setting
            if (weights.keyword_setting && weights.keyword_setting.keyword_weight !== undefined) {
              keywordWeight.value = parseFloat(weights.keyword_setting.keyword_weight.toFixed(1));
            } else {
              keywordWeight.value = 0.5;
            }

            // 处理weights里的vector_setting
            if (weights.vector_setting && weights.vector_setting.vector_weight !== undefined) {
              semanticWeight.value = parseFloat(weights.vector_setting.vector_weight.toFixed(1));
            } else {
              semanticWeight.value = 0.5;
            }
          }
        }

        // 设置TopK和阈值
        topK.value = retrievalModelDict.top_k || 3;
        scoreThreshold.value = retrievalModelDict.score_threshold || 0.5;
        scoreThresholdEnabled.value = retrievalModelDict.score_threshold_enabled || true;

        // 设置Rerank
        if (retrievalModelDict.reranking_enable) {
          enableReranking.value = true;
          crossEncoderThreshold.value = retrievalModelDict.cross_encoder_threshold || 0.5;

          // 设置Rerank模型信息
          if (retrievalModelDict.reranking_model) {
            rerankModel.value = {
              provider: retrievalModelDict.reranking_model.reranking_provider_name || '',
              model: retrievalModelDict.reranking_model.reranking_model_name || ''
            };
          }
        } else {
          enableReranking.value = false;
        }
      }

      // 设置权限
      // permissionType.value = res.permission || 'partial_members';

      // 如果是仅自己可见，则显示当前用户信息
      // if (permissionType.value === 'only_me') {
      //   // 从本地存储获取用户信息
      //   const user = getUser();
      //   if (user) {
      //     memberList.value = [{
      //       id: user.id,
      //       name: user.name || 'User',
      //       email: user.email || '',
      //       joinDate: new Date().toISOString(),
      //       role: 'admin',
      //       avatar: user.avatar_url || null
      //     }];
      //   }
      // }
      // // 如果是部分成员可见，处理成员列表
      // if (permissionType.value === 'partial_members' && res.partial_member_list && res.partial_member_list.length > 0) {
      //   console.log("//////////////////////////")
      //   // 获取id列表
      //   const memberIds = res.partial_member_list;

      //   // 等待工作空间成员列表加载完成
      //   await fetchWorkspaceMembersList();

      //   // 清空现有成员列表
      //   memberList.value = [];
      //   selectedMembers.value = [];
      //   selectedMemberIds.value = [];

      //   // 匹配工作空间成员并填充列表
      //   memberIds.forEach((id: string) => {
      //     // 在工作空间成员中查找匹配的成员
      //     const member = workspaceMembers.value.find(m => m.id === id);

      //     if (member) {
      //       // 添加到已选中成员列表
      //       selectedMembers.value.push(member);
      //       selectedMemberIds.value.push(id);

      //       // 添加到成员列表
      //       memberList.value.push({
      //         id: member.id,
      //         name: member.name || '未知用户',
      //         email: member.email || '',
      //         joinDate: new Date().toISOString(),
      //         role: member.role, // 默认角色
      //         avatar: member.avatar_url || null
      //       });
      //     }
      //   });
      // }
    }
  } catch (error) {
    console.error('获取知识库详情失败', error);
    ElMessage.error('获取知识库详情失败');
  }
};

// 保存知识库设置
const saveKnowledge = async () => {
  try {
    // 构建要保存的数据
    const saveData: any = {
      name: knowledgeDetail.value.name,
      description: knowledgeDetail.value.description,
      indexing_technique: indexType.value,
      embedding_model: embeddingModel.value.model,
      embedding_model_provider: embeddingModel.value.provider || '',
      permission: permissionType.value
    };

    // 构建检索模型数据
    const retrievalModel: any = {
      top_k: topK.value,
      score_threshold: scoreThreshold.value,
      score_threshold_enabled: scoreThresholdEnabled.value
    };

    // 根据检索策略设置search_method
    if (retrievalStrategy.value === 'vector') {
      retrievalModel.search_method = 'semantic_search';
    } else if (retrievalStrategy.value === 'fulltext') {
      retrievalModel.search_method = 'full_text_search';
    } else if (retrievalStrategy.value === 'hybrid') {
      retrievalModel.search_method = 'hybrid_search';

      // 确保embeddingModel不为null
      if (embeddingModel.value) {
        // 修正混合检索的weights结构，放入retrievalModel.weights
        retrievalModel.weights = {
          weight_type: "customized",
          keyword_setting: {
            keyword_weight: parseFloat(keywordWeight.value.toFixed(1))
          },
          vector_setting: {
            vector_weight: parseFloat(semanticWeight.value.toFixed(1)),
            embedding_provider_name: embeddingModel.value.provider || '',
            embedding_model_name: embeddingModel.value.model
          }
        };
      }
    }

    // 设置rerank
    if (enableReranking.value) {
      retrievalModel.reranking_enable = true;
      retrievalModel.cross_encoder_threshold = crossEncoderThreshold.value;
      // 如果有rerank模型信息，保留它
      if (knowledgeDetail.value && knowledgeDetail.value.retrieval_model_dict && knowledgeDetail.value.retrieval_model_dict.reranking_model) {
        retrievalModel.reranking_model = knowledgeDetail.value.retrieval_model_dict.reranking_model;
      }
    } else {
      retrievalModel.reranking_enable = false;
    }

    saveData.retrieval_model = retrievalModel;

    // 如果是部分成员可见，添加成员列表
    if (permissionType.value === 'partial_members') {
      saveData.partial_member_list = memberList.value.map((member) => {
        return {
          user_id: member.id,
          role: member.role
        };
      });
    }

    // 调用更新API
    const res = await updateDataset(knowledgeId.value, saveData);
    if (res) {
      ElMessage.success('保存成功');
      // 重新获取一次数据
      getKnowledgeDetail();
    }
  } catch (error) {
    console.error('保存知识库设置失败', error);
    ElMessage.error('保存知识库设置失败');
  }
};

// // 权限管理部分函数
// const handleRemoveMember = async (row: any) => {
//   try {
//     // 调用删除成员API
//     await deleteDatasetMember(knowledgeId.value, row.id);

//     // 从memberList中移除成员
//     const index = memberList.value.findIndex(member => member.id === row.id);
//     if (index !== -1) {
//       memberList.value.splice(index, 1);
//     }

//     ElMessage.success('成员已移除');
//   } catch (error) {
//     console.error('移除成员失败:', error);
//     ElMessage.error('移除成员失败');
//   }
// };

const formatDate = (date: string) => {
  return date.replace('T', ' ').replace('Z', '');
};

// 将时间戳转换为年月日时分格式
const formatTimestamp = (timestamp: any) => {
  if (!timestamp) return '-';

  try {
    // 处理字符串类型的ISO日期
    if (typeof timestamp === 'string') {
      if (timestamp.includes('T') || timestamp.includes('-')) {
        return formatDate(timestamp);
      }
      // 如果是字符串形式的数字
      timestamp = parseInt(timestamp);
    }

    // 处理数字时间戳
    if (typeof timestamp === 'number') {
      // 如果时间戳长度为13位（毫秒级时间戳）
      if (timestamp.toString().length === 13) {
        timestamp = Math.floor(timestamp / 1000);
      }

      // 确保时间戳合理（1970年之后，2100年之前）
      if (timestamp < 0 || timestamp > 4102444800) {
        return '-';
      }

      const date = new Date(timestamp * 1000);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    }

    return '-';
  } catch (e) {
    console.error('时间格式化错误:', e, timestamp);
    return '-';
  }
};

// 文档状态类型
const getDocumentStatusType = (status: string): string => {
  switch (status) {
    case 'available':
      return 'success';
    case 'disabled':
      return 'info';
    case 'queuing':
    case 'indexing':
      return 'warning';
    case 'error':
      return 'danger';
    default:
      return 'info';
  }
};

// 文档状态文本
const getDocumentStatusText = (status: string): string => {
  switch (status) {
    case 'available':
      return '可用';
    case 'disabled':
      return '禁用';
    case 'queuing':
      return '排队中';
    case 'indexing':
      return '索引中';
    case 'error':
      return '错误';
    case 'archived':
      return '已归档';
    default :
      return '未知';
  }
};

// 知识库召回测试相关
const testQuery = ref('');
const testMode = ref('hybrid_search');
const testResults = ref<any[]>([]);
const hasTestedOnce = ref(false);
const testHistory = ref<any[]>([]);
const testHistoryLoading = ref(false);

// 获取历史测试记录
const fetchTestHistory = async () => {
  if (!knowledgeId.value) return;

  testHistoryLoading.value = true;
  try {
    const result = await fetchTestingRecords(knowledgeId.value, { page: 1, limit: 50 });

    console.log('获取历史记录', result);
    if (result && result.data) {
      testHistory.value = result.data.map((item: any) => {
        return {
          id: item.id,
          query: item.content,
          mode: item.source || 'hybrid_search',
          resultCount: item.result_count || 0,
          time: formatTimestamp(item.created_at)
        };
      });
    }
  } catch (error) {
    console.error('获取历史记录失败', error);
    ElMessage.error('获取历史记录失败');
  } finally {
    testHistoryLoading.value = false;
  }
};

// // 在组件挂载时，或者切换到测试标签页时获取历史记录
// watch(() => activeTab.value, (newVal) => {
//   if (newVal === 'test') {
//     fetchTestHistory();
//   }
// });

// 启动测试
const startTest = async () => {
  if (!testQuery.value) {
    ElMessage.warning('请输入测试问题');
    return;
  }

  try {
    // 获取当前设置中的retrieval模型配置
    let retrievalModel: any = {};

    // 如果有知识库详情的数据，使用其配置作为基础
    if (knowledgeDetail.value && knowledgeDetail.value.retrieval_model) {
      retrievalModel = { ...knowledgeDetail.value.retrieval_model };
    } else {
      // 默认配置
      retrievalModel = {
        search_method: 'hybrid_search',
        reranking_enable: false,
        top_k: 3,
        score_threshold_enabled: false,
        score_threshold: 0.5
      };
    }

    // 更新搜索方法为当前选择的测试模式
    retrievalModel.search_method = testMode.value;

    // 调用API进行测试
    const result = await hitTesting(knowledgeId.value, testQuery.value, retrievalModel);

    if (result && result.records) {
      // 处理返回的结果数据
      testResults.value = result.records.map((record: any) => {
        const segment = record.segment || {};
        return {
          id: segment.id || `chunk-${Math.random().toString(36).substring(2, 9)}`,
          fileName: `Chunk-${segment.position || '01'}`,
          pageCount: segment.word_count || 0,
          docFileName: segment.document?.name || 'Unknown',
          score: record.score || 0,
          content: segment.content || '',
          tags: segment.keywords || [],
          metadata: {
            document_id: segment.document_id,
            position: segment.position,
            word_count: segment.word_count,
            created_at: segment.created_at ? formatTimestamp(segment.created_at) : ''
          }
        };
      });

      // 如果没有结果
      if (testResults.value.length === 0) {
        ElMessage.info('没有找到匹配的结果');
      }

      // 测试完成后刷新历史记录
      fetchTestHistory();
    } else {
      ElMessage.warning('测试返回结果格式异常');
      testResults.value = [];
    }
  } catch (error) {
    console.error('测试失败', error);
    ElMessage.error('测试失败，请检查网络连接或联系管理员');
    testResults.value = [];
  }
};

// 加载历史记录项
const loadHistoryItem = (item: any) => {
  testQuery.value = item.query;
  // testMode.value = item.mode;
  startTest();
};

// 查看全部历史
const showAllHistory = () => {
  historyDialogVisible.value = true;
  fetchAllHistoryRecords();
};

// 获取模式名称
const getModeName = (mode: string) => {
  const modeMap: any = {
    'hybrid': '混合模式',
    'semantic': '语义模式',
    'fulltext': '全文检索'
  };
  return modeMap[mode] || mode;
};

// 格式化分数显示
const formatScore = (score: number) => {
  const roundedScore = Math.round(score * 100);
  return `${Math.floor(roundedScore/10)}/${70}`;
};

// 根据分数获取颜色
const getScoreColor = (score: number) => {
  if (score > 0.9) return '#3370ff';
  if (score > 0.8) return '#3370ff';
  if (score > 0.7) return '#3370ff';
  return '#606266';
};

// 获取Rerank模型列表
const fetchRerankModelListData = async () => {
  try {
    const response = await fetchRerankModelList();
    if (response.data && Array.isArray(response.data)) {
      rerankModelList.value = response.data;
    }
  } catch (error) {
    console.error('获取Rerank模型列表失败:', error);
    ElMessage.error('获取Rerank模型列表失败');
  }
};

// 获取文本嵌入模型列表
const fetchTextEmbeddingModelListData = async () => {
  try {
    const response = await fetchTextEmbeddingModelList();
    if (response.data && Array.isArray(response.data)) {
      embeddingModelList.value = response.data;
    }
  } catch (error) {
    console.error('获取文本嵌入模型列表失败:', error);
    ElMessage.error('获取文本嵌入模型列表失败');
  }
};

// 获取工作空间成员列表
const fetchWorkspaceMembersList = async () => {
  try {
    const response = await fetchWorkspaceMembers();
    if (response && response.accounts) {
      workspaceMembers.value = response.accounts;
      filteredWorkspaceMembers.value = [...workspaceMembers.value];
    }
  } catch (error) {
    console.error('获取工作空间成员列表失败:', error);
    ElMessage.error('获取工作空间成员列表失败');
  }
};

// 文档管理相关
const documentsSearchKeyword = ref('');
const documentsLoading = ref(false);
const documentsList = ref<any[]>([]);
const documentsCurrentPage = ref(1);
const documentsPageSize = ref(10);
const documentsTotalCount = ref(0);
const documentProcessRules = ref<any>(null);

// 获取文档列表
const fetchDocumentsList = async () => {
  if (!knowledgeId.value) return;

  documentsLoading.value = true;
  try {
    const params = {
      page: documentsCurrentPage.value,
      limit: documentsPageSize.value,
      keyword: documentsSearchKeyword.value
    };

    const response = await fetchDocuments(knowledgeId.value, params);
    documentsList.value = response.data || [];
    documentsTotalCount.value = response.total || 0;

    // 如果文档列表不为空，获取第一个文档的处理规则
    if (documentsList.value.length > 0) {
      const firstDocument = documentsList.value[0];
      try {
        const processRuleResponse = await fetchDocumentProcessRule(firstDocument.id);
        console.log('文档处理规则:', processRuleResponse);
        // 保存rules参数
        if (processRuleResponse && processRuleResponse.rules) {
          documentProcessRules.value = processRuleResponse.rules;
        }
      } catch (error) {
        console.error('获取文档处理规则失败:', error);
      }
    }
  } catch (error) {
    console.error('获取文档列表失败:', error);
    ElMessage.error('获取文档列表失败');
  } finally {
    documentsLoading.value = false;
  }
};



// 文档格式文本映射
const getDocForm = (docForm: string): string => {
  switch (docForm) {
    case 'text_model':
      return '文本模式';
    case 'qa_model':
      return '问答模式';
    case 'auto_model':
      return '自动模式';
    default:
      return '未知';
  }
};

// 切换文档启用状态
const handleToggleDocument = async (documentId: string, enabled: boolean) => {
  try {
    if (enabled) {
      await enableDocument(knowledgeId.value, documentId);
      ElMessage.success('文档已启用');
    } else {
      await disableDocument(knowledgeId.value, documentId);
      ElMessage.success('文档已禁用');
    }
    fetchDocumentsList();
  } catch (error) {
    console.error('切换文档状态失败:', error);
    ElMessage.error('操作失败');
    // 重新获取列表，恢复正确的状态
    fetchDocumentsList();
  }
};

// 文档操作
const handleAddDocument = () => {
  // 打开文件上传对话框
  fileUploadDialogVisible.value = true;
};

// 点击文档跳转到文档详情页
const handleDocumentClick = (document: any) => {
  // 使用router.push，App.vue已添加key属性强制重新渲染组件
  router.push({
    path: `/knowledge/${knowledgeId.value}/document/${document.id}`,
    query: {
      name: document.name,
      t: Date.now().toString() // 添加时间戳确保路由fullPath变化
    }
  });
};

// 权限设置
const handlePermissionSetting = (document: any) => {
  currentDocumentId.value = document.id;
  permissionDialogVisible.value = true;
};

// 删除文档
const handleDeleteDocument = (document: any) => {
  ElMessageBox.confirm(
    `确定要删除文档「${document.name}」吗？此操作不可恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger',
    }
  ).then(async () => {
    try {
      await deleteDocument(knowledgeId.value, document.id);
      ElMessage.success('删除成功');
      // 刷新文档列表
      fetchDocumentsList();
    } catch (error) {
      console.error('删除文档失败:', error);
      ElMessage.error('删除失败，请重试');
    }
  }).catch(() => {
    // 用户取消删除操作
  });
};

// 归档文档
const handleArchiveDocument = (document: any) => {
  if (document.archived) {
    // 如果已经归档，则撤销归档
    unarchiveDocument(knowledgeId.value, document.id)
      .then(() => {
        ElMessage.success('撤销归档成功');
        // 刷新文档列表
        fetchDocumentsList();
      })
      .catch((error) => {
        console.error('撤销归档失败:', error);
        ElMessage.error('撤销归档失败，请重试');
      });
  } else {
    // 如果未归档，则归档
    archiveDocument(knowledgeId.value, document.id)
      .then(() => {
        ElMessage.success('归档成功');
        // 刷新文档列表
        fetchDocumentsList();
      })
      .catch((error) => {
        console.error('归档失败:', error);
        ElMessage.error('归档失败，请重试');
      });
  }
};

// 重命名文档
interface DocumentType {
  id: string;
  name: string;
  [key: string]: any;
}

const renameDialogVisible = ref(false);
const currentRenameDocument = ref<DocumentType | null>(null);
const newDocumentName = ref('');

const handleRenameDocument = (document: any) => {
  currentRenameDocument.value = document;
  newDocumentName.value = document.name;
  renameDialogVisible.value = true;
};

const confirmRenameDocument = async () => {
  if (!currentRenameDocument.value || !newDocumentName.value.trim()) {
    ElMessage.warning('请输入有效的文档名称');
    return;
  }

  try {
    await renameDocument(knowledgeId.value, currentRenameDocument.value.id, newDocumentName.value.trim());
    ElMessage.success('重命名成功');
    // 关闭对话框
    renameDialogVisible.value = false;
    // 刷新文档列表
    fetchDocumentsList();
  } catch (error) {
    console.error('重命名文档失败:', error);
    ElMessage.error('重命名失败，请重试');
  }
};

// 文档上传相关
const fileUploadDialogVisible = ref(false);
const uploadFileList = ref<any[]>([]);
const uploadProgress = ref(0);
const uploading = ref(false);

// 进度条百分比显示格式化
const percentageFormat = (percentage: number) => {
  return percentage === 100 ? '完成' : `${percentage}%`;
};

// 处理文件改变事件
const handleFileChange = (file: any) => {
  // 限制文件类型
  const allowedTypes = ['text/plain', 'text/markdown', 'application/pdf', 'text/html', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/csv'];

  if (!allowedTypes.includes(file.raw.type) && file.name.split('.').pop().toLowerCase() !== 'md' && file.name.split('.').pop().toLowerCase() !== 'mdx' && file.name.split('.').pop().toLowerCase() !== 'htm') {
    ElMessage.error('文件类型不支持，请上传支持的文件类型');
    uploadFileList.value = [];
    return false;
  }

  // 限制文件大小 (15MB)
  const maxSize = 15 * 1024 * 1024;
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过15MB');
    uploadFileList.value = [];
    return false;
  }

  uploadFileList.value = [file];
};

// 处理文件移除事件
const handleFileRemove = () => {
  uploadFileList.value = [];
  uploadProgress.value = 0;
};

// 处理上传提交
const handleUploadSubmit = async () => {
  if (!uploadFileList.value.length || !knowledgeId.value) return;

  uploading.value = true;
  uploadProgress.value = 0;

  try {
    // 获取文件对象
    const file = uploadFileList.value[0].raw;

    // 调用上传文件接口，传入进度回调函数
    const response = await uploadFile(file, (progress: number) => {
      uploadProgress.value = progress;
    });

    if (response && response.data) {
      ElMessage.success('文件上传成功');

      // 文件上传成功，获取文件ID
      const fileId = response.data.id;

      // 准备addDocument接口所需参数
      const addDocumentParams = {
        data_source: {
          type: "upload_file",
          info_list: {
            data_source_type: "upload_file",
            file_info_list: {
              file_ids: [fileId]
            }
          }
        },
        indexing_technique: knowledgeDetail.value?.indexing_technique || "high_quality",
        process_rule: {
          rules: documentProcessRules.value || {
            pre_processing_rules: [
              {
                id: "remove_extra_spaces",
                enabled: true
              },
              {
                id: "remove_urls_emails",
                enabled: false
              }
            ],
            segmentation: {
              separator: "\n\n",
              max_tokens: 500,
              chunk_overlap: 50
            }
          },
          mode: "custom" // 默认值，如果后续需要动态获取可以修改
        },
        doc_form: knowledgeDetail.value?.doc_form || "text_model",
        doc_language: knowledgeDetail.value?.doc_language || "Chinese",
        retrieval_model: knowledgeDetail.value?.retrieval_model_dict || {
          search_method: "semantic_search",
          reranking_enable: true,
          reranking_mode: null,
          reranking_model: {
            reranking_provider_name: "langgenius/tongyi/tongyi",
            reranking_model_name: "gte-rerank"
          },
          weights: null,
          top_k: 3,
          score_threshold_enabled: true,
          score_threshold: 0.5
        },
        embedding_model: knowledgeDetail.value?.embedding_model || "text-embedding-v1",
        embedding_model_provider: knowledgeDetail.value?.embedding_model_provider || "langgenius/tongyi/tongyi"
      };

      // 调用addDocument接口添加文档
      try {
        const addDocumentResponse = await addDocument(knowledgeId.value, addDocumentParams);
        console.log('文档添加成功:', addDocumentResponse);
      } catch (error) {
        console.error('添加文档失败:', error);
        ElMessage.error('添加文档失败，请重试');
      }

      // 上传成功后刷新文档列表
      fetchDocumentsList();

      // 关闭对话框并重置状态
      fileUploadDialogVisible.value = false;
      uploadFileList.value = [];
      uploadProgress.value = 0;
    } else {
      ElMessage.error('上传文件失败，请重试');
    }
  } catch (error) {
    console.error('上传文件出错:', error);
    ElMessage.error('上传文件出错，请重试');
  } finally {
    uploading.value = false;
  }
};

// 成员对话框相关函数
// const handleAddMember = () => {
//   // 打开对话框前，初始化已有成员到选中列表中
//   // 先清空之前的选择
//   selectedMembers.value = [];
//   selectedMemberIds.value = [];

//   // 将已添加成员排除在候选列表之外
//   filteredWorkspaceMembers.value = workspaceMembers.value.filter(member => {
//     return !memberList.value.some(m => m.accountId === member.id);
//   });

//   // 打开对话框
//   memberDialogVisible.value = true;
// };

// 权限管理部分函数
const handleRemoveMember = async (row: any) => {
  console.log("rowrowrowrowrowrow", row)
  ElMessageBox.confirm(
    `确定要移除成员 "${row.name}" 吗？`,
    '移除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        // 调用删除成员API
        await deleteDatasetMember(knowledgeId.value, row.accountId);

        // 从memberList中移除成员
        const index = memberList.value.findIndex((member: any) => member.id === row.accountId);
        if (index !== -1) {
          memberList.value.splice(index, 1);
        }

        ElMessage.success('成员已移除');
        fetchDatasetMembersList();
      } catch (error) {
        console.error('移除成员失败:', error);
        ElMessage.error('移除成员失败');
      }
    })
    .catch(() => {
      // 用户取消操作
      ElMessage.info('已取消移除');
    });
};

const handleRoleChange = async (row: any) => {
  // 确定新角色
  const newRole = row.role === 'member' ? 'admin' : 'member';
  const actionText = row.role === 'member' ? '设置为管理员' : '移除管理员权限';

  ElMessageBox.confirm(
    `确定要将成员 "${row.name}" ${actionText}吗？`,
    '角色更改确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        // 调用更新成员角色API
        const data = {
          role: newRole
        };

        await updateDatasetMember(knowledgeId.value, row.accountId, data);
        // 更新本地数据
        row.role = newRole;

        ElMessage.success(`成员角色已${actionText}`);
        fetchDatasetMembersList();
      } catch (error) {
        console.error('更新成员角色失败:', error);
        ElMessage.error('更新成员角色失败');
      }
    })
    .catch(() => {
      // 用户取消操作
      ElMessage.info('已取消角色更改');
    });
};

// 历史记录对话框相关
const historyDialogVisible = ref(false);
const allHistoryRecords = ref<any[]>([]);
const historyCurrentPage = ref(1);
const historyPageSize = ref(10);
const historyTotal = ref(0);

const fetchAllHistoryRecords = async () => {
  try {
    const response = await fetchTestingRecords(knowledgeId.value, {
      page: historyCurrentPage.value,
      limit: historyPageSize.value
    });

    if (response && response.data) {
      allHistoryRecords.value = response.data.map((item: any) => {
        return {
          id: item.id,
          query: item.content,
          mode: item.source || 'hybrid_search',
          resultCount: item.result_count || 0,
          time: item.created_at
        };
      });
      historyTotal.value = response.total || 0;
    }
  } catch (error) {
    console.error('获取历史记录失败:', error);
    ElMessage.error('获取历史记录失败');
  }
};

const handleHistorySizeChange = (size: number) => {
  historyPageSize.value = size;
  fetchAllHistoryRecords();
};

const handleHistoryPageChange = (page: number) => {
  historyCurrentPage.value = page;
  fetchAllHistoryRecords();
};

const openDetailDialog = (result: any) => {
  currentDetailFile.value = result;
  detailDialogVisible.value = true;
};

const detailDialogVisible = ref(false);
const currentDetailFile = ref<any>(null);

// Format weight tooltip
const formatWeightTooltip = (val: number) => {
  return `语义: ${val.toFixed(1)} / 关键词: ${(1-val).toFixed(1)}`;
};

// Format threshold tooltip
const formatThresholdTooltip = (val: number) => {
  return `${Math.round(val * 100) / 100}`;
};

// 更新权重
const updateWeights = (val: number) => {
  semanticWeight.value = parseFloat(val.toFixed(1));
  keywordWeight.value = parseFloat((1 - val).toFixed(1));
};

// 获取知识库成员列表
const fetchDatasetMembersList = async () => {
  if (!knowledgeId.value) return;

  memberListLoading.value = true;
  try {
    const response = await fetchDatasetMembers(knowledgeId.value);
    if (response && response.members) {
      // 将API返回的数据转换为UI需要的格式
      memberList.value = response.members.map((member: any) => {
        // 在工作空间成员中查找完整信息
        const memberInfo = workspaceMembers.value.find(m => m.id === member.account_id);
        return {
          id: member.id, // 成员关系ID
          accountId: member.account_id, // 用户账号ID
          datasetId: member.dataset_id,
          name: memberInfo?.name || '未知用户',
          email: memberInfo?.email || '',
          joinDate: member.created_at,
          role: member.role,
          avatar: memberInfo?.avatar_url || null
        };
      });
    }
  } catch (error) {
    console.error('获取知识库成员列表失败:', error);
    ElMessage.error('获取知识库成员列表失败');
  } finally {
    memberListLoading.value = false;
  }
};

// 监听标签页切换，当切换到权限管理标签页时获取成员列表
watch(() => activeTab.value, (newVal) => {
  if (newVal === 'permission') {
    fetchDatasetMembersList();
  } else if (newVal === 'test') {
    fetchTestHistory();
  }
});

// 成员对话框相关函数
const handleAddMember = () => {
  // 打开对话框前，初始化已有成员到选中列表中
  // 先清空之前的选择
  selectedMembers.value = [];
  selectedMemberIds.value = [];

  // 将已添加成员排除在候选列表之外
  filteredWorkspaceMembers.value = workspaceMembers.value.filter(member => {
    return !memberList.value.some(m => m.accountId === member.id);
  });

  // 打开对话框
  memberDialogVisible.value = true;
};

const confirmAddMembers = async () => {
  if (selectedMembers.value.length === 0) {
    ElMessage.warning('请选择至少一名成员');
    return;
  }

  let successCount = 0;
  let failCount = 0;

  // 添加成员到知识库，逐个调用 API
  for (const member of selectedMembers.value) {
    try {
      const data = {
        account_id: member.id,
        role: 'member' // 默认角色为编辑者
      };

      const response = await addDatasetMember(knowledgeId.value, data);
      if (response) {
        successCount++;
      } else {
        failCount++;
      }
    } catch (error) {
      console.error('添加成员失败:', error);
      failCount++;
    }
  }

  // 成功添加成员后重新获取成员列表
  if (successCount > 0) {
    fetchDatasetMembersList();
  }

  // 关闭对话框并清空选择
  memberDialogVisible.value = false;
  selectedMembers.value = [];
  selectedMemberIds.value = [];

  // 显示成功或失败消息
  if (successCount > 0 && failCount === 0) {
    ElMessage.success(`已添加 ${successCount} 名成员`);
  } else if (successCount > 0 && failCount > 0) {
    ElMessage.warning(`成功添加 ${successCount} 名成员，${failCount} 名成员添加失败`);
  } else {
    ElMessage.error('添加成员失败');
  }
};

const removeSelectedMember = (member: any) => {
  selectedMembers.value = selectedMembers.value.filter(m => m.id !== member.id);
  selectedMemberIds.value = selectedMemberIds.value.filter(id => id !== member.id);
};

const handleSelectedMembersChange = (ids: string[]) => {
  selectedMembers.value = workspaceMembers.value.filter(member => ids.includes(member.id));
};

// 添加成员筛选功能
const filteredMemberList = computed(() => {
  if (!searchKeyword.value) {
    return memberList.value;
  }

  const keyword = searchKeyword.value.toLowerCase();
  return memberList.value.filter(member => {
    return member.name.toLowerCase().includes(keyword) ||
           (member.email && member.email.toLowerCase().includes(keyword));
  });
});

// 文档搜索
const handleDocumentsSearch = () => {
  documentsCurrentPage.value = 1;
  fetchDocumentsList();
};

// 分页处理
const handleDocumentsSizeChange = (size: number) => {
  documentsPageSize.value = size;
  fetchDocumentsList();
};

const handleDocumentsPageChange = (page: number) => {
  documentsCurrentPage.value = page;
  fetchDocumentsList();
};
</script>

<style scoped>
.edit-knowledge-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.back {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-right: 12px;
  color: #606266;
}

.title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.edit-content {
  display: flex;
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tab-sidebar {
  width: 200px;
  background-color: #f5f7fa;
  border-right: 1px solid #e8eaec;
  padding-top: 16px;
}

.tab-sidebar > .tab-item {
  padding: 14px 20px 14px 30px;
  cursor: pointer;
  color: #606266;
  transition: all 0.3s;
  position: relative;
  font-size: 14px;
  margin-bottom: 4px;
  border-left: 3px solid transparent;
}

.tab-sidebar > .tab-item:hover {
  background-color: #f0f2f5;
  color: #409eff;
}

.tab-sidebar > .tab-item.active {
  background-color: #ffffff;
  color: #409eff;
  font-weight: 500;
  border-left: 3px solid #409eff;
}

.tab-sidebar > .tab-item.active::before {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background-color: #fff;
}

.content-area {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.config-container {
  flex: 1;
  gap: 20px;
}

.left-panel {
  flex: 3;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background-color: #fff;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
}

.tab-container {
  overflow: hidden;
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.tab-header {
  display: flex;
  background-color: #f5f7fa;
}

.tab-item {
  flex: 1;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
  border-bottom: 2px solid transparent;
}

.tab-item.active {
  color: #409eff;
  background-color: #fff;
  border-bottom: 2px solid #409eff;
}

.tab-item:hover {
  background-color: #ecf5ff;
}

.tab-content {
  padding: 20px;
}

.vertical-form-item {
  margin-bottom: 20px;
}

.item-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.input-with-suffix {
  display: flex;
  align-items: center;
}

.suffix-text {
  margin-left: 10px;
  color: #909399;
}

.text-processing {
  margin-top: 20px;
}

.text-processing-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.preview-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.parent-child-wrapper {
  margin-top: 20px;
}

.pc-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
}

.pc-options-wrapper {
  display: flex;
}

.pc-option-left {
  width: 50%;
  margin-right: 20px;
}

.pc-option-right {
  width: 50%;
}

.parent-radio {
  margin-bottom: 10px;
}

.option-desc {
  color: #909399;
  margin-bottom: 20px;
}

.input-fields {
  margin-top: 20px;
}

.field-group {
  margin-bottom: 20px;
}

.field-label {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.field-hint {
  color: #909399;
  margin-top: 10px;
}

.child-content {
  margin-top: 20px;
}

.index-options {
  display: flex;
  gap: 20px;
  margin-top: 10px;
}

.index-option-card {
  flex: 1;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.index-option-card.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.index-option-card:hover {
  border-color: #409eff;
}

.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin: 0 auto 10px;
}

.crown {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.lightning {
  background-color: #f0f9eb;
  color: #67c23a;
}

.option-name {
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0;
}

.option-desc {
  font-size: 12px;
  color: #909399;
}

.strategy-tabs {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.strategy-tab {
  flex: 1;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.strategy-tab:hover {
  border-color: #409eff;
}

.strategy-tab.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.tab-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.settings-tabs {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.settings-header {
  display: flex;
  background-color: #f5f7fa;
}

.settings-tab {
  flex: 1;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.settings-tab.active {
  color: #409eff;
  background-color: #ecf5ff;
  border-bottom: 2px solid #409eff;
}

.settings-content {
  padding: 15px;
}

.weights-display-container {
  margin-bottom: 20px;
}

.weights-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.weight-display-item {
  display: flex;
  align-items: center;
}

.weight-label {
  font-size: 12px;
  color: #606266;
  margin-right: 5px;
}

.weight-value {
  font-size: 12px;
  font-weight: bold;
}

.weight-progress-container {
  margin-top: 10px;
}

.weight-progress {
  height: 24px;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
}

.slider-section {
  margin-bottom: 20px;
}

.threshold-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.slider-with-value {
  display: flex;
  align-items: center;
  width: 100%;
}

.top-k-section {
  margin-top: 20px;
}

.top-k-settings {
  margin-top: 20px;
}

.single-setting {
  padding: 20px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.top-k-row {
  margin-top: 20px;
}

.permission-content {
  padding: 20px;
}

.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.member-info {
  display: flex;
  align-items: center;
}

.member-avatar {
  margin-right: 10px;
}

.member-name-email {
  display: flex;
  flex-direction: column;
}

.member-name {
  font-size: 16px;
  color: #606266;
}

.member-email {
  font-size: 14px;
  color: #909399;
}

.document-name-section {
  margin-bottom: 20px;
}

.document-name-content {
  padding: 20px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.form-wrapper {
  padding: 20px;
}

.form-item {
  margin-bottom: 20px;
}

.form-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.form-label.required::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.action-buttons {
  margin-top: 20px;
  text-align: right;
}

.bottom-actions {
  margin-top: 20px;
  text-align: center;
  padding: 15px 0;
  background-color: #f5f7fa;
  border-top: 1px solid #e4e7ed;
}

.button-container {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.cancel-btn, .save-btn {
  min-width: 100px;
}

.member-dialog-content {
  padding: 20px;
}

.member-search-input {
  width: 100%;
  margin-bottom: 20px;
}

.member-select-container {
  display: flex;
  gap: 20px;
}

.selected-members-container {
  margin-top: 15px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f7f9fc;
}

.selected-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 12px;
}

.selected-members-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selected-member-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.selected-member-info {
  display: flex;
  align-items: center;
}

.selected-member-avatar {
  margin-right: 12px;
}

.selected-member-name {
  font-size: 14px;
  color: #303133;
}

.remove-selected {
  cursor: pointer;
  color: #909399;
  font-size: 16px;
}

.remove-selected:hover {
  color: #f56c6c;
}

.no-selected {
  padding: 10px;
  text-align: center;
  color: #909399;
}

/* 文档管理页面样式 */
.documents-content {
  width: 100%;
  padding: 20px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: calc(100vh - 240px); /* 使用视口高度的相对值 */
  position: relative;
}

.documents-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-container {
  width: 300px;
}

.documents-table-container {
  overflow-x: auto;
  flex: 1;
  overflow-y: auto;
  margin-bottom: 60px;
}

.document-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.document-actions {
  display: flex;
  gap: 8px;
}

.documents-pagination {
  position: absolute;
  bottom: 20px;
  right: 20px;
}

/* 知识库召回测试页面样式 */
.test-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.test-header {
  margin-bottom: 20px;
}

.back-button {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  cursor: pointer;
}

.back-button .el-icon {
  margin-right: 8px;
}

.test-container {
  display: flex;
  gap: 20px;
  height: calc(100% - 40px);
}

.test-input-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 45%;
}

.query-input-container {
  position: relative;
}

.input-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 5px;
}

.char-count {
  color: #909399;
  font-size: 12px;
}

.test-mode-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.mode-select {
  width: 150px;
}

.info-icon {
  color: #909399;
  cursor: pointer;
}

.test-description {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #909399;
  font-size: 12px;
  margin-top: -10px;
}

.history-records {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f5f7fa;
}

.history-title {
  font-weight: 500;
  color: #303133;
}

.history-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.history-item {
  padding: 12px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  transition: background-color 0.3s;
}

.history-item:hover {
  background-color: #f5f7fa;
}

.history-query {
  color: #303133;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.history-meta {
  display: flex;
  font-size: 12px;
  color: #909399;
  gap: 8px;
}

.history-time {
  margin-right: 8px;
}

.test-results-area {
  flex: 1.2;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.results-count {
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 500;
  font-size: 14px;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  flex: 1;
  padding: 16px;
}

.result-item {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  transition: box-shadow 0.3s;
  border: 1px solid #eaeef3;
  margin-bottom: 15px;
}

.result-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
}

.chunk-id {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.result-title {
  font-weight: 600;
  color: #333;
}

.page-count {
  color: #909399;
  font-size: 14px;
}

.score-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #eef5ff;
  color: #366ef4;
  font-size: 12px;
  font-weight: bold;
}

.score-number {
  margin-left: 4px;
  color: #366ef4;
}

.result-content {
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: 10px 0;
  border-top: 1px solid #eaeef3;
  border-bottom: 1px solid #eaeef3;
}

.result-footer {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-top: 10px;
}

.result-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag-item {
  color: #909399;
  font-size: 14px;
}

.result-file {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 8px 12px;
  margin-top: 10px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  overflow: hidden;
}

.file-name {
  font-size: 14px;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: none;
  flex: 1;
}

.file-action {
  display: flex;
  align-items: center;
  color: #909399;
  cursor: pointer;
  white-space: nowrap;
  margin-left: 16px;
}

.file-action:hover {
  color: #409eff;
}

.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.file-meta .el-icon {
  font-size: 12px;
  margin-left: 4px;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  flex: 1;
  padding: 16px;
}

.result-file {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-info {
  display: flex;
  align-items: center;
}

.file-name {
  font-size: 14px;
  color: #303133;
  margin-right: 10px;
}

.file-action {
  display: flex;
  align-items: center;
  color: #409eff;
  cursor: pointer;
}

.file-action:hover {
  color: #66b1ff;
}

/* 段落详情对话框样式 */
.file-detail-content {
  padding: 20px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chunk-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
}

.document-source {
  color: #909399;
  font-weight: normal;
  font-size: 14px;
}

.detail-content {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  margin-bottom: 20px;
  white-space: pre-line;
  line-height: 1.6;
}

.detail-keywords {
  padding: 10px 0;
}

.keyword-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 10px;
}

.keyword-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.keyword-item {
  color: #909399;
  font-size: 14px;
}

/* 文件上传对话框样式 */
.upload-dialog-content {
  display: flex;
  flex-direction: column;
  padding: 20px 0;
}

.upload-area {
  margin-bottom: 20px;
}

.upload-dragger {
  width: 100%;
}

.upload-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 12px;
}

.upload-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 8px;
}

.upload-text em {
  color: #409eff;
  font-style: normal;
  cursor: pointer;
}

.upload-supported-types {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.upload-action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.upload-progress {
  margin-top: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.progress-text {
  margin-top: 8px;
  font-size: 12px;
  color: #606266;
  text-align: center;
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-start;
}

/* 重命名对话框样式 */
.rename-dialog-content {
  padding: 20px 0;
}

.form-item {
  margin-bottom: 20px;
}

.form-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}
</style>

