<template>
  <el-dialog v-model="dialogVisible" title="添加成员" width="800px" :before-close="handleClose">
    <div class="member-dialog-content">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索成员"
        clearable
        class="search-input"
      />
      
      <div class="member-selection-container">
        <div class="member-tree-panel">
          <div v-for="org in filteredOrgs" :key="org.id" class="org-item">
            <div class="org-header">
              <span class="org-name">{{ org.name }}</span>
              <span class="org-count">({{ org.memberCount }}人)</span>
              <el-checkbox 
                v-model="org.checked" 
                @change="(val) => handleOrgCheckChange(org, val)"
              />
            </div>
            
            <div v-for="dept in org.departments" :key="dept.id" class="dept-item">
              <div class="dept-header">
                <el-icon><Folder /></el-icon>
                <span class="dept-name">{{ dept.name }}</span>
                <span class="dept-count">({{ dept.memberCount }}人)</span>
                <el-checkbox 
                  v-model="dept.checked" 
                  @change="(val) => handleDeptCheckChange(dept, val)"
                />
              </div>
              
              <div class="members-list">
                <div 
                  v-for="member in dept.members" 
                  :key="member.id"
                  class="member-item"
                >
                  <el-icon><User /></el-icon>
                  <span class="member-name">{{ member.name }}</span>
                  <el-checkbox 
                    v-model="member.checked" 
                    @change="(val) => handleMemberCheckChange(member, val)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="selected-members-panel">
          <div class="panel-header">已选择</div>
          <div class="selected-members-list">
            <div 
              v-for="member in selectedMembers" 
              :key="member.id"
              class="selected-member-item"
            >
              <el-icon><User /></el-icon>
              <span class="selected-member-name">{{ member.name }}</span>
              <el-icon class="remove-icon" @click="removeMember(member)"><Close /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { Folder, User, Close } from '@element-plus/icons-vue';

export default {
  name: 'SpaceMember',
  components: {
    Folder,
    User,
    Close
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    spaceId: {
      type: String,
      default: ''
    },
    currentMembers: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      searchKeyword: '',
      organizations: [],
      selectedMembers: []
    };
  },
  computed: {
    filteredOrgs() {
      if (!this.searchKeyword) {
        return this.organizations;
      }
      
      const keyword = this.searchKeyword.toLowerCase();
      return this.organizations.map(org => {
        // 过滤部门
        const filteredDepts = org.departments.map(dept => {
          // 过滤成员
          const filteredMembers = dept.members.filter(member => 
            member.name.toLowerCase().includes(keyword)
          );
          
          return {
            ...dept,
            members: filteredMembers
          };
        }).filter(dept => 
          dept.name.toLowerCase().includes(keyword) || dept.members.length > 0
        );
        
        return {
          ...org,
          departments: filteredDepts
        };
      }).filter(org => 
        org.name.toLowerCase().includes(keyword) || org.departments.length > 0
      );
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.initData();
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false);
      }
    }
  },
  methods: {
    async initData() {
      // 初始化数据和已选成员
      this.loadOrgData();
      this.selectedMembers = [...this.currentMembers];
      
      // 根据已选成员更新选中状态
      this.updateCheckStatus();
    },
    
    loadOrgData() {
      // 模拟数据，实际应该从API获取
      this.organizations = [
        {
          id: '1',
          name: '深圳市科技创新有限公司',
          memberCount: 108,
          checked: false,
          departments: [
            {
              id: 'd1',
              name: '技术研发中心',
              memberCount: 42,
              checked: false,
              members: [
                { id: 'm1', name: 'ABC', checked: false },
                { id: 'm2', name: 'DEF', checked: false },
                { id: 'm3', name: 'HIJ', checked: false }
              ]
            },
            {
              id: 'd2',
              name: '产品设计部',
              memberCount: 16,
              checked: false,
              members: [
                { id: 'm4', name: 'ABC', checked: false },
                { id: 'm5', name: 'DEF', checked: false }
              ]
            },
            {
              id: 'd3',
              name: '市场营销部',
              memberCount: 28,
              checked: false,
              members: [
                { id: 'm6', name: 'ABC', checked: false }
              ]
            },
            {
              id: 'd4',
              name: '人力资源部',
              memberCount: 12,
              checked: false,
              members: [
                { id: 'm7', name: 'ABC', checked: false }
              ]
            },
            {
              id: 'd5',
              name: '财务部',
              memberCount: 10,
              checked: false,
              members: [
                { id: 'm8', name: 'ABC', checked: false },
                { id: 'm9', name: 'DEF', checked: false },
                { id: 'm10', name: 'HIJ', checked: false }
              ]
            }
          ]
        }
      ];
    },
    
    updateCheckStatus() {
      // 根据selectedMembers更新选中状态
      const selectedMemberIds = this.selectedMembers.map(m => m.id);
      
      this.organizations.forEach(org => {
        let orgHasAllSelected = true;
        
        org.departments.forEach(dept => {
          let deptHasAllSelected = true;
          
          dept.members.forEach(member => {
            member.checked = selectedMemberIds.includes(member.id);
            if (!member.checked) {
              deptHasAllSelected = false;
            }
          });
          
          dept.checked = deptHasAllSelected && dept.members.length > 0;
          if (!dept.checked) {
            orgHasAllSelected = false;
          }
        });
        
        org.checked = orgHasAllSelected && org.departments.length > 0;
      });
    },
    
    handleOrgCheckChange(org, checked) {
      // 组织选中状态改变，更新部门和成员的选中状态
      org.departments.forEach(dept => {
        dept.checked = checked;
        dept.members.forEach(member => {
          member.checked = checked;
          this.updateSelectedMembers(member, checked);
        });
      });
    },
    
    handleDeptCheckChange(dept, checked) {
      // 部门选中状态改变，更新成员的选中状态
      dept.members.forEach(member => {
        member.checked = checked;
        this.updateSelectedMembers(member, checked);
      });
      
      // 更新组织的选中状态
      this.updateOrgCheckStatus(dept);
    },
    
    handleMemberCheckChange(member, checked) {
      // 成员选中状态改变
      this.updateSelectedMembers(member, checked);
      
      // 找到对应的部门和组织，更新它们的选中状态
      this.organizations.forEach(org => {
        org.departments.forEach(dept => {
          if (dept.members.some(m => m.id === member.id)) {
            this.updateDeptCheckStatus(dept);
            this.updateOrgCheckStatus(dept);
          }
        });
      });
    },
    
    updateSelectedMembers(member, checked) {
      if (checked) {
        // 添加到选中列表
        if (!this.selectedMembers.some(m => m.id === member.id)) {
          this.selectedMembers.push({
            id: member.id,
            name: member.name
          });
        }
      } else {
        // 从选中列表移除
        this.selectedMembers = this.selectedMembers.filter(m => m.id !== member.id);
      }
    },
    
    updateDeptCheckStatus(dept) {
      // 更新部门的选中状态，当所有成员都被选中时，部门也被选中
      dept.checked = dept.members.length > 0 && dept.members.every(m => m.checked);
    },
    
    updateOrgCheckStatus(dept) {
      // 更新组织的选中状态
      this.organizations.forEach(org => {
        if (org.departments.some(d => d.id === dept.id)) {
          org.checked = org.departments.length > 0 && org.departments.every(d => d.checked);
        }
      });
    },
    
    removeMember(member) {
      // 从选中列表中移除成员
      this.selectedMembers = this.selectedMembers.filter(m => m.id !== member.id);
      
      // 更新成员的选中状态
      this.organizations.forEach(org => {
        org.departments.forEach(dept => {
          dept.members.forEach(m => {
            if (m.id === member.id) {
              m.checked = false;
            }
          });
          this.updateDeptCheckStatus(dept);
        });
        this.updateOrgCheckStatus(org.departments[0]);
      });
    },
    
    handleClose() {
      this.dialogVisible = false;
      this.searchKeyword = '';
    },
    
    handleSave() {
      this.$emit('save', [...this.selectedMembers]);
      this.handleClose();
    }
  }
};
</script>

<style scoped lang="scss">
.member-dialog-content {
  .search-input {
    margin-bottom: 20px;
    width: 100%;
  }
  
  .member-selection-container {
    display: flex;
    gap: 20px;
    height: 400px;
    
    .member-tree-panel {
      flex: 2;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 10px;
      overflow-y: auto;
      
      .org-item {
        margin-bottom: 10px;
        
        .org-header {
          display: flex;
          align-items: center;
          font-weight: bold;
          margin-bottom: 10px;
          
          .org-name {
            flex: 1;
          }
          
          .org-count {
            margin-right: 10px;
            color: #909399;
            font-size: 12px;
          }
        }
        
        .dept-item {
          margin-left: 20px;
          margin-bottom: 10px;
          
          .dept-header {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            
            .dept-name {
              margin-left: 5px;
              flex: 1;
            }
            
            .dept-count {
              margin-right: 10px;
              color: #909399;
              font-size: 12px;
            }
          }
          
          .members-list {
            margin-left: 20px;
            
            .member-item {
              display: flex;
              align-items: center;
              padding: 5px 0;
              
              .member-name {
                margin-left: 5px;
                flex: 1;
              }
            }
          }
        }
      }
    }
    
    .selected-members-panel {
      flex: 1;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      
      .panel-header {
        padding: 10px;
        border-bottom: 1px solid #e4e7ed;
        font-weight: bold;
        background-color: #f5f7fa;
      }
      
      .selected-members-list {
        flex: 1;
        padding: 10px;
        overflow-y: auto;
        
        .selected-member-item {
          display: flex;
          align-items: center;
          padding: 5px 0;
          
          .selected-member-name {
            margin-left: 5px;
            flex: 1;
          }
          
          .remove-icon {
            cursor: pointer;
            color: #909399;
            
            &:hover {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
