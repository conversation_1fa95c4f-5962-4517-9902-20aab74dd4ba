<template>
  <el-dialog
    v-model="dialogVisible"
    title="模型列表"
    width="800px"
    :before-close="handleClose"
    class="show-model-dialog"
  >
    <div class="model-list-container" v-loading="loading">
      <div class="model-list">
        <div class="model-item" v-for="model in modelList" :key="model.id">
          <div class="model-info">
            <img :src="model.icon || iconUrl + provider?.icon_small?.zh_Hans" alt="model-icon" class="model-icon" />
            <div class="model-details">
              <div class="model-name">{{ model.model }}</div>
              <div class="model-attrs">
                <span class="model-type">{{ model.model_type.toLocaleUpperCase() }}</span>
                <span v-if="model.model_properties.mode">{{ model.model_properties.mode.toLocaleUpperCase() }}</span>
                <span class="model-size" v-if="model.model_properties.context_size"
                  >{{ Math.floor(model.model_properties.context_size / 1000) }}K</span
                >
              </div>
            </div>
          </div>
          <div class="model-action">
            <el-switch :model-value="model.status === 'active'" @click="() => handleStatusChange(model)" />
          </div>
        </div>
      </div>
      <el-empty description="暂无数据" v-if="modelList.length === 0" />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import type { ShowModelDialogProps, ModelInfo } from "../types";
import { get, patch } from "@/utils/request";
import { ElMessage } from "element-plus";

const props = defineProps<ShowModelDialogProps>();
const emit = defineEmits(["update:visible"]);

const iconUrl = import.meta.env.VITE_API_ICON_URL;
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit("update:visible", value),
});

const loading = ref(false);
const modelList = ref<ModelInfo[]>([]);

// 获取模型列表
const getModelList = async () => {
  if (!props.provider) return;

  try {
    loading.value = true;
    const res = await get<{ data: ModelInfo[] }>(
      `/workspaces/current/model-providers/${props.provider.provider}/models`
    );
    modelList.value = res.data;
  } catch (error) {
    console.error("获取模型列表失败:", error);
    ElMessage.error("获取模型列表失败");
  } finally {
    loading.value = false;
  }
};

// 更新模型状态
const handleStatusChange = async (model: ModelInfo) => {
  if (!props.provider) return;

  const newStatus = model.status === "active" ? "inactive" : "active";
  const oldStatus = model.status;
  try {
    loading.value = true;
    await patch(
      `/workspaces/current/model-providers/${props.provider.provider}/models/${
        oldStatus === "active" ? "disable" : "enable"
      }`,
      {
        model: model.model,
        model_type: model.model_type,
      }
    );
    model.status = newStatus;
  } catch (error) {
  } finally {
    loading.value = false;
  }
};

const handleClose = () => {
  dialogVisible.value = false;
  modelList.value = []; // 关闭弹窗时清空列表
};

watch(
  () => props.visible,
  newVal => {
    if (newVal && props.provider) {
      getModelList();
    }
  }
);
</script>

<style scoped lang="scss">
.show-model-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
    max-height: 70vh;
  }
}

.model-list-container {
  max-height: calc(70vh - 108px); // 减去头部和底部的高度
  overflow-y: auto;
}

.model-list {
  padding: 20px;

  .model-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f9fafb;
    }
  }
}

.model-info {
  display: flex;
  align-items: center;

  .model-icon {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    margin-right: 16px;
  }

  .model-details {
    .model-name {
      font-size: 15px;
      font-weight: 500;
      color: #333;
      margin-bottom: 8px;
    }

    .model-attrs {
      display: flex;
      align-items: center;
      gap: 12px;
      color: #666;
      font-size: 13px;

      span {
        position: relative;

        &:not(:last-child)::after {
          content: "";
          position: absolute;
          right: -6px;
          top: 50%;
          transform: translateY(-50%);
          width: 1px;
          height: 12px;
          background-color: #e5e7eb;
        }
      }
    }
  }
}

.model-action {
  :deep(.el-switch) {
    --el-switch-on-color: #4972f7;
  }
}
</style>
