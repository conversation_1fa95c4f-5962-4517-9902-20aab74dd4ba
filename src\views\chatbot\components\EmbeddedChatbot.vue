<template>
  <div class="qa-container">
    <div class="chat-area">
      <!-- 消息列表区域 -->
      <chat-messages
        :messages="currentChatMessages"
        :opening-statement="openingStatement"
        v-if="!showConfigPanel && !messagesLoading"
      />

      <!-- 加载状态 -->
      <div v-else-if="messagesLoading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>

      <!-- 配置面板 - 在开始聊天前显示 -->
      <div v-else-if="showConfigPanel" class="config-panel-container">
        <config-panel @start-chat="handleStartChat" />
      </div>
    </div>

    <!-- 推荐问题区域 -->
    <div class="suggest-box" v-if="suggestedQuestions.length > 0 && currentChatMessages.length > 0 && !showConfigPanel && !messagesLoading">
      <el-divider>试着问问</el-divider>
      <ul class="suggest-list">
        <el-button
          plain
          size="small"
          v-for="(item, index) in suggestedQuestions"
          :key="index"
          @click="handleSuggestedQuestion(item)"
          >{{ item }}</el-button
        >
      </ul>
    </div>

    <!-- 底部输入区域 -->
    <message-input
      :disabled="showConfigPanel || messagesLoading"
      @send="sendMessage"
    />
  </div>
</template>

<script lang="ts">
import { inject, computed, defineComponent } from 'vue';
import { Loading } from '@element-plus/icons-vue';
import { CHATBOT_CONTEXT_KEY } from './ChatbotProvider.vue';
import ConfigPanel from './ConfigPanel.vue';
import ChatMessages from './ChatMessages.vue';
import MessageInput from './MessageInput.vue';

export default defineComponent({
  name: 'EmbeddedChatbot',
  components: {
    ConfigPanel,
    ChatMessages,
    MessageInput,
    Loading
  },
  setup() {
    // 注入聊天上下文
    const context = inject(CHATBOT_CONTEXT_KEY) as any;

    // 从上下文中获取需要的数据和方法
    const currentChatMessages = computed(() => context.currentChatMessages.value);
    const showConfigPanel = computed(() => context.showConfigPanel.value);
    const messagesLoading = computed(() => context.messagesLoading.value);
    const suggestedQuestions = computed(() => context.suggestedQuestions.value);
    const openingStatement = computed(() => context.appParams.value?.opening_statement || '');

    // 从上下文中获取方法
    const handleStartChat = () => context.handleStartChat();
    const sendMessage = (content: string) => context.sendMessage(content);

    // 处理推荐问题点击
    const handleSuggestedQuestion = (question: string) => {
      if (question) {
        sendMessage(question);
      }
    };

    return {
      currentChatMessages,
      showConfigPanel,
      messagesLoading,
      suggestedQuestions,
      openingStatement,
      handleStartChat,
      sendMessage,
      handleSuggestedQuestion
    };
  }
});
</script>

<style scoped>
.qa-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f9fafb;
  overflow: hidden; /* 防止内容溢出 */
}

.chat-area {
  flex: 1;
  overflow-y: auto;
  position: relative;
  padding-bottom: 20px; /* 添加底部内边距，确保最后一条消息不被遮挡 */
}

.config-panel-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.loading-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 16px;
  gap: 16px;
}

.loading-container .el-icon {
  font-size: 24px;
}

@media (max-width: 768px) {
  .config-panel-container {
    padding: 12px;
  }
}
/* 推荐问题样式 */
.suggest-box {
  padding: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9fafb;
  z-index: 10;
}

.suggest-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  margin-top: 8px;
  padding: 0;
  list-style: none;
}

:deep(.el-divider__text) {
  background-color: transparent;
  font-size: 14px;
  color: #909399;
}
</style>
