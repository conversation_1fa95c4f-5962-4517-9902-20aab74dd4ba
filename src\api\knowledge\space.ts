import { get, post, put, del, patch } from '@/utils/request';

// 知识空间相关接口类型定义
export interface Space {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  tenant_id?: string;
  dataset_count?: number;
  created_by?: string;
  created_at?: number;
  updated_by?: string;
  updated_at?: number;
  [key: string]: any;
}

export interface SpaceListResponse {
  data: Space[];
  total: number;
}

export interface SpaceListParams {
  page?: number;
  limit?: number;
  keyword?: string;
  [key: string]: any;
}

export interface CreateSpaceParams {
  name: string;
  description?: string;
  logo?: string;
  [key: string]: any;
}

export interface UpdateSpaceParams {
  name: string;
  description?: string;
  logo?: string;
  [key: string]: any;
}

export interface SpaceMember {
  account_id: string;
  role: 'admin' | 'member';
}

export interface SpaceMembersResponse {
  members: SpaceMember[];
}

/**
 * 获取知识空间列表
 * @param params 查询参数
 * @returns 知识空间列表
 */
export const fetchSpaceList = (params: SpaceListParams = {}): Promise<SpaceListResponse> => {
  return get('/dataset-spaces', params);
};

/**
 * 创建知识空间
 * @param data 创建参数
 * @returns 创建结果
 */
export const createSpace = (data: CreateSpaceParams): Promise<{ data: Space }> => {
  return post('/dataset-spaces', data);
};

/**
 * 更新知识空间
 * @param id 知识空间ID
 * @param data 更新参数
 * @returns 更新结果
 */
export const updateSpace = (id: string, data: UpdateSpaceParams): Promise<{ data: Space }> => {
  return patch(`/dataset-spaces/${id}`, data);
};

/**
 * 删除知识空间
 * @param id 知识空间ID
 * @returns 删除结果
 */
export const deleteSpace = (id: string): Promise<any> => {
  return del(`/dataset-spaces/${id}`);
};

/**
 * 获取知识空间详情
 * @param id 知识空间ID
 * @returns 知识空间详情
 */
export const getSpaceDetail = (id: string): Promise<{ data: Space }> => {
  return get(`/dataset-spaces/${id}`);
};

/**
 * 获取知识空间成员列表
 * @param id 知识空间ID
 * @returns 成员列表
 */
export const getSpaceMembers = (id: string): Promise<SpaceMembersResponse> => {
  return get(`/dataset-spaces/${id}/members`);
};

/**
 * 添加知识空间成员
 * @param id 知识空间ID
 * @param accountId 成员ID
 * @param role 角色，默认为member
 * @returns 添加结果
 */
export const addSpaceMembers = (id: string, accountId: string, role: string = 'member'): Promise<any> => {
  return post(`/dataset-spaces/${id}/members`, { account_id: accountId, role });
};

/**
 * 更新成员角色
 * @param id 知识空间ID
 * @param accountId 成员ID
 * @param role 角色
 * @returns 更新结果
 */
export const updateMemberRole = (id: string, accountId: string, role: string): Promise<any> => {
  return patch(`/dataset-spaces/${id}/members/${accountId}`, { role });
};

/**
 * 删除成员
 * @param id 知识空间ID
 * @param accountId 成员ID
 * @returns 删除结果
 */
export const removeMember = (id: string, accountId: string): Promise<any> => {
  return del(`/dataset-spaces/${id}/members/${accountId}`);
};
