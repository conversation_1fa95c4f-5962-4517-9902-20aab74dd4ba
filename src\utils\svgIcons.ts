import { markRaw } from "vue";
import RobotSvg from "@/assets/icons/robot.svg?component";
import RobotDefaultSvg from "@/assets/icons/robot_default.svg?component";
import ToolboxSvg from "@/assets/icons/toolbox.svg?component";
import WorkflowSvg from "@/assets/icons/workflow.svg?component";
import ChatflowSvg from "@/assets/icons/chatflow.svg?component";
import { createColorableIcon } from "./svgProcessor";

// 创建可继承颜色的SVG图标
const ColorableRobotSvg = createColorableIcon(RobotSvg);
const ColorableRobotDefaultSvg = createColorableIcon(RobotDefaultSvg);
const ColorableToolboxSvg = createColorableIcon(ToolboxSvg);
const ColorableWorkflowSvg = createColorableIcon(WorkflowSvg);
const ColorableChatflowSvg = createColorableIcon(ChatflowSvg);

/**
 * 导出所有SVG图标
 * 所有图标都是可继承颜色的，可以通过以下方式使用：
 * 1. 继承SVG原始颜色：<SvgIcon :icon="SvgIcons.Robot" />
 * 2. 继承父元素颜色：<div style="color: red"><SvgIcon :icon="SvgIcons.Robot" /></div>
 * 3. 指定颜色：<SvgIcon :icon="SvgIcons.Robot" color="blue" />
 */
export const SvgIcons = {
  // SVG图标
  Robot: markRaw(ColorableRobotSvg),
  RobotDefault: markRaw(ColorableRobotDefaultSvg),
  Toolbox: markRaw(ColorableToolboxSvg),
  Workflow: markRaw(ColorableWorkflowSvg),
  Chatflow: markRaw(ColorableChatflowSvg),

  // 可以在这里添加更多SVG图标
};

// 导出图标类型
export type SvgIconName = keyof typeof SvgIcons;
