<template>
  <div class="models-container">
    <div class="models-header">
      <div class="models-title">模型市场</div>
      <ModelSettingsPopover />
    </div>
    <div class="models-desc">发现和对接优质的AI模型能力</div>
    <div class="models-filter">
      <ul class="models-filter-list">
        <li
          class="models-filter-item"
          :class="{ active: item.id === activeNav }"
          v-for="item in navList"
          :key="item.id"
          @click="handleNavClick(item)"
        >
          {{ item.name }}
        </li>
      </ul>
      <div class="models-filter-input">
        <el-input v-model="keyword" placeholder="搜索模型" :prefix-icon="Search" size="large" clearable />
      </div>
    </div>
    <ul class="providers-list">
      <li
        class="providers-item"
        :class="{ unactive: provider.custom_configuration?.status !== 'active' }"
        v-for="provider in filterProviders"
        :key="provider.id"
      >
        <div class="providers-item-content">
          <div class="providers-item-header">
            <img class="item-header-img" :src="iconUrl + provider.icon_small?.zh_Hans" alt="logo" />
            <div class="item-header-box">
              <div class="item-header-title">{{ provider.label?.zh_Hans }}</div>
              <div class="item-header-icon">
                <el-tag
                  type="primary"
                  effect="plain"
                  v-for="(tag, tagIndex) in provider.supported_model_types"
                  :key="tagIndex"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
            <div
              class="item-header-status"
              :class="[provider.custom_configuration?.status === 'active' ? 'success' : 'warning']"
            >
              <el-icon :size="16" color="#16A34A" v-if="provider.custom_configuration?.status === 'active'"
                ><SuccessFilled
              /></el-icon>
              <el-icon :size="16" color="#C18404" v-else><WarnTriangleFilled /></el-icon>
              <div class="status-text">
                {{ provider.custom_configuration?.status === "active" ? "已激活" : "待配置" }}
              </div>
            </div>
          </div>
          <div
            class="providers-item-middle flex-h"
            :class="{ active: provider.custom_configuration?.status !== 'active' }"
          >
            <el-icon :size="20" color="#C18404"><InfoFilled /></el-icon>
            <div class="providers-item-middle-text">请配置 API 密钥，添加模型。</div>
          </div>
          <div
            class="show-model"
            :class="{ active: provider.custom_configuration?.status === 'active' }"
            @click="openShowModel(provider)"
          >
            <div>显示模型</div>
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
        <div class="providers-item-footer flex-h" :class="{ active: provider?.configurate_methods?.length === 2 }">
          <el-button
            plain
            block
            class="footer-button"
            size="large"
            :icon="Tools"
            v-if="provider.configurate_methods.includes('predefined-model')"
            @click="openConfigDrawer(provider)"
            >设置</el-button
          >
          <el-button
            plain
            block
            class="footer-button"
            size="large"
            :icon="Plus"
            v-if="provider.configurate_methods.includes('customizable-model')"
            @click="openModelDrawer(provider)"
            >添加模型</el-button
          >
        </div>
      </li>
    </ul>
    <el-empty description="暂无数据" v-if="filterProviders.length === 0" />
    <div class="other-title">其他模型</div>
    <ul class="providers-list grid-columns-4">
      <li class="providers-item unactive" v-for="(plugin, index) in pluginsList" :key="index">
        <div class="providers-item-content">
          <div class="providers-item-header">
            <img
              class="item-header-img"
              :src="`https://marketplace.dify.ai/api/v1/plugins/langgenius/${plugin?.model?.provider}/icon`"
              alt="logo"
            />
            <div class="item-header-box">
              <div class="item-header-title">
                {{ plugin.label?.zh_Hans ? plugin.label?.zh_Hans : plugin.label?.en_US }}
              </div>
            </div>
          </div>
          <div class="plugins-item-middle">
            {{
              plugin?.model?.description?.zh_Hans
                ? plugin?.model?.description?.zh_Hans
                : plugin?.model?.description?.en_US
            }}
          </div>
        </div>
        <div class="providers-item-footer flex-h">
          <el-button block class="footer-button" size="large" type="primary" @click="openInstall(plugin)"
            >安装</el-button
          >
        </div>
      </li>
    </ul>
    <el-empty description="暂无数据" v-if="pluginsList.length === 0" />

    <!-- 安装弹窗 -->
    <InstallDialog v-model:visible="installDialogVisible" :plugin="selectedPlugin" @success="handleInstallSuccess" />

    <!-- 配置抽屉 -->
    <ConfigDrawer v-model="configDrawerVisible" :currentProvider="currentProvider" @refresh="getProviders" />

    <!-- 模型配置抽屉 -->
    <ModelDrawer v-model="modelDrawerVisible" :currentProvider="currentProvider" @refresh="getProviders" />

    <!-- 显示模型弹窗 -->
    <ShowModelDialog v-model:visible="showModelDialogVisible" :provider="selectedProvider" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import {
  Search,
  SuccessFilled,
  WarnTriangleFilled,
  InfoFilled,
  Tools,
  Plus,
  ArrowRight,
} from "@element-plus/icons-vue";
import type { ProviderRecord, ApiProviderResponse, PluginRecord } from "./types";
import { get } from "@/utils/request";
import axios from "axios";
import { getToken } from "@/utils/user";
import InstallDialog from "./components/InstallDialog.vue";
import ConfigDrawer from "./components/ConfigDrawer.vue";
import ModelDrawer from "./components/ModelDrawer.vue";
import ShowModelDialog from "./components/ShowModelDialog.vue";
import ModelSettingsPopover from "./components/ModelSettingsPopover.vue";

const keyword = ref("");
const iconUrl = import.meta.env.VITE_API_ICON_URL;
const navList = [
  { id: 1, name: "全部模型" },
  { id: 2, name: "已对接" },
];
const activeNav = ref(1);
const handleNavClick = (item: { id: number; name: string }) => {
  activeNav.value = item.id;
};

// 模型列表
const providers = ref<ProviderRecord[]>([]);
// 其他模型列表
const pluginsList = ref<PluginRecord[]>([]);
const includePlugins = [
  "Ollama",
  "Xorbits Inference",
  "深度求索",
  "通义千问",
  "硅基流动",
  "火山方舟",
  "智谱AI",
  "月之暗面",
  "腾讯混元",
  "文心一言",
];

// 安装弹窗相关
const installDialogVisible = ref(false);
const selectedPlugin = ref<PluginRecord | null>(null);

// 配置抽屉相关
const configDrawerVisible = ref(false);
const currentProvider = ref<ProviderRecord | null>(null);

// 模型配置抽屉相关
const modelDrawerVisible = ref(false);

// 显示模型弹窗相关
const showModelDialogVisible = ref(false);
const selectedProvider = ref<ProviderRecord | null>(null);

// 已对接模型过滤
const filterProviders = computed(() => {
  if (activeNav.value === 1) {
    return providers.value.filter(provider => provider.label?.zh_Hans?.includes(keyword.value));
  }
  return providers.value.filter(
    provider => provider?.custom_configuration?.status === "active" && provider.label?.zh_Hans?.includes(keyword.value)
  );
});

// 获取模型列表
const getProviders = async () => {
  const res = await get<ApiProviderResponse>("/workspaces/current/model-providers");
  // 对数据进行排序，active状态的排在前面
  const sortedData = [...res.data].sort((a, b) => {
    const statusA = a.custom_configuration?.status === "active" ? 1 : 0;
    const statusB = b.custom_configuration?.status === "active" ? 1 : 0;
    return statusB - statusA;
  });
  providers.value = sortedData;
};

// 获取其他模型列表
const getPluginsList = async () => {
  let res = await axios.post(
    "https://marketplace.dify.ai/api/v1/plugins/search/advanced",
    {
      query: "",
      category: "model",
      page: 1,
      page_size: 1000,
      sort_by: "install_count",
      sort_order: "DESC",
      type: "plugin",
      exclude: providers.value.map(item => {
        return item.provider.slice(0, item.provider.lastIndexOf("/"));
      }),
    },
    {
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Content-Type": "application/json;charset=utf-8",
      },
    }
  );
  pluginsList.value = res.data.data.plugins.filter((item: PluginRecord) => {
    return includePlugins.includes(item.label.zh_Hans || item.label.en_US);
  });
};

// 打开安装页面
const openInstall = (plugin: PluginRecord) => {
  selectedPlugin.value = plugin;
  installDialogVisible.value = true;
};

// 安装成功回调
const handleInstallSuccess = () => {
  getProviders();
};

const openConfigDrawer = (provider: ProviderRecord) => {
  currentProvider.value = provider;
  configDrawerVisible.value = true;
};

const openModelDrawer = (provider: ProviderRecord) => {
  currentProvider.value = provider;
  modelDrawerVisible.value = true;
};

// 打开显示模型页面
const openShowModel = (provider: ProviderRecord) => {
  selectedProvider.value = provider;
  showModelDialogVisible.value = true;
};

// 生命周期
onMounted(async () => {
  await getProviders();
  getPluginsList(); // 同时更新插件列表
});
</script>

<style scoped lang="scss">
.models-container {
  padding: 32px 24px;
}

.models-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  .models-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
  }
}

.models-desc {
  margin: 20px 0 40px 0;
  font-size: 18px;
  color: #666;
}
.models-filter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .models-filter-input {
    width: 300px;
  }
}
.models-filter-list {
  display: flex;
  align-items: center;
  .models-filter-item {
    padding: 12px 20px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 20px;
    color: #676f83;
    &.active {
      background-color: #eff6ff;
      color: #4972f7;
    }
  }
}
.providers-list {
  display: grid;
  grid-gap: 20px;
  grid-template-columns: repeat(3, 1fr);
  margin-top: 20px;
  .providers-item {
    padding: 20px;
    background-color: #f9faff;
    border-radius: 10px;
    border: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    .providers-item-content {
      flex: 1;
      overflow: hidden;
    }
    &.unactive {
      background-color: #fff;
    }
  }
}
.providers-item-header {
  display: flex;
  align-items: center;
  .item-header-img {
    width: 40px;
    height: 40px;
    overflow: hidden;
  }
  .item-header-box {
    flex: 1;
    overflow: hidden;
    margin: 0 20px;
    .item-header-title {
      font-size: 18px;
      font-weight: 600;
    }
    .item-header-icon {
      margin-top: 10px;
      span {
        margin-right: 4px;
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
  .item-header-status {
    display: flex;
    align-items: center;
    font-size: 14px;
    padding: 6px;
    border-radius: 8px;
    &.success {
      color: #16a34a;
      background-color: rgba(22, 163, 74, 0.1);
    }
    &.warning {
      color: #c18404;
      background-color: rgba(193, 132, 4, 0.1);
    }
    .status-text {
      margin-left: 4px;
    }
  }
}
.providers-item-middle {
  margin: 20px 0;
  opacity: 0;
  .providers-item-middle-text {
    color: #c18404;
    margin-left: 10px;
  }
  &.active {
    opacity: 1;
  }
}
.providers-item-footer {
  justify-content: space-between;
  .footer-button {
    width: 100%;
  }
  &.active {
    .footer-button {
      width: 50%;
    }
  }
}
.other-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 40px 0;
}
.grid-columns-4 {
  grid-template-columns: repeat(4, 1fr);
}
.plugins-item-middle {
  margin: 30px 0;
  font-size: 14px;
  color: #676f83;
  line-height: 20px;
}
.show-model {
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  color: #676f83;
  margin-bottom: 10px;
  cursor: pointer;
  padding: 10px;
  border-radius: 4px;
  visibility: hidden;
  &:hover {
    background-color: #c8ceda33;
  }
  &.active {
    visibility: visible;
  }
}
</style>
