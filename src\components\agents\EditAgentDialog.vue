<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑智能体"
    width="600px"
    :close-on-click-modal="false"
    @closed="handleDialogClosed"
  >
    <div class="edit-agent-form">
      <div class="icon-upload-container">
        <el-upload
          class="icon-upload"
          :show-file-list="false"
          :before-upload="beforeUpload"
          :http-request="customUpload"
          accept="image/*"
        >
          <div v-if="!form.icon" class="upload-placeholder">
            <el-icon class="upload-icon"><Setting /></el-icon>
            <div class="upload-text">请上传图标</div>
          </div>
          <div v-else class="preview-uploaded">
            <el-image :src="form.iconUrl" fit="cover" class="uploaded-icon"></el-image>
            <div class="upload-hover-mask">
              <el-icon><Edit /></el-icon>
            </div>
          </div>
        </el-upload>
        <el-progress
          v-if="uploadProgress > 0 && uploadProgress < 100"
          :percentage="uploadProgress"
          type="circle"
          :width="60"
          class="upload-progress"
        />
      </div>

      <div class="form-container">
        <div class="form-item">
          <div class="form-label">名称</div>
          <el-input
            v-model="form.name"
            placeholder="请输入名称"
            maxlength="30"
            show-word-limit
          />
        </div>

        <div class="form-item">
          <div class="form-label">描述</div>
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入描述"
            maxlength="200"
            show-word-limit
            rows="5"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, defineProps, defineEmits } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import { Setting, Edit } from '@element-plus/icons-vue';
import { updateAgent, putAgentDetail, uploadFile } from '@/api/agents';
import type { UploadRawFile } from 'element-plus';
import type { AppsRecord } from '@/views/agents/types';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  agent: {
    type: Object as () => AppsRecord,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'refresh']);

const dialogVisible = ref(props.visible);
const submitting = ref(false);
const uploadProgress = ref(0);

// 表单数据
const form = reactive({
  name: '',
  description: '',
  icon: '',
  icon_type: 'image',
  iconUrl: '',
  use_icon_as_answer_icon: false 
});

// 监听visible属性变化
watch(() => props.visible, (newVal: boolean) => {
  dialogVisible.value = newVal;
}, { immediate: true });

// 监听agent属性变化
watch(() => props.agent, (newVal: AppsRecord) => {
  if (dialogVisible.value && newVal) {
    // 初始化表单数据
    form.name = newVal.name || '';
    form.description = newVal.description || '';
    form.icon = newVal.icon || '';
    form.iconUrl = newVal.icon_url ? import.meta.env.VITE_API_ICON_URL + newVal.icon_url : '';
  }
}, { immediate: true, deep: true });

// 监听对话框关闭
watch(dialogVisible, (newVal: boolean) => {
  emit('update:visible', newVal);
});

// 对话框关闭处理
const handleDialogClosed = () => {
  // 重置表单
  form.name = '';
  form.description = '';
  form.icon = '';
  form.iconUrl = '';
  uploadProgress.value = 0;
};

// 上传前验证
const beforeUpload = (file: UploadRawFile) => {
  const isImage = file.type.startsWith('image/');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!');
    return false;
  }
  return true;
};

// 自定义上传
const customUpload = async (options: any) => {
  const { file } = options;
  uploadProgress.value = 0;

  try {
    const response = await uploadFile(file, (progress: number) => {
      uploadProgress.value = progress;
    });

    console.log('上传响应:', response.data);

    // 文件上传成功，只保存ID用于提交表单
    form.icon = response.data.id;

    // 使用 File 对象的 URL 用于预览
    form.iconUrl = URL.createObjectURL(file);

    ElMessage.success('图标上传成功');
  } catch (error) {
    console.error('图标上传失败', error);
    ElMessage.error('图标上传失败，请重试');
  } finally {
    setTimeout(() => {
      uploadProgress.value = 0;
    }, 500);
  }
};

// 提交表单
const handleSubmit = async () => {
  // 表单验证
  if (!form.name.trim()) {
    ElMessage.warning('请输入智能体名称');
    return;
  }

  submitting.value = true;
  const loading = ElLoading.service({
    lock: true,
    text: '保存中...',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  try {
    // 构建更新参数
    const updateParams: any = {
      name: form.name,
      description: form.description,
      icon_type: 'image',
      use_icon_as_answer_icon: false
    };

    // 如果有上传图标，则添加图标ID
    if (form.icon) {
      updateParams.icon = form.icon;
    }

    // 调用更新API
    await putAgentDetail(props.agent.id, updateParams);

    ElMessage.success('智能体更新成功');
    dialogVisible.value = false;
    emit('refresh'); // 通知父组件刷新列表
  } catch (error) {
    console.error('更新智能体失败:', error);
    ElMessage.error('更新失败，请重试');
  } finally {
    submitting.value = false;
    loading.close();
  }
};
</script>

<style lang="scss" scoped>
.edit-agent-form {
  padding: 20px 0;

  .icon-upload-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 24px;
    position: relative;

    .icon-upload {
      width: 100px;
      height: 100px;

      :deep(.el-upload) {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }

      .upload-placeholder {
        width: 100%;
        height: 100%;
        border: 1px dashed #d9d9d9;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: #fafafa;

        .upload-icon {
          font-size: 28px;
          color: #8c8c8c;
          margin-bottom: 8px;
        }

        .upload-text {
          font-size: 12px;
          color: #8c8c8c;
        }
      }

      .preview-uploaded {
        width: 100%;
        height: 100%;
        position: relative;
        border-radius: 8px;
        overflow: hidden;

        .uploaded-icon {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .upload-hover-mask {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          opacity: 0;
          transition: opacity 0.3s;

          .el-icon {
            font-size: 24px;
            color: #fff;
          }
        }

        &:hover .upload-hover-mask {
          opacity: 1;
        }
      }
    }

    .upload-progress {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 50%;
      padding: 5px;
    }
  }

  .form-container {
    .form-item {
      margin-bottom: 20px;

      .form-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
