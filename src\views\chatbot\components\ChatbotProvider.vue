<template>
  <div class="chatbot-provider">
    <slot v-if="initialized"></slot>
    <div v-else class="loading-container">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
  </div>
</template>

<script lang="ts">
import { ref, reactive, provide, onMounted, computed, defineComponent, watch, nextTick } from 'vue';
import { Loading } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getTokenFromPath, chatbotConversationId } from '@/utils/chatbot';
import { fetchSiteInfo, fetchChatParams, fetchChatMeta, fetchConversations, sendChatMessage, fetchChatMessages, fetchSuggestedQuestions } from '@/api/chatbot';

// 提供给子组件的上下文键名
export const CHATBOT_CONTEXT_KEY = Symbol('chatbotContext');

// 消息类型定义
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  rawAnswer?: string; // 用于存储原始内容
  timestamp?: number;
  isLoading?: boolean;
  status?: string; // 添加状态字段：pending, normal, failed
  feedback?: { rating: 'like' | 'dislike' };
  metadata?: Record<string, any>;
  isOpening?: boolean; // 标记是否为开场白消息
}

// 会话类型定义
interface Conversation {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  pinned?: boolean;
}

// 应用数据类型
interface AppData {
  site?: {
    title?: string;
    description?: string;
  };
  [key: string]: any;
}

export default defineComponent({
  name: 'ChatbotProvider',
  components: {
    Loading
  },
  props: {
    readyToInit: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    // 初始化状态
    const initialized = ref(false);
    const appInfoLoading = ref(true);
    const appInfoError = ref(false);

    // 聊天机器人基础数据
    const appData = ref<AppData>({});
    const appParams = ref<Record<string, any>>({});
    const appMeta = ref<Record<string, any>>({});
    const chatToken = ref(getTokenFromPath());

    // 会话和消息数据
    const currentConversationId = ref(chatbotConversationId(chatToken.value));
    const parentMessageId = ref<string | null>(null);
    const conversationList = ref<Conversation[]>([]);
    const pinnedConversationList = ref<Conversation[]>([]);
    const currentChatMessages = ref<ChatMessage[]>([]);
    const messagesLoading = ref(false);
    const isMobile = ref(window.innerWidth < 768);

    // 配置面板状态 - 设置为false以直接显示聊天界面
    const showConfigPanel = ref(false);
    const newConversationInputs = reactive<Record<string, any>>({});
    const currentChatInstanceRef = ref<{ handleStop: () => void }>({ handleStop: () => {} });

    // 计算属性
    const currentConversationItem = computed(() => {
      return [...conversationList.value, ...pinnedConversationList.value].find(item => item.id === currentConversationId.value);
    });

    const chatReady = computed(() => {
      return !showConfigPanel.value || (currentConversationId.value !== '');
    });

    // 创建新会话
    const handleNewConversation = () => {
      currentChatInstanceRef.value.handleStop();

      // 清空当前会话ID
      currentConversationId.value = '';
      chatbotConversationId(chatToken.value, '');

      // 显示配置面板
      showConfigPanel.value = true;

      // 清空消息列表
      currentChatMessages.value = [];

      // 清空父消息ID
      parentMessageId.value = null;
    };

    // 切换会话
    const handleChangeConversation = (conversationId: string) => {
      currentChatInstanceRef.value.handleStop();

      currentConversationId.value = conversationId;
      chatbotConversationId(chatToken.value, conversationId);

      if (conversationId) {
        showConfigPanel.value = false;
        loadChatMessages(conversationId);
      } else {
        showConfigPanel.value = true;
        currentChatMessages.value = [];
      }
    };

    // 开始聊天（从配置面板）
    const handleStartChat = () => {
      // 这里可以添加输入验证逻辑
      showConfigPanel.value = false;
      // 如果消息列表为空且有开场白配置，添加开场白消息
      if (appParams.value?.opening_statement) {
        currentChatMessages.value.unshift({
          id: `assistant-opening-${Date.now()}`,
          role: 'assistant',
          content: appParams.value.opening_statement,
          timestamp: Date.now(),
          isOpening: true, // 标记为开场白消息
        });
        console.log(2222222222222222222, currentChatMessages.value);

        // 滚动到底部，确保用户可以看到开场白
        scrollToBottom();
      }
    };

    // 加载会话列表
    const loadConversations = async () => {
      try {
        // 获取普通会话列表
        const normalRes = await fetchConversations({ pinned: false, limit: 100 });
        conversationList.value = normalRes.data || [];

        // 获取置顶会话列表
        const pinnedRes = await fetchConversations({ pinned: true, limit: 100 });
        pinnedConversationList.value = pinnedRes.data || [];
      } catch (error) {
        console.error('加载会话列表失败:', error);
      }
    };

    // 加载聊天消息
    const loadChatMessages = async (conversationId: string) => {
      if (!conversationId) return;

      messagesLoading.value = true;
      try {
        const res = await fetchChatMessages(conversationId);

        // 格式化消息列表
        const formattedMessages: ChatMessage[] = [];

        console.log("1111111111111", res);
        // 处理用户消息和回复
        (res?.data?.data || []).forEach((item: any) => {
          // 添加用户消息
          if (item.query) {
            formattedMessages.push({
              id: item.query_id || `user-${Date.now()}-${Math.random()}`,
              role: 'user',
              content: item.query,
              timestamp: new Date(item.created_at).getTime(),
            });
          }

          // 添加助手回复
          if (item.answer) {
            formattedMessages.push({
              id: item.id || `assistant-${Date.now()}-${Math.random()}`,
              role: 'assistant',
              content: item.answer,
              timestamp: new Date(item.created_at).getTime(),
              feedback: item.feedback,
              metadata: {
                tokens: item.tokens,
                duration: item.duration,
              },
            });
          }
        });

        // 按时间排序
        formattedMessages.sort((a, b) => (a.timestamp || 0) - (b.timestamp || 0));

        // 如果有开场白配置，添加开场白消息到最前面
        if (appParams.value?.opening_statement) {
          // 创建开场白消息
          const openingMessage = {
            id: `assistant-opening-${Date.now()}`,
            role: 'assistant',
            content: appParams.value.opening_statement,
            timestamp: 0, // 设置为0确保开场白消息始终在最前面
            isOpening: true, // 标记为开场白消息
          };

          // 将开场白消息添加到消息列表最前面
          formattedMessages.unshift(openingMessage);
        }

        currentChatMessages.value = formattedMessages;
      } catch (error) {
        console.error('加载聊天消息失败:', error);
        ElMessage.error('加载聊天记录失败');
      } finally {
        messagesLoading.value = false;
      }
    };

    // 发送消息
    const sendMessage = async(content: string, files: any[] = []) => {

      console.log("parentMessageId.value1", parentMessageId.value);

      if (!content.trim() && files.length === 0) return;

      // 添加用户消息
      const userMessageId = `user-${Date.now()}`;
      currentChatMessages.value.push({
        id: userMessageId,
        role: 'user',
        content: content,
        timestamp: Date.now(),
      });

      // 添加临时助手消息
      const assistantMessageId = `assistant-${Date.now()}`;
      currentChatMessages.value.push({
        id: assistantMessageId,
        role: 'assistant',
        content: '',
        rawAnswer: '', // 用于存储原始内容
        isLoading: true,
        status: 'pending', // 添加状态字段
        timestamp: Date.now(),
      });

      // 准备请求数据
      const requestData: {
        conversation_id?: string;
        query: string;
        inputs?: Record<string, any>;
        files?: any[];
        response_mode?: string;
        parent_message_id?: string | null;
      } = {
        query: content,
        files: files || [],
        conversation_id: currentConversationId.value || "", // 确保总是发送conversation_id参数，首次为空字符串
        // conversation_id: "", // 确保总是发送conversation_id参数，首次为空字符串
        response_mode: "streaming", // 添加response_mode参数
        inputs: {}, // 添加空的inputs对象
        parent_message_id: parentMessageId.value || null, // 添加parent_message_id参数
      };

      // 如果有输入参数，更新inputs
      if (Object.keys(newConversationInputs).length > 0) {
        requestData.inputs = { ...newConversationInputs };
      }
      // 发送消息到API
      await sendChatMessage(requestData, {
        onMessage: (chunk: string) => {
          // 将接收到的文本按换行符分割，处理每一条消息
          const messageLines = chunk.split("\n");

          const index = currentChatMessages.value.findIndex((msg: any) => msg.id === assistantMessageId);
          console.log("index", index);
          if (index === -1) return;

          let hasNewContent = false;
          let messageId: string | null = null;
          let conversationId: string | null = null;
          // 获取消息对象
          const msg = currentChatMessages.value[index];

          for (const line of messageLines) {
            if (!line.includes("data:")) continue;

            try {
              const jsonStr = line.replace(/^data: /, "").trim();
              if (!jsonStr) continue;

              const parsedData = JSON.parse(jsonStr);
              console.log('收到的数据:', parsedData); // 调试用

              // 处理不同类型的事件
              if (parsedData.event === "agent_message") {
                // 检查是否有直接answer或message内容
                if (parsedData.answer !== undefined) {
                  // 更新原始内容
                  msg.rawAnswer = (msg.rawAnswer || '') + parsedData.answer;
                  msg.content = msg.rawAnswer;
                  hasNewContent = true;
                }

                // 收集元数据
                if (parsedData.conversation_id) {
                  conversationId = parsedData.conversation_id;
                }
                if (parsedData.message_id) {
                  parentMessageId.value = parsedData.message_id;
                }

                console.log("parentMessageId.value2", parentMessageId.value);
              }
              // 处理思考过程（可选展示）
              else if (parsedData.event === "agent_thought" && parsedData.message && parsedData.message.thought) {
                // 可以选择是否展示思考过程
                // msg.rawAnswer += "\n思考: " + parsedData.message.thought;
                // msg.content = msg.rawAnswer;
                // hasNewContent = true;
              }
              // 处理普通消息事件
              else if (parsedData.answer !== undefined) {
                // 增量更新内容
                msg.rawAnswer = (msg.rawAnswer || '') + parsedData.answer;
                msg.content = msg.rawAnswer;
                hasNewContent = true;

                // 收集元数据
                if (parsedData.conversation_id) {
                  conversationId = parsedData.conversation_id;
                }
                if (parsedData.id) {
                  messageId = parsedData.id;
                }
              }
            } catch (error) {
              console.error("解析响应数据失败:", error);
            }
          }

          // 处理状态更新和元数据
          if (hasNewContent) {
            // 如果是首次收到内容，更新状态
            if (msg.status === 'pending') {
              msg.status = 'normal';
              msg.isLoading = false;
            }

            // 处理会话ID
            if (conversationId && !currentConversationId.value) {
              currentConversationId.value = conversationId;
              chatbotConversationId(chatToken.value, conversationId);
              // 重新加载会话列表
              loadConversations();
            }

            // // 更新真实消息ID
            // if (messageId) {
            //   currentChatMessages.value[index].id = messageId;
            // }
          }
        },
        onComplete: () => {
          const index = currentChatMessages.value.findIndex((msg: any) => msg.id === assistantMessageId);
          if (index !== -1) {
            // 完成时确保状态更新
            const lastMessage = currentChatMessages.value[index];
            lastMessage.isLoading = false;
            lastMessage.status = 'normal';

            // 完成时再次处理内容，确保格式完整
            if (lastMessage.rawAnswer) {
              // 这里可以添加最终处理逻辑，例如格式化内容
              // lastMessage.content = formatMarkdown(lastMessage.rawAnswer);
            }

            // 如果是新会话，可以在这里处理会话创建后的逻辑
            if (currentConversationId.value) {
              // 可以在这里添加获取会话名称等操作
            }

            // 如果配置了回答后推荐问题功能，获取推荐问题
            if (appParams.value?.suggested_questions_after_answer?.enabled && parentMessageId.value) {
              // 直接使用parentMessageId获取推荐问题
              getSuggestedQuestions(parentMessageId.value);
            }

            // 已经在 loadChatMessages 中添加了开场白消息，这里不需要重复添加
          }
        },
        onError: (error: any) => {
          ElMessage.error('发送消息失败');
          console.error('发送消息失败:', error);

          // 更新消息状态为失败
          const index = currentChatMessages.value.findIndex((msg: any) => msg.id === assistantMessageId);
          if (index !== -1) {
            currentChatMessages.value[index].status = 'failed';
            currentChatMessages.value[index].isLoading = false;
          }
        }
      });

      // 滚动到底部，确保用户可以看到最新消息
      scrollToBottom();

      // 保存中止函数
      currentChatInstanceRef.value.handleStop = () => {
        // 这里应该实现中止功能，但目前没有实现
        console.log('停止生成回复');
      };
    };

    // 处理消息反馈
    const handleFeedback = (messageId: string, feedback: 'like' | 'dislike') => {
      const index = currentChatMessages.value.findIndex((msg: any) => msg.id === messageId);
      if (index !== -1) {
        currentChatMessages.value[index].feedback = { rating: feedback };
      }
    };

    // 加载初始数据
    const loadInitialData = async () => {
      appInfoLoading.value = true;
      appInfoError.value = false;

      try {
        // 并行请求基础数据
        const [siteInfoRes, paramsRes, metaRes] = await Promise.all([
          fetchSiteInfo(),
          fetchChatParams(),
          fetchChatMeta()
        ]);

        appData.value = siteInfoRes;
        appParams.value = paramsRes.data;
        appMeta.value = metaRes;

        console.log('聊天参数配置:', appParams.value);

        // 加载会话列表
        await loadConversations();

        // 如果有当前会话ID，加载消息
        if (currentConversationId.value) {
          await loadChatMessages(currentConversationId.value);
          showConfigPanel.value = false;
        }

        // 更新页面标题
        if (appData.value?.site?.title) {
          document.title = appData.value.site.title;
        }

        initialized.value = true;

        // 初始化完成后滚动到底部
        scrollToBottom();
      } catch (error) {
        console.error('初始化数据失败:', error);
        appInfoError.value = true;
        ElMessage.error('加载聊天机器人数据失败');
      } finally {
        appInfoLoading.value = false;
      }
    };

    // 监听窗口大小变化
    const handleResize = () => {
      isMobile.value = window.innerWidth < 768;
    };

    // 监听readyToInit属性变化
    watch(() => props.readyToInit, (newVal: boolean) => {
      if (newVal && !initialized.value) {
        loadInitialData();
      }
    }, { immediate: true });

    onMounted(() => {
      // 只添加事件监听器，不立即初始化
      window.addEventListener('resize', handleResize);
    });

    // 获取推荐问题
    const suggestedQuestions = ref<string[]>([]);
    const loadingSuggestions = ref(false);

    // 获取推荐问题的方法
    const getSuggestedQuestions = async (messageId: string) => {
      if (!messageId || !currentConversationId.value) return;

      try {
        loadingSuggestions.value = true;
        // 使用封装的API函数获取推荐问题
        const res = await fetchSuggestedQuestions(parentMessageId.value || messageId);
        // 处理返回的数据
        // 使用any类型解决TypeScript错误
        const data = res as any;
        suggestedQuestions.value = data?.data || [];
        console.log('推荐问题:', suggestedQuestions.value);
      } catch (error) {
        console.error('获取推荐问题失败:', error);
        // 使用any类型解决TypeScript错误
        suggestedQuestions.value = [];
      } finally {
        loadingSuggestions.value = false;
      }
    };

    // 滚动到底部的方法
    const scrollToBottom = () => {
      nextTick(() => {
        const chatArea = document.querySelector('.chat-area');
        if (chatArea) {
          chatArea.scrollTop = chatArea.scrollHeight;
        }
      });
    };

    // 监听消息列表变化，自动滚动到底部
    watch(currentChatMessages, () => {
      scrollToBottom();
    }, { deep: true });

    // 创建上下文对象
    const chatbotContext = {
      appData,
      appParams,
      appMeta,
      isMobile,
      currentConversationId,
      currentConversationItem,
      conversationList,
      pinnedConversationList,
      currentChatMessages,
      showConfigPanel,
      messagesLoading,
      chatReady,
      newConversationInputs,
      suggestedQuestions,
      loadingSuggestions,
      handleNewConversation,
      handleChangeConversation,
      handleStartChat,
      sendMessage,
      handleFeedback,
      currentChatInstanceRef,
      getSuggestedQuestions,
      scrollToBottom,
    };

    // 向子组件提供上下文
    provide(CHATBOT_CONTEXT_KEY, chatbotContext);

    return {
      initialized,
      chatbotContext
    };
  }
});
</script>

<style scoped>
.chatbot-provider {
  height: 100%;
  width: 100%;
}

.loading-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 16px;
  gap: 16px;
}

.loading-container .el-icon {
  font-size: 24px;
}
</style>
