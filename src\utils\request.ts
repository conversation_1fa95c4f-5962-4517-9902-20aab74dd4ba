import axios from "axios";
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from "axios";
import { ElMessage } from "element-plus";
import router from "@/router";
import { getToken } from "@/utils/user";

// 消息管理器
class MessageManager {
  private static instance: MessageManager;
  private currentMessage: string | null = null;

  private constructor() {}

  static getInstance(): MessageManager {
    if (!MessageManager.instance) {
      MessageManager.instance = new MessageManager();
    }
    return MessageManager.instance;
  }

  showError(message: string) {
    if (this.currentMessage === message) {
      return;
    }
    this.currentMessage = message;
    ElMessage.error({
      message,
      onClose: () => {
        this.currentMessage = null;
      },
    });
  }
}

const messageManager = MessageManager.getInstance();

// 定义请求参数类型
interface RequestData {
  [key: string]: any;
  conversation_id?: string;
  parent_message_id?: string | null;
  files?: unknown[];
  inputs?: Record<string, unknown>;
  response_mode?: string;
  query?: string;
}

// 定义流式请求的回调函数类型
interface StreamCallbacks {
  onMessage: (text: string) => void;
  onError?: (error: Error) => void;
  onComplete?: () => void;
}

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || "/api", // 从环境变量获取api基础路径
  timeout: 0, // 请求超时时间
  headers: {
    "Content-Type": "application/json;charset=utf-8",
  },
});

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 从localStorage获取token
    const token = getToken();

    // 如果有token则添加到请求头
    if (token && config.headers) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }

    return config;
  },
  (error: unknown) => {
    console.error("请求错误：", error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data, status } = response;

    if (status === 200 || status === 201 || status === 204) {
      return data;
    }

    switch (status) {
      case 401:
      case 402:
      case 403:
        get("/logout");
        localStorage.clear();
        router.push("/login");
        break;
      case 500:
        messageManager.showError("服务器错误，请稍后重试");
        break;
      default:
        messageManager.showError(data.message || "未知错误");
    }

    return Promise.reject(response);
  },
  (error: unknown) => {
    if (axios.isAxiosError(error) && error.response) {
      switch (error.response.status) {
        case 401:
          messageManager.showError("登录已过期，请重新登录");
          localStorage.clear();
          router.replace("/login");
          break;
        case 403:
          messageManager.showError("没有权限访问该资源");
          break;
        case 404:
          messageManager.showError("请求的资源不存在");
          break;
        case 500:
          messageManager.showError("服务器错误，请稍后重试");
          break;
        default:
          messageManager.showError(error.response.data.message || "网络错误，请稍后重试");
      }
    }
    return Promise.reject(error);
  }
);

// 导出请求方法
export const get = <T = unknown>(url: string, params?: RequestData): Promise<T> => {
  return service.get(url, { params });
};

export const post = <T = unknown>(url: string, data?: RequestData): Promise<T> => {
  return service.post(url, data);
};

export const put = <T = unknown>(url: string, data?: RequestData): Promise<T> => {
  return service.put(url, data);
};

export const del = <T = unknown>(url: string, params?: RequestData): Promise<T> => {
  return service.delete(url, { params });
};

export const patch = <T = unknown>(url: string, data?: RequestData): Promise<T> => {
  return service.patch(url, data);
};

// 扩展请求方法，支持自定义请求头
export const getWithHeaders = <T = unknown>(
  url: string,
  params?: RequestData,
  options?: { headers?: Record<string, string> }
): Promise<T> => {
  return service.get(url, { params, headers: options?.headers });
};

export const postWithHeaders = <T = unknown>(
  url: string,
  data?: RequestData,
  options?: { headers?: Record<string, string> }
): Promise<T> => {
  return service.post(url, data, { headers: options?.headers });
};

// 流式请求方法
export const postStream = (url: string, data: RequestData, callbacks: StreamCallbacks) => {
  const { onMessage, onError, onComplete } = callbacks;
  let previousResponseLength = 0;

  return service
    .request({
      method: "post",
      url,
      data,
      responseType: "stream",
      onDownloadProgress: progressEvent => {
        try {
          const responseText = progressEvent.event.target.responseText;
          if (responseText && responseText.length > previousResponseLength) {
            // 只获取新增部分
            const newChunk = responseText.substring(previousResponseLength);
            previousResponseLength = responseText.length;
            onMessage(newChunk);
          }
        } catch (error) {
          onError?.(error as Error);
        }
      },
    })
    .then(() => {
      onComplete?.();
    })
    .catch(error => {
      onError?.(error);
    });
};

// 上传文件请求方法
export interface UploadProgressCallback {
  (progress: number): void;
}

export const uploadFile = <T = unknown>(url: string, file: File, onProgress?: UploadProgressCallback): Promise<T> => {
  const formData = new FormData();
  formData.append("file", file);

  return service.request({
    method: "post",
    url,
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
    onUploadProgress: onProgress
      ? progressEvent => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
          onProgress(percentCompleted);
        }
      : undefined,
  });
};

export default service;
