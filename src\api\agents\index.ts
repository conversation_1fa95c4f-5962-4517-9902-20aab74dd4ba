import axios from 'axios';
import { getToken } from '@/utils/user';
import { get, post, put, del, postStream } from '@/utils/request';
import type { ChatParams } from '../../views/agents/types';

// 定义接口类型
export interface AgentListResponse {
  data: any[];
  total: number;
}

export interface Agent {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  icon_type?: string;
  status?: string;
  created_at?: string;
  updated_at?: string;
  tags?: string[];
  mode?: string;
  creator?: {
    id: string;
    name: string;
    avatar?: string;
  };
}

export interface CreateAgentReq {
  name: string;
  description?: string;
  icon?: string;
  icon_type?: string;
  mode?: string;
  [key: string]: any; // 添加索引签名以解决类型错误
}

export interface FileUploadResponse {
  id: string;
  name: string;
  size: number;
  url: string;
  type: string;
}

// 获取智能体列表
export const fetchAgentList = (params?: any) => {
  return get<AgentListResponse>('/agents', params);
};

// 创建智能体
export const createAgent = (data: CreateAgentReq) => {
  return post<Agent>('/apps', data);
};

// 上传文件
export const uploadFile = (file: File, onProgress?: (progress: number) => void) => {
  const formData = new FormData();
  formData.append('file', file);
  
  // 使用 FormData 时，需要手动处理这个请求
  return axios.post<FileUploadResponse>('/files/upload', formData, {
    baseURL: import.meta.env.VITE_API_BASE_URL || "/api",
    headers: {
      // 'Content-Type': 'multipart/form-data', // 确保使用正确的Content-Type
      "Content-Type": "*/*",
      'Authorization': `Bearer ${getToken()}` // 添加授权头
    },
    onUploadProgress: onProgress ? (progressEvent) => {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
      onProgress(percentCompleted);
    } : undefined
  });
};

// 获取智能体详情
export const getAgentDetail = (id: string) => {
  return get<any>(`/apps/${id}`);
};

// 编辑智能体
export const putAgentDetail = (id: string, data: Partial<CreateAgentReq>) => {
  return put<any>(`/apps/${id}`, data);
};

// 更新智能体
export const updateAgent = (agentId: string, data: Partial<CreateAgentReq>) => {
  return post<Agent>(`/apps/${agentId}/model-config`, data);
};

// 删除智能体
export const deleteAgent = (id: string) => {
  return del<void>(`/apps/${id}`);
};

// 发送聊天消息
export const sendChatMessage = (
  agentId: string, 
  params: ChatParams, 
  callbacks: {
    onMessage: (text: string) => void;
    onError: (error: any) => void;
    onComplete: () => void;
  }
) => {
  return postStream(`/apps/${agentId}/chat-messages`, params, callbacks);
};


// 获取聊天消息历史记录
export const getChatMessages = (agentId: string, conversationId: string) => {
  return get<any>(`/apps/${agentId}/chat-messages`, { conversation_id: conversationId });
};

// 获取推荐问题
export const getSuggestedQuestions = (agentId: string, messageId: string) => {
  return get<any>(`/apps/${agentId}/chat-messages/${messageId}/suggested-questions`);
};

// 获取Embedding模型列表
export const fetchTextEmbeddingModelList = () => {
  return get<any>('/llm/embedding/providers');
};

// 获取默认Embedding模型
export const fetchDefaultTextEmbeddingModel = () => {
  return get<any>('/llm/embedding/default');
};