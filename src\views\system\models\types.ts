export interface ProviderRecord {
  id: string | number;
  provider: string;
  label?: {
    zh_Hans?: string;
    zh_Hant?: string;
    en?: string;
  };
  icon_small?: {
    zh_Hans?: string;
    zh_Hant?: string;
    en?: string;
  };
  supported_model_types?: string[];
  configurate_methods: string[];
  custom_configuration?: {
    status: string;
  };
  provider_credential_schema?: {
    credential_form_schemas: Array<{
      variable: string;
      label: {
        zh_Hans: string;
        zh_Hant?: string;
        en?: string;
      };
      type: string;
      placeholder?: {
        zh_Hans: string;
        zh_Hant?: string;
        en?: string;
      };
      required?: boolean;
    }>;
  };
  model_credential_schema?: {
    credential_form_schemas: Array<{
      variable: string;
      label: {
        zh_Hans: string;
        zh_Hant?: string;
        en?: string;
      };
      type: "text-input" | "secret" | "radio" | "checkbox" | "select";
      placeholder?: {
        zh_Hans: string;
        zh_Hant?: string;
        en?: string;
      };
      required?: boolean;
      options?: Array<{
        value: string;
        label: {
          zh_Hans: string;
          zh_Hant?: string;
          en?: string;
        };
      }>;
      default?: string | string[];
      show_on?: Array<{
        variable: string;
        value: string;
      }>;
    }>;
    model: {
      label: {
        zh_Hans: string;
        zh_Hant?: string;
        en?: string;
      };
      placeholder: {
        zh_Hans: string;
        zh_Hant?: string;
        en?: string;
      };
    };
  };
  [key: string]: unknown;
}

export interface ApiProviderResponse {
  data: ProviderRecord[];
}

export interface PluginRecord {
  label: {
    zh_Hans: string;
    en_US: string;
  };
  description?: {
    zh_Hans: string;
    en?: string;
  };
  model?: {
    provider: string;
    description: {
      zh_Hans: string;
      en_US: string;
    };
  };
  [key: string]: unknown;
}

export interface ModelInfo {
  id: string;
  name: string;
  model_type: string;
  property: string;
  size: number;
  status: "active" | "inactive";
  icon?: string;
  model: string;
  model_properties: {
    mode: string;
    context_size: number;
  };
}

export interface ShowModelDialogProps {
  visible: boolean;
  provider: ProviderRecord | null;
}

export interface ModelSettingsItem {
  model: string; // 直接使用model字段，不再使用id字段
  name: string;
  type?: string;
  icon?: string;
  selected?: boolean; // 设为可选，因为选中状态由el-select组件通过v-model自动管理
}

export interface ModelSettingsGroup {
  title: string;
  key: string;
  api_path: string; // 添加API路径
  items: ModelSettingsItem[];
  categories: ModelCategoryItem[];
  desc?: string;
}

// 模型API返回数据类型
export interface ModelApiResponse {
  data?: ModelCategoryItem[];
}

// 模型分类项
export interface ModelCategoryItem {
  icon_large?: {
    en_US?: string;
    zh_Hans?: string;
  };
  icon_small?: {
    en_US?: string;
    zh_Hans?: string;
  };
  label?: {
    en_US?: string;
    zh_Hans?: string;
  };
  provider?: string;
  status?: string;
  tenant_id?: string;
  models?: ModelItem[];
}

// 具体模型项
export interface ModelItem {
  deprecated?: boolean;
  features?: string[];
  fetch_from?: string;
  label?: {
    en_US?: string;
    zh_Hans?: string;
  };
  load_balancing_enabled?: boolean;
  model?: string;
  model_properties?: {
    context_size?: number;
    mode?: string;
  };
  model_type?: string;
  status?: string;
}

export interface DefaultModelResponse {
  data: DefaultModelItem;
}

export interface DefaultModelItem {
  model?: string;
  model_type?: string;
  provider?: ModelCategoryItem[];
}
