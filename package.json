{"name": "vue-demo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"serve": "vite", "dev": "vite", "build": "vite build", "build:skip-ts": "vite build", "build:prod": "vite build --mode production", "build:no-check": "cross-env VITE_SKIP_TS_CHECK=true vite build", "build:force": "cross-env VITE_SKIP_TS_CHECK=true DISABLE_TS_CHECK=true vite build --emptyOutDir", "preview": "vite preview"}, "dependencies": {"@dagrejs/dagre": "^1.1.4", "@element-plus/icons-vue": "^2.3.1", "@types/lodash-es": "^4.17.12", "@vue-flow/core": "^1.44.0", "axios": "^1.8.3", "dompurify": "^3.2.4", "element-plus": "^2.9.6", "highlight.js": "^11.11.1", "lodash-es": "^4.17.21", "marked": "^15.0.7", "nanoid": "^5.1.5", "vue": "^3.5.13", "vue-linkify": "^1.0.1", "vue-router": "^4.5.0"}, "devDependencies": {"@types/marked": "^6.0.0", "@types/node": "^22.13.10", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "cross-env": "^7.0.3", "sass": "^1.85.1", "typescript": "~5.7.2", "vite": "^6.2.0", "vite-svg-loader": "^5.1.0", "vue-tsc": "^2.2.4"}}