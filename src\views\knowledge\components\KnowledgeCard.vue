<template>
  <div class="knowledge-card" :class="{ 'not-connected': !knowledge.connected }">
    <div class="card-header">
      <div class="card-icon">{{ knowledge.icon || '📚' }}</div>
      <div class="card-title">{{ knowledge.name }}</div>
      <div class="card-actions">
        <el-dropdown trigger="click">
          <el-button type="text">
            <el-icon :size="18"><MoreFilled /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="handleEdit">编辑</el-dropdown-item>
              <el-dropdown-item @click="handleExport">导出</el-dropdown-item>
              <el-dropdown-item @click="handleDelete" class="text-danger">删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <div class="card-description">{{ knowledge.description }}</div>
    
    <div class="card-tags">
      <el-tag v-for="(tag, index) in knowledge.tags" :key="index" size="small">{{ tag }}</el-tag>
    </div>
    
    <div class="card-statistics">
      <div class="stat-item">
        <div class="stat-value">{{ knowledge.document_count }}</div>
        <div class="stat-label">文档数量</div>
      </div>
      <div class="stat-divider"></div>
      <div class="stat-item">
        <div class="stat-value">{{ formatWordCount(knowledge.word_count) }}</div>
        <div class="stat-label">总字数</div>
      </div>
      <div class="stat-divider"></div>
      <div class="stat-item">
        <div class="stat-value">{{ formatDate(knowledge.updated_at) }}</div>
        <div class="stat-label">更新时间</div>
      </div>
    </div>
    
    <div class="card-footer">
      <el-button 
        v-if="!knowledge.connected" 
        type="primary" 
        plain 
        @click="handleConnect"
      >
        <el-icon><Link /></el-icon>
        <span>连接</span>
      </el-button>
      <el-button 
        v-else 
        type="success" 
        plain 
      >
        <el-icon><CircleCheckFilled /></el-icon>
        <span>已连接</span>
      </el-button>
      <el-button @click="handleView">
        <el-icon><View /></el-icon>
        <span>查看</span>
      </el-button>
    </div>

    <!-- 确认删除对话框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="确认删除"
      width="400px"
    >
      <div class="delete-dialog-content">
        <el-icon :size="32" color="#F56C6C"><WarningFilled /></el-icon>
        <p class="delete-warning">确定要删除知识库"{{ knowledge.name }}"吗？此操作不可恢复。</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmDelete">确认删除</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { MoreFilled, Link, View, CircleCheckFilled, WarningFilled } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { get, post, del } from '@/utils/request';

defineOptions({
  name: 'KnowledgeCard',
});

const props = defineProps<{
  knowledge: any;
}>();

const emits = defineEmits<{
  (e: 'refresh'): void;
}>();

const router = useRouter();
const deleteDialogVisible = ref(false);

// 格式化字数
const formatWordCount = (count: number) => {
  if (count >= 10000) {
    return `${(count / 10000).toFixed(1)}万`;
  }
  return count.toString();
};

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 查看知识库详情
const handleView = () => {
  router.push(`/knowledge/detail/${props.knowledge.id}`);
};

// 编辑知识库
const handleEdit = () => {
  router.push(`/knowledge/edit/${props.knowledge.id}`);
};

// 导出知识库
const handleExport = async () => {
  try {
    ElMessage.info('开始导出知识库，请稍候...');
    // 模拟API调用
    // await post(`/datasets/${props.knowledge.id}/export`);
    ElMessage.success('知识库导出成功！');
  } catch (error) {
    console.error('导出知识库失败', error);
    ElMessage.error('导出知识库失败，请重试');
  }
};

// 连接知识库
const handleConnect = async () => {
  try {
    ElMessage.info('正在连接知识库...');
    // 模拟API调用
    // await post(`/datasets/${props.knowledge.id}/connect`);
    ElMessage.success('知识库连接成功！');
    emits('refresh');
  } catch (error) {
    console.error('连接知识库失败', error);
    ElMessage.error('连接知识库失败，请重试');
  }
};

// 删除知识库
const handleDelete = () => {
  deleteDialogVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
  try {
    // 模拟API调用
    // await del(`/datasets/${props.knowledge.id}`);
    ElMessage.success('知识库删除成功！');
    deleteDialogVisible.value = false;
    emits('refresh');
  } catch (error) {
    console.error('删除知识库失败', error);
    ElMessage.error('删除知识库失败，请重试');
  }
};
</script>

<style scoped lang="scss">
.knowledge-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  border: 1px solid #eaeaea;
  
  &:hover {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  &.not-connected {
    background-color: #f9f9f9;
  }
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  
  .card-icon {
    font-size: 24px;
    margin-right: 12px;
    min-width: 32px;
  }
  
  .card-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .card-actions {
    margin-left: 8px;
  }
}

.card-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  flex: 1;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.card-statistics {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 6px;
  
  .stat-item {
    flex: 1;
    text-align: center;
    
    .stat-value {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
    
    .stat-label {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
    }
  }
  
  .stat-divider {
    width: 1px;
    height: 30px;
    background-color: #e0e0e0;
  }
}

.card-footer {
  display: flex;
  gap: 10px;
  margin-top: auto;
}

.delete-dialog-content {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 20px 0;
  
  .delete-warning {
    flex: 1;
    margin: 0;
  }
}

.text-danger {
  color: #F56C6C;
}
</style>
