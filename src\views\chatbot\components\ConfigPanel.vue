<template>
  <div class="config-panel">
    <div class="panel-card">
     
      
      <div class="panel-content">
        <template v-if="appParams?.user_input_form && appParams.user_input_form.length > 0">
          <!-- 表单输入项 -->
          <div class="input-form">
            <div
              v-for="(item, index) in inputForms"
              :key="index"
              class="form-item"
            >
              <template v-if="item.type === 'text-input' || item.type === 'paragraph'">
                <div class="item-label">
                  {{ item.label }}
                  <span v-if="item.required" class="required">*</span>
                </div>
                <el-input
                  v-if="item.type === 'text-input'"
                  v-model="newConversationInputs[item.variable]"
                  :placeholder="item.placeholder || '请输入'"
                  :maxlength="item.max_length"
                />
                <el-input
                  v-else
                  v-model="newConversationInputs[item.variable]"
                  type="textarea"
                  :rows="4"
                  :placeholder="item.placeholder || '请输入'"
                  :maxlength="item.max_length"
                />
              </template>
              
              <template v-else-if="item.type === 'select'">
                <div class="item-label">
                  {{ item.label }}
                  <span v-if="item.required" class="required">*</span>
                </div>
                <el-select
                  v-model="newConversationInputs[item.variable]"
                  :placeholder="item.placeholder || '请选择'"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in item.options"
                    :key="option"
                    :label="option"
                    :value="option"
                  />
                </el-select>
              </template>
              
              <template v-else-if="item.type === 'number'">
                <div class="item-label">
                  {{ item.label }}
                  <span v-if="item.required" class="required">*</span>
                </div>
                <el-input-number
                  v-model="newConversationInputs[item.variable]"
                  :min="item.min"
                  :max="item.max"
                  :step="item.step || 1"
                  style="width: 100%"
                />
              </template>
            </div>
          </div>
        </template>
        
        <div v-else class="no-form-message">
          <p>开始与{{ appData?.site?.title || '聊天机器人' }}对话吧</p>
        </div>
      </div>
      
      <div class="panel-footer">
        <el-button type="primary" @click="startChat">开始对话</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { inject, computed, onMounted, defineComponent } from 'vue';
import { CHATBOT_CONTEXT_KEY } from './ChatbotProvider.vue';
import { ElMessage } from 'element-plus';

export default defineComponent({
  name: 'ConfigPanel',
  emits: ['start-chat'],
  setup(props, { emit }) {
    // 注入聊天上下文
    const context = inject(CHATBOT_CONTEXT_KEY) as any;

    // 从上下文中获取数据
    const appData = computed(() => context.appData.value);
    const appParams = computed(() => context.appParams.value);
    const newConversationInputs = computed(() => context.newConversationInputs);

    // 格式化输入表单
    const inputForms = computed(() => {
      if (!appParams.value?.user_input_form) return [];
      
      return appParams.value.user_input_form.filter((item: any) => !item.external_data_tool).map((item: any) => {
        if (item.paragraph) {
          return {
            ...item.paragraph,
            type: 'paragraph',
          };
        }
        if (item.number) {
          return {
            ...item.number,
            type: 'number',
          };
        }
        if (item.select) {
          return {
            ...item.select,
            type: 'select',
          };
        }
        if (item['text-input']) {
          return {
            ...item['text-input'],
            type: 'text-input',
          };
        }
        return null;
      }).filter(Boolean);
    });

    // 检查必填项
    const validateInputs = () => {
      const requiredFields = inputForms.value.filter((item: any) => item.required);
      
      for (const field of requiredFields) {
        const value = newConversationInputs.value[field.variable];
        if (value === undefined || value === null || value === '') {
          ElMessage.warning(`请填写${field.label}`);
          return false;
        }
      }
      
      return true;
    };

    // 初始化默认值
    onMounted(() => {
      inputForms.value.forEach((item: any) => {
        if (item.default !== undefined && newConversationInputs.value[item.variable] === undefined) {
          newConversationInputs.value[item.variable] = item.default;
        }
      });
    });

    // 开始聊天
    const startChat = () => {
      if (validateInputs()) {
        context.handleStartChat();
        emit('start-chat');
      }
    };

    return {
      appData,
      appParams,
      newConversationInputs,
      inputForms,
      startChat
    };
  }
});
</script>

<style scoped>
.config-panel {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 16px;
}

.panel-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.panel-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
}

.panel-header h2 {
  margin: 0 0 8px;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
}

.panel-header p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.panel-content {
  padding: 20px;
}

.input-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-item {
  margin-bottom: 16px;
}

.item-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.required {
  color: #f56c6c;
  margin-left: 4px;
}

.no-form-message {
  text-align: center;
  padding: 24px 0;
  color: #6b7280;
}

.panel-footer {
  padding: 16px 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
  .config-panel {
    padding: 12px;
  }
  
  .panel-header {
    padding: 16px;
  }
  
  .panel-content {
    padding: 16px;
  }
  
  .panel-footer {
    padding: 12px 16px;
  }
}
</style>
