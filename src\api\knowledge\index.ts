import { get, post, put, del, patch } from '@/utils/request';
import axios from 'axios'; // 引入axios
import { getToken } from '@/utils/user'; // 从正确的路径导入getToken函数

// 知识库列表接口
export interface Dataset {
  id: string;
  name: string;
  description: string;
  created_at: string;
  document_count: number;
  tags: string[];
  status: string;
  word_count?: number;
  updated_at?: string;
  connected?: boolean;
  icon?: string;
  permission?: string;
  creator?: {
    id: string;
    name: string;
    avatar?: string;
  };
  [key: string]: unknown; // 添加索引签名以支持更多属性
}

export interface DatasetListResponse {
  data: Dataset[];
  total: number;
  page: number;
  limit: number;
}

export interface DatasetListParams {
  page?: number;
  limit?: number;
  keyword?: string;
  tags?: string[];
  include_all?: boolean;
  status?: string;
  [key: string]: unknown;
}

// 文件上传相关接口
export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  status: 'uploading' | 'uploaded' | 'failed';
  progress?: number;
  error?: string;
}

export interface FileUploadResponse {
  id: string;
  name: string;
  size: number;
  extension: string;
  mime_type: string;
  created_at: string;
  [key: string]: unknown;
}

// 创建知识库相关接口
export interface CreateDatasetReq {
  name: string;
  description?: string;
  tags?: string[];
  [key: string]: unknown;
}

export interface ProcessRule {
  mode: 'general' | 'parent_child' | 'custom';
  rules: {
    clean_empty_lines?: boolean;
    remove_urls?: boolean;
    use_qa_split?: boolean;
    segment_identifier?: string;
    max_chunk_length?: number;
    overlap?: number;
    parent_segment_identifier?: string;
    parent_max_chunk_length?: number;
    child_segment_identifier?: string;
    pre_processing_rules?: Array<{
      id: string;
      enabled: boolean;
    }>;
    segmentation?: {
      delimiter?: string;
      max_tokens?: number;
      chunk_overlap?: number;
    };
    [key: string]: unknown;
  };
  limits?: {
    indexing_max_segmentation_tokens_length?: number;
    [key: string]: unknown;
  };
}

export interface DocumentParams {
  ids: string[];
  data_source_type: string;
  process_rule: ProcessRule;
  [key: string]: unknown;
}

// 接口响应类型
export interface ProcessRuleResponse {
  data: ProcessRule;
}

export interface FileIndexingEstimateResponse {
  data: {
    tokens: number;
    pages: number;
    characters: number;
    segments: number;
    [key: string]: unknown;
  };
}

// 模型相关接口类型
export interface ModelProvider {
  provider: string;
  provider_name?: string;
  models: string[];
  [key: string]: unknown;
}

export interface ModelListResponse {
  data: ModelProvider[];
}

export interface DefaultModelResponse {
  data: {
    provider: {
      provider: string;
      provider_name?: string;
    };
    model: string;
    [key: string]: unknown;
  };
}

// 模型类型枚举
export enum ModelType {
  RERANK = 'rerank',
  TEXT_EMBEDDING = 'text-embedding'
}

// 知识库设置2相关接口
export interface DatasetSettings {
  topK: number;
  scoreThreshold: number;
  scoreThresholdEnabled: boolean;
  [key: string]: any;
}

// 获取知识库列表
export const fetchDatasetList = (params: DatasetListParams = {}) => {
  return get<DatasetListResponse>('/datasets', params);
};

// 创建空知识库
export const createEmptyDataset = (data: CreateDatasetReq) => {
  return post<Dataset>('/datasets', data);
};

// 上传文件
export const uploadFile = (file: File, onProgress?: (progress: number) => void) => {
  const formData = new FormData();
  formData.append('file', file);

  // 使用 FormData 时，需要手动处理这个请求
  return axios.post<FileUploadResponse>('/files/upload?source=datasets', formData, {
    baseURL: import.meta.env.VITE_API_BASE_URL || "/api",
    headers: {
      'Content-Type': 'multipart/form-data', // 确保使用正确的Content-Type
      'Authorization': `Bearer ${getToken()}` // 添加授权头
    },
    onUploadProgress: onProgress ? (progressEvent) => {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
      onProgress(percentCompleted);
    } : undefined
  });
};

// 创建知识库并添加文档
export const createDatasetWithDocuments = (data: CreateDatasetReq, documents: DocumentParams) => {
  return post<Dataset>('/datasets/create-with-files', {
    ...data,
    documents,
  });
};

// 获取知识库详情
export const getDatasetDetail = (id: string) => {
  return get<any>(`/datasets/${id}`);
};

// 删除知识库
export const deleteDataset = (id: string) => {
  return del(`/datasets/${id}`);
};

// 更新知识库
export const updateDataset = (id: string, data: Partial<CreateDatasetReq>) => {
  return patch<Dataset>(`/datasets/${id}`, data);
};

// 获取所有标签
export const fetchAllTags = () => {
  return get<{ tags: string[] }>('/tags');
};

// 获取支持的文件类型
export const getSupportedFileTypes = () => {
  return get<{ allowed_extensions: string[] }>('/files/allowed-extensions');
};

// 获取知识库设置
export const fetchDatasetSettings = async (datasetId: string): Promise<any> => {
  return get<any>(`${import.meta.env.VITE_API_ICON_URL}/datasets/${datasetId}/settings?_rsc=1i55h`);
};

// 更新知识库设置
export const updateDatasetSettings = async (datasetId: string, settings: any): Promise<any> => {
  return put<any>(`${import.meta.env.VITE_API_ICON_URL}/datasets/${datasetId}/settings?_rsc=1i55h`, settings);
};

// /**
//  * 获取文件预处理估算
//  */
// export const getFileIndexingEstimate = (params: {
//   datasetId: string;
//   files: string[];
//   indexingTechnique: string;
//   processRule: ProcessRule;
// }) => {
//   return post('/datasets/indexing-estimate', params);
// };

// 获取默认处理规则
export const fetchDefaultProcessRule = () => {
  return get<ProcessRuleResponse>('/datasets/process-rule');
};

// /**
//  * 获取文件预处理估算（通过文件ID，扩展现有接口）
//  */
// export const fetchFileIndexingEstimate = (params: any) => {
//   return post<FileIndexingEstimateResponse>('/indexing-estimate', params);
// };

// 获取模型列表（rerank模型）
export const fetchRerankModelList = () => {
  return get<ModelListResponse>('/workspaces/current/models/model-types/rerank');
};

// 获取模型列表（文本嵌入模型）
export const fetchTextEmbeddingModelList = () => {
  return get<ModelListResponse>('/workspaces/current/models/model-types/text-embedding');
};

// 获取默认rerank模型
export const fetchDefaultRerankModel = () => {
  return get<DefaultModelResponse>('/workspaces/current/default-model?model_type=rerank');
};

// 获取默认文本嵌入模型
export const fetchDefaultTextEmbeddingModel = (): Promise<DefaultModelResponse> => {
  return get(`/workspaces/current/default-model?model_type=text-embedding`);
};

// 获取当前工作空间成员列表
export interface WorkspaceMember {
  id: string;
  name: string;
  avatar: string | null;
  avatar_url: string | null;
  email?: string;
  role?: string;
  status?: string;
  created_at?: number;
  last_login_at?: number;
}

export interface WorkspaceMembersResponse {
  accounts: WorkspaceMember[];
}

export const fetchWorkspaceMembers = (): Promise<WorkspaceMembersResponse> => {
  return get(`/workspaces/current/members`);
};

// 初始化创建知识库并处理文档
export interface CreateDocumentReq {
  data_source: {
    type: string;
    info_list: {
      data_source_type: string;
      file_info_list?: {
        file_ids: string[];
      };
      [key: string]: any;
    };
  };
  indexing_technique: string;
  process_rule: ProcessRule;
  doc_form: string;
  doc_language: string;
  retrieval_model: {
    search_method: string;
    reranking_enable: boolean;
    reranking_model: {
      reranking_provider_name: string;
      reranking_model_name: string;
    };
    top_k: number;
    score_threshold_enabled: boolean;
    score_threshold: number;
  };
  embedding_model: string;
  embedding_model_provider: string;
}

export interface CreateDocumentResponse {
  data: any;
}

// 创建第一个文档（初始化知识库）
export const createFirstDocument = (data: CreateDocumentReq) => {
  return post<CreateDocumentResponse>('/datasets/init', data as any);
};

// 获取处理规则
export const fetchProcessRule = (documentId?: string) => {
  return get<ProcessRuleResponse>('/datasets/process-rule', { document_id: documentId });
};

// 知识库召回测试相关接口

/**
 * 进行知识库召回测试
 * @param datasetId 知识库ID
 * @param queryText 查询文本
 * @param retrieval_model 检索模型配置
 * @returns 测试结果
 */
export const hitTesting = (datasetId: string, queryText: string, retrieval_model: any) => {
  return post<any>(`/datasets/${datasetId}/hit-testing`, {
    query: queryText,
    retrieval_model
  });
};

/**
 * 获取知识库测试历史记录
 * @param datasetId 知识库ID
 * @param params 分页参数
 * @returns 历史记录列表
 */
export const fetchTestingRecords = (datasetId: string, params: { page: number; limit: number }) => {
  return get<any>(`/datasets/${datasetId}/queries`, params);
};

// 文档管理相关接口

// 文档列表接口参数类型
export interface DocumentsListParams {
  page?: number;
  limit?: number;
  keyword?: string;
  [key: string]: any;
}

// 文档列表接口响应类型
export interface DocumentsListResponse {
  data: any[];
  total: number;
  page: number;
  limit: number;
}

// 获取文档列表
export const fetchDocuments = (datasetId: string, params: DocumentsListParams = {}) => {
  return get<DocumentsListResponse>(`/datasets/${datasetId}/documents`, params);
};

//添加新文档
export const addDocument = (datasetId: string, params: DocumentsListParams = {}) => {
  return post<DocumentsListResponse>(`/datasets/${datasetId}/documents`, params);
};
// 启用文档
export const enableDocument = (datasetId: string, documentId: string) => {
  return post<any>(`/datasets/${datasetId}/documents/${documentId}/enable`, {});
};

// 禁用文档
export const disableDocument = (datasetId: string, documentId: string) => {
  return post<any>(`/datasets/${datasetId}/documents/${documentId}/disable`, {});
};

// 删除文档
export const deleteDocument = (datasetId: string, documentId: string) => {
  return del<any>(`/datasets/${datasetId}/documents/${documentId}`);
};

// 归档文档
export const archiveDocument = (datasetId: string, documentId: string) => {
  return patch<any>(`/datasets/${datasetId}/documents/status/archive/batch?document_id=${documentId}`, {});
};

// 撤销归档文档
export const unarchiveDocument = (datasetId: string, documentId: string) => {
  return patch<any>(`/datasets/${datasetId}/documents/status/un_archive/batch?document_id=${documentId}`, {});
};

// 重命名文档
export const renameDocument = (datasetId: string, documentId: string, name: string) => {
  return post<any>(`/datasets/${datasetId}/documents/${documentId}/rename`, { name });
};

// 上传文档
export const uploadDocument = (datasetId: string, file: File, onProgress?: (progress: number) => void) => {
  const formData = new FormData();
  formData.append('file', file);

  return axios.post<any>(`/datasets/${datasetId}/documents/upload`, formData, {
    baseURL: import.meta.env.VITE_API_BASE_URL || "/api",
    headers: {
      'Content-Type': 'multipart/form-data',
      'Authorization': `Bearer ${getToken()}`
    },
    onUploadProgress: onProgress ? (progressEvent) => {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
      onProgress(percentCompleted);
    } : undefined
  });
};

// 获取文档段落列表
export interface SegmentsListParams {
  page?: number;
  limit?: number;
  keyword?: string;
  enabled?: 'all' | boolean;
}

export interface SegmentItem {
  id: string;
  content: string;
  word_count: number;
  tokens: number;
  segment_index: number;
  enabled: boolean;
  status: string;
  tags: string[];
  created_at: number;
  [key: string]: any;
}

export interface SegmentsListResponse {
  data: SegmentItem[];
  total: number;
  page: number;
  limit: number;
}

// 获取文档段落列表
export const fetchDocumentSegments = (datasetId: string, documentId: string, params: SegmentsListParams = {}) => {
  return get<SegmentsListResponse>(`/datasets/${datasetId}/documents/${documentId}/segments`, params);
};

// 获取文档详情
export interface DocumentDetail {
  id: string;
  name: string;
  fileSize?: string;
  created_at: number;
  updated_at: number;
  source?: string;
  doc_form?: string;
  max_segment_length?: number;
  avg_segment_length?: string;
  segment_count?: number;
  hit_rate?: string;
  hit_count?: number;
  embedding_time?: string;
  embedding_tokens?: string;
  indexing_status?: string;
  [key: string]: any;
}

export interface DocumentDetailResponse {
  data: DocumentDetail;
}

export const fetchDocumentDetail = (datasetId: string, documentId: string) => {
  return get<DocumentDetailResponse>(`/datasets/${datasetId}/documents/${documentId}`);
};

// 知识库成员管理相关接口类型

/**
 * 知识库成员对象接口
 */
export interface DatasetMember {
  id: string;
  dataset_id: string;
  account_id: string;
  role: string;
  created_at: number;
  updated_at: number;
  [key: string]: any;
}

/**
 * 知识库成员列表响应接口
 */
export interface DatasetMembersResponse {
  members: DatasetMember[];
}

/**
 * 添加成员请求参数
 */
export interface AddDatasetMemberRequest {
  account_id: string;
  role: string;
  [key: string]: any;
}

/**
 * 更新成员角色请求参数
 */
export interface UpdateDatasetMemberRequest {
  role: string;
  [key: string]: any;
}

/**
 * 获取知识库成员列表
 * @param datasetId 知识库ID
 * @returns 成员列表
 */
export const fetchDatasetMembers = (datasetId: string) => {
  return get<DatasetMembersResponse>(`/datasets/${datasetId}/members`);
};

/**
 * 添加知识库成员
 * @param datasetId 知识库ID
 * @param data 成员信息
 * @returns 添加的成员信息
 */
export const addDatasetMember = (datasetId: string, data: AddDatasetMemberRequest) => {
  return post<DatasetMember>(`/datasets/${datasetId}/members`, data);
};

/**
 * 删除知识库成员
 * @param datasetId 知识库ID
 * @param memberId 成员ID
 * @returns 删除结果
 */
export const deleteDatasetMember = (datasetId: string, memberId: string) => {
  return del<any>(`/datasets/${datasetId}/members/${memberId}`);
};

/**
 * 更新知识库成员角色
 * @param datasetId 知识库ID
 * @param memberId 成员ID
 * @param data 更新数据
 * @returns 更新后的成员信息
 */
export const updateDatasetMember = (datasetId: string, memberId: string, data: UpdateDatasetMemberRequest) => {
  return patch<DatasetMember>(`/datasets/${datasetId}/members/${memberId}`, data);
};

/**
 * 获取文档处理规则
 * @param documentId 文档ID
 * @returns 处理规则信息
 */
export const fetchDocumentProcessRule = (documentId: string) => {
  return get<ProcessRule>(`/datasets/process-rule?document_id=${documentId}`);
};

// 文件预览请求参数
export interface IndexingEstimateRequest {
  info_list: {
    data_source_type: string;
    file_info_list?: {
      file_ids: string[];
    };
  };
  indexing_technique: string;
  process_rule: {
    rules: {
      pre_processing_rules: Array<{
        id: string;
        enabled: boolean;
      }>;
      segmentation: {
        separator: string;
        max_tokens: number;
        chunk_overlap: number;
      };
    };
    mode: string;
  };
  doc_form: string;
  doc_language: string;
  [key: string]: any; // 添加索引签名
}

// 文件预览响应类型
export interface IndexingEstimateResponse {
  total_segments: number;
  preview: Array<{
    content: string;
    child_chunks: any[] | null;
  }>;
  qa_preview: any | null;
}

// 文件内容预览接口
export const indexingEstimate2 = (data: IndexingEstimateRequest) => {
  return post<IndexingEstimateResponse>('/datasets/indexing-estimate', data);
};
