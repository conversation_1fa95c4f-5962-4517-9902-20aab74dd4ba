<template>
  <div class="agents-container">
    <!-- 编辑智能体弹窗 -->
    <edit-agent-dialog v-model:visible="editDialogVisible" :agent="currentEditAgent" @refresh="getAgentsList" />
    <!-- 搜索和创建区域 -->
    <div class="header-actions">
      <div class="search-wrapper">
        <el-input
          v-model="searchQuery"
          placeholder="搜索智能体名称"
          class="search-input"
          clearable
          @change="getAgentsList"
        >
          <template #suffix>
            <img class="search-img" src="@/assets/images/ic_search_active.png" alt="" />
          </template>
        </el-input>
      </div>
    </div>

    <!-- 分类导航 -->
    <div class="category-box">
      <div class="category-nav">
        <div
          v-for="category in categories"
          :key="category.id"
          class="category-item"
          :class="{ active: currentCategory === category.id }"
          @click="changeCategory(category.id)"
        >
          {{ category.name }}
        </div>
      </div>
      <el-button type="primary" class="create-button" title="创建智能体" size="large" @click="handleCreateAgent">
        <img src="@/assets/images/ic_agent_white.png" alt="" class="create-btn-icon" />
        <div class="create-btn-title">创建智能体</div>
      </el-button>
    </div>

    <!-- 智能体列表 -->
    <div class="home-content">
      <div class="agents-grid">
        <div v-for="(agent, index) in agentsList" :key="index" class="agent-card" @click="handleStartAgent(agent)">
          <div class="card-content">
            <div class="card-main">
              <div class="menu-icon" @click.stop>
                <el-dropdown trigger="click" @command="command => handleCommand(command, agent)">
                  <span class="el-dropdown-link">
                    <el-icon><MoreFilled /></el-icon>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit">编辑信息</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
              <img
                class="agent-image"
                :src="iconUrl + agent.icon_url"
                alt=""
                v-if="agent.icon_type === 'image' && agent.icon_url"
              />
              <img v-else class="agent-image" src="@/assets/images/ic_qa_robot.png" alt="" />
              <div class="card-main-right">
                <h3 class="agent-name">{{ agent.name }}</h3>
                <p class="agent-desc line-clamp line-clamp-3">{{ agent.description }}</p>
              </div>
              <div class="edit-icon" @click.stop="handleEditAgent2(agent)">
                <el-icon :size="20"><Edit /></el-icon>
              </div>
              <div class="delete-icon" @click.stop="handleDeleteAgent(agent)">
                <el-icon :size="20"><Delete /></el-icon>
              </div>
            </div>
          </div>
          <div class="card-footer">
            <div class="agent-category">{{ AgentMode[agent.mode as keyof typeof AgentMode] }}</div>
            <el-button class="card-footer-button" type="primary" size="large" @click.stop="handleStartAgent(agent)"
              >开始使用</el-button
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { Search, Plus, Edit, Delete, MoreFilled } from "@element-plus/icons-vue";
import { get } from "@/utils/request";
import type { AppsRecord, AppsListPageResponse, InstalledAppsResponse } from "./types";
import { AgentMode } from "./types";
import { useRouter } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import { deleteAgent, putAgentDetail } from "@/api/agents";
import EditAgentDialog from "@/components/agents/EditAgentDialog.vue";

// 分类数据
const categories = [
  { id: "agent-chat", name: "智能体" },
  { id: "chat", name: "聊天助手" },
];
const iconUrl = import.meta.env.VITE_API_ICON_URL;
const router = useRouter();

const currentCategory = ref("agent-chat");
const searchQuery = ref("");

// 编辑弹窗相关状态
const editDialogVisible = ref(false);
const currentEditAgent = ref<AppsRecord | null>(null);

const agentsList = ref<AppsRecord[]>([]); // 获取智能体列表
const getAgentsList = async () => {
  let res = await get<AppsListPageResponse>("/apps", {
    page: 1,
    limit: 100,
    name: searchQuery.value,
    is_created_by_me: false,
    mode: currentCategory.value,
  });
  agentsList.value.length = 0;
  agentsList.value = [...res.data];
};
// 切换分类
const changeCategory = (category: string) => {
  currentCategory.value = category;
  getAgentsList();
};
// 创建智能体
const handleCreateAgent = () => {
  router.push("/agents/create");
};
// 开始使用智能体
const handleStartAgent = async (agent: AppsRecord) => {
  console.log("agentagentagent", agent);

  // 判断agent.name是否包含"ai批改"四个字
  if (agent.name && agent.name.includes("AI题目批改")) {
    // 如果包含，打开新标签页跳转到百度
    window.open("https://xlyyplus.eduer.co/xlyy/#/xlyy_sub/notBar/xlAIEssayGrading2", "_blank");
    return; // 提前返回，不执行后续代码
  }

  if (agent.name && agent.name.includes("AI命题质检助手")) {
    // 如果包含，打开新标签页跳转到百度
    window.open("https://xlyyplus.eduer.co/xlyy/#/xlyy_sub/notBar/aIAssistant", "_blank");
    return; // 提前返回，不执行后续代码
  }

  if (agent.name && agent.name.includes("报价助手")) {
    // 如果包含，打开新标签页跳转到百度
    window.open("https://ektom.eduer.co/ektom/h5/quotationSheetPC.html", "_blank");
    return; // 提前返回，不执行后续代码
  }

  if (agent.name && agent.name.includes("学科知识图谱")) {
    // 如果包含，打开新标签页跳转到百度
    window.open("https://xiaolejkp-dev.eduer.co/knowledgeGraph/#/", "_blank");
    return; // 提前返回，不执行后续代码
  }
  // 原有逻辑
  let res = await get<InstalledAppsResponse>("/installed-apps", { app_id: agent.id });
  if (res.installed_apps.length > 0) {
    // router.push(
    //   `/agents/chat?id=${res.installed_apps[0].id}&name=${agent.name}&icon_url=${encodeURIComponent(
    //     (agent.icon_url ? agent.icon_url : "") as string
    //   )}&icon=${encodeURIComponent((agent.icon ? agent.icon : "") as string) || ""}&icon_background=${
    //     encodeURIComponent((agent.icon_background ? agent.icon_background : "") as string) || ""
    //   }`
    // );
    router.push(`/agents/chat?id=${agent.id}`);
  }
};
// 编辑智能体
const handleEditAgent = (agent: AppsRecord) => {
  // 设置当前编辑的智能体并打开弹窗
  currentEditAgent.value = agent;
  editDialogVisible.value = true;
};

// 编辑智能体
const handleEditAgent2 = (agent: AppsRecord) => {
  router.push(`/agents/create?agentId=${agent.id}`);
};

// 删除智能体
const handleDeleteAgent = async (agent: AppsRecord) => {
  try {
    await ElMessageBox.confirm(`确定要删除智能体 ${agent.name} 吗？此操作不可恢复。`, "删除确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    await deleteAgent(agent.id);
    ElMessage.success("删除成功");
    getAgentsList(); // 刷新列表
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除智能体失败:", error);
    }
  }
};

// 处理下拉菜单命令
const handleCommand = (command: string, agent: AppsRecord) => {
  switch (command) {
    case "edit":
      handleEditAgent(agent);
      break;
    default:
      break;
  }
};

onMounted(() => {
  getAgentsList();
});
</script>

<style lang="scss" scoped>
.agents-container {
  height: 100%;
  padding: 32px 24px;
  display: flex;
  flex-direction: column;
  background-image: url("@/assets/images/default_bg.png");
  background-size: cover;
  .header-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    .search-wrapper {
      flex: 1;
      max-width: 480px;
      .search-input {
        :deep(.el-input__wrapper) {
          background: #ffffff;
          box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.04);
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #edeaea;
          padding: 0 16px;
          height: 36px;

          &:hover,
          &.is-focus {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }
        :deep(.el-input__inner) {
          height: 36px;
          font-size: 14px;
          &::placeholder {
            color: #909399;
          }
        }
        :deep(.search-icon) {
          font-size: 16px;
          color: #909399;
          margin-right: 4px;
        }
        .search-img {
          width: 20px;
          height: 20px;
          overflow: hidden;
        }
      }
    }
  }
  .category-box {
    display: flex;
    align-items: center;
  }
  .create-button {
    display: flex;
    align-items: center;
    padding: 8px;
    color: #fff;
    background: #3a67f8;
    border-radius: 4px 4px 4px 4px;
    .create-btn-icon {
      width: 20px;
      height: 20px;
      overflow: hidden;
    }
    .create-btn-title {
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
      margin-left: 6px;
    }
  }
  .category-nav {
    display: flex;
    flex: 1;
    overflow-x: auto;
    margin-right: 16px;
    .category-item {
      font-weight: 400;
      font-size: 14px;
      color: #000000;
      line-height: 16px;
      cursor: pointer;
      width: 76px;
      padding: 7px 0;
      text-align: center;
      border-radius: 3px;
      margin-right: 16px;
      &:last-child {
        margin-right: 0;
      }
      &:hover {
        font-weight: 600;
        color: #3a67f8;
        background-color: #ebebf0;
      }
      &.active {
        font-weight: 600;
        color: #3a67f8;
        background-color: #ebebf0;
      }
    }
  }
}
.home-content {
  margin-top: 32px;
  flex: 1;
  overflow-y: auto;
}
.agents-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  padding: 0;
  .agent-card {
    border-radius: 8px;
    cursor: pointer;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .card-content {
      flex: 1;
      overflow: hidden;
    }
    .card-main {
      padding: 26px 24px 40px 24px;
      display: flex;
      position: relative;
      background-color: #fbfbfa;

      .menu-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        color: #909399;
        cursor: pointer;
        z-index: 3;

        .el-dropdown-link {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;

          .el-icon {
            font-size: 20px;
          }
        }

        &:hover {
          color: #409eff;
        }
      }
      .agent-image {
        width: 54px;
        height: 54px;
        border-radius: 50%;
        overflow: hidden;
      }
      .card-main-right {
        margin-left: 14px;
        flex: 1;
        overflow: hidden;
        .agent-name {
          font-weight: 600;
          font-size: 16px;
          color: #000000;
          line-height: 19px;
        }
        .agent-desc {
          margin-top: 6px;
          font-weight: 400;
          font-size: 12px;
          color: #4a4848;
          line-height: 14px;
          height: 42px;
        }
      }
      .edit-icon {
        position: absolute;
        bottom: 10px;
        right: 16px;
        color: #909399;
        cursor: pointer;
        z-index: 2;
        &:hover {
          color: #409eff;
        }
      }
      .delete-icon {
        position: absolute;
        bottom: 10px;
        right: 50px;
        color: #909399;
        cursor: pointer;
        z-index: 2;
        &:hover {
          color: #409eff;
        }
      }
    }

    .card-mode {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 14px 16px;
      font-size: 13px;
      color: #909399;
      .usage-count {
        display: flex;
        align-items: center;

        .el-icon {
          font-size: 16px;
          opacity: 0.5;
          margin-right: 4px;
        }
      }
    }

    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  }
}
.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background-color: #fff;
  .agent-category {
    font-weight: 400;
    font-size: 12px;
    color: #4f4f4f;
    line-height: 14px;
  }
  .card-footer-button {
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 16px;
    background-color: #3a67f8;
  }
}
</style>
