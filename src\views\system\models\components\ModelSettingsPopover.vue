<template>
  <div class="model-settings-popover">
    <el-popover
      placement="bottom-end"
      :width="400"
      trigger="click"
      popper-class="model-settings-popper"
      v-model:visible="popoverVisible"
    >
      <template #reference>
        <el-button plain :icon="Setting">系统模型设置</el-button>
      </template>
      <div class="settings-container">
        <!-- 动态生成模型设置区域 -->
        <div v-for="(group, groupIndex) in modelSettings" :key="groupIndex" class="model-section">
          <div class="model-section-title">
            {{ group.title }}
            <el-tooltip
              :teleported="false"
              popper-class="desc-tooltip"
              :content="group.desc"
              placement="top"
              effect="light"
            >
              <el-icon class="model-section-info"><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>

          <!-- 使用选择器实现模型选择 -->
          <div class="model-select-container">
            <el-select
              v-model="formData[group.key]"
              class="model-select"
              popper-class="model-select-dropdown"
              :teleported="false"
              :loading="loading"
              :disabled="loading"
              :placeholder="`请选择${group.title}`"
              no-data-text="暂无数据"
            >
              <!-- 自定义前缀显示模型类型 -->
              <template #prefix>
                <template v-if="getSelectedModel(group)?.type">
                  <el-tag effect="plain">{{ getSelectedModel(group)?.type }}</el-tag>
                </template>
              </template>
              <!-- 分组显示模型 -->
              <el-option-group
                v-for="category in group.categories"
                :key="category.provider"
                :label="category.label?.zh_Hans || category.provider"
              >
                <el-option
                  v-for="model in category.models"
                  :key="model.model"
                  :label="model.label?.zh_Hans || model.model"
                  :value="model.model"
                >
                  <div class="model-option-content">
                    <img class="category-icon" :src="iconUrl + category?.icon_small?.zh_Hans" alt="" />
                    <div>{{ model.label?.zh_Hans || model.model }}</div>
                  </div>
                </el-option>
              </el-option-group>
            </el-select>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="settings-footer">
          <el-button plain @click="cancelSettings" :disabled="loading">取消</el-button>
          <el-button type="primary" @click="saveModelSettings" :loading="loading" :disabled="loading">保存</el-button>
        </div>
      </div>
    </el-popover>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { Setting, QuestionFilled } from "@element-plus/icons-vue";
import { get, post } from "@/utils/request";
import { ElMessage } from "element-plus";
import type {
  ModelSettingsGroup,
  ModelSettingsItem,
  ModelApiResponse,
  ModelCategoryItem,
  ModelItem,
  DefaultModelResponse,
} from "../types";
const iconUrl = import.meta.env.VITE_API_ICON_URL;
// 控制弹窗显示
const popoverVisible = ref(false);
// 模型设置数据
const modelSettings = ref<ModelSettingsGroup[]>([
  {
    title: "系统推理模型",
    key: "llm",
    api_path: "/workspaces/current/models/model-types/llm",
    items: [],
    categories: [],
    desc: "设置创建应用使用的默认推理模型，以及对话名称生成、下一步问题建议等功能也会使用该默认推理模型。",
  },
  {
    title: "Embedding 模型",
    key: "text-embedding",
    api_path: "/workspaces/current/models/model-types/text-embedding",
    items: [],
    categories: [],
    desc: "设置知识库文档嵌入处理的默认模型，检索和导入知识库均使用该Embedding模型进行向量化处理，切换后将导致已导入的知识库与问题之间的向量维度不一致，从而导致检索失败。为避免检索失败，请勿随意切换该模型。",
  },
  {
    title: "Rerank 模型",
    key: "rerank",
    api_path: "/workspaces/current/models/model-types/rerank",
    items: [],
    categories: [],
    desc: "重排序模型将根据候选文档列表与用户问题语义匹配度进行重新排序，从而改进语义排序的结果。",
  },
  {
    title: "语音转文本模型",
    key: "speech2text",
    api_path: "/workspaces/current/models/model-types/speech2text",
    items: [],
    categories: [],
    desc: "设置对话中语音转文字输入的默认使用模型。",
  },
  {
    title: "文本转语音模型",
    key: "tts",
    api_path: "/workspaces/current/models/model-types/tts",
    items: [],
    categories: [],
    desc: "设置对话中文字转语音输出的默认使用模型。",
  },
]);

// 加载状态
const loading = ref(false);

// 表单数据，用于保存每个选择器选中的值
const formData = ref<Record<string, string>>({});

// 初始化表单数据
const initFormData = () => {
  // 初始化为空字符串，表示未选中任何模型
  // 默认值将通过getDefaultModels方法从接口获取
  modelSettings.value.forEach(group => {
    formData.value[group.key] = "";
  });
};
// 选中模型的缓存对象
const selectedModelsCache = computed(() => {
  const cache: Record<
    string,
    {
      item?: ModelSettingsItem;
      category?: ModelCategoryItem;
      model?: ModelItem;
    }
  > = {};

  modelSettings.value.forEach(group => {
    const selectedModel = formData.value[group.key];
    if (!selectedModel) {
      cache[group.key] = {};
      return;
    }

    // 查找匹配的扁平化item
    const item = group.items.find(item => item.model === selectedModel);

    // 查找匹配的category和model
    let matchedCategory: ModelCategoryItem | undefined;
    let matchedModel: ModelItem | undefined;

    for (const category of group.categories) {
      if (category.models) {
        const model = category.models.find(model => model.model === selectedModel);
        if (model) {
          matchedCategory = category;
          matchedModel = model;
          break;
        }
      }
    }

    cache[group.key] = {
      item,
      category: matchedCategory,
      model: matchedModel,
    };
  });

  return cache;
});
// 获取当前选中的模型
const getSelectedModel = (group: ModelSettingsGroup): ModelSettingsItem | undefined => {
  return selectedModelsCache.value[group.key]?.item;
};
// 获取模型设置的方法
const getModelSettings = async () => {
  loading.value = true;
  try {
    // 并行请求所有模型数据
    const promises = modelSettings.value.map(async group => {
      try {
        const response = await get<ModelApiResponse>(group.api_path);

        if (response && response.data) {
          // 保存原始分类数据
          group.categories = response.data;

          // 同时将数据转换为扁平化的items数组，用于兼容现有逻辑
          const items: ModelSettingsItem[] = [];

          response.data.forEach(category => {
            if (category.models && category.models.length > 0) {
              category.models.forEach(model => {
                items.push({
                  model: model.model || "", // 直接使用model字段
                  name: model.label?.zh_Hans || model.model || "",
                  type: model.model_properties?.mode,
                  icon: "",
                });
              });
            }
          });

          // 默认不选中任何项
          // 选中状态由el-select组件通过v-model自动管理

          // 更新模型组的扁平化数据
          group.items = items;
        }
      } catch (error) {
        // 如果请求失败，使用空数组
        group.items = [];
        group.categories = [];
      }
    });

    // 等待所有请求完成
    await Promise.all(promises);

    // 初始化表单数据
    initFormData();
  } catch (error) {
  } finally {
    loading.value = false;
  }
};
// 用于存储表单数据备份的引用
// 使用ref而不是const，这样可以在保存成功后更新备份
// 初始化为空对象，在保存时再赋值
// 定义在函数外部，这样在多次保存操作之间可以保持备份数据
const formDataBackup = ref<Record<string, string>>({});
// 保存模型设置
const saveModelSettings = async () => {
  // 在保存前先保存一份当前表单数据的副本
  formDataBackup.value = { ...formData.value };

  // 设置加载状态
  loading.value = true;

  try {
    // 组装API需要的数据格式
    const modelSettings = Object.entries(selectedModelsCache.value).map(([key, value]) => {
      // 如果没有选中模型或没有找到匹配的category/model，则只返回model_type
      if (!value.model || !value.category) {
        return { model_type: key };
      }

      // 否则返回完整的信息
      return {
        model_type: key,
        provider: value.category?.provider || "",
        model: value.model?.model || "",
      };
    });

    // 组装最终的请求数据
    const requestData = {
      model_settings: modelSettings,
    };

    // 调用API保存设置
    await post("/workspaces/current/default-model", requestData);

    // 提示修改成功
    ElMessage.success("模型设置修改成功");

    // 保存成功后更新备份数据，以便于下次保存失败时恢复到最新的成功状态
    // 使用.value访问和修改ref对象的值
    formDataBackup.value = { ...formData.value };

    // 关闭弹窗
    popoverVisible.value = false;
  } catch (error) {
    // 关闭弹窗
    popoverVisible.value = false;

    // 直接使用备份的表单数据恢复初始值，不需要再次调用接口
    formData.value = { ...formDataBackup.value };
  } finally {
    loading.value = false;
  }
};
// 取消操作
const cancelSettings = () => {
  popoverVisible.value = false;
};
// 获取默认模型设置
const getDefaultModels = async () => {
  try {
    // 并行请求所有模型类型的默认值
    const promises = modelSettings.value.map(async group => {
      try {
        const { data } = await get<DefaultModelResponse>(`/workspaces/current/default-model?model_type=${group.key}`);

        if (data) {
          if (data.model) {
            // 设置默认选中的模型值
            formData.value[group.key] = data.model;
          }
        }
      } catch (error) {}
    });

    // 等待所有请求完成
    await Promise.all(promises);
  } catch (error) {}
};
onMounted(async () => {
  // 初始化表单数据
  initFormData();
  // 组件挂载时先获取模型设置
  await getModelSettings();
  // 然后获取默认模型并设置选中状态
  await getDefaultModels();
});
</script>
<style scoped lang="scss">
.model-settings-popover {
  display: inline-block;
}
:deep(.model-settings-popper) {
  padding: 0;
}
.settings-container {
  padding: 10px;
}
.model-section {
  margin-bottom: 15px;
  .model-section-title {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
    .model-section-info {
      color: #909399;
      font-size: 14px;
      margin-left: 5px;
      cursor: pointer;
    }
  }
}
// 模型选择器容器
.model-select-container {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
// 选择器样式
.model-select {
  flex: 1;
}
:deep(.el-select) {
  width: 100%;
}
:deep(.el-select .el-input__wrapper) {
  background-color: #f9faff;
  border-color: #ebeef5;
  padding: 0 12px;
  height: 40px;
}
:deep(.el-select .el-input__prefix) {
  margin-right: 8px;
}
:deep(.model-select-dropdown) {
  .el-select-dropdown__item {
    padding: 0;
    height: auto;
  }
}
// 选项内容样式
.model-option-content {
  display: flex;
  align-items: center;
  padding: 4px 15px;
  .category-icon {
    width: 20px;
    height: 20px;
    overflow: hidden;
    margin-right: 6px;
  }
}
.settings-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  gap: 10px;
}
.desc-tooltip {
  max-width: 300px;
}
</style>
