/**
 * 将时间戳转换为相对时间（例如：几分钟前，几小时前，几天前）
 * @param timestamp 时间戳（秒或毫秒）
 * @returns 格式化后的相对时间字符串
 */
export function formatRelativeTime(timestamp: number): string {
  // 判断是否为秒级时间戳（小于 10000000000 认为是秒级时间戳）
  const milliseconds = timestamp < 10000000000 ? timestamp * 1000 : timestamp;

  const now = Date.now();
  const diff = now - milliseconds; // 时间差（毫秒）

  // 如果是未来时间，直接返回刚刚
  if (diff < 0) {
    return "刚刚";
  }

  // 转换为秒
  const seconds = Math.floor(diff / 1000);

  // 小于 1 分钟
  if (seconds < 60) {
    return "刚刚";
  }

  // 转换为分钟
  const minutes = Math.floor(seconds / 60);
  if (minutes < 60) {
    return `${minutes}分钟前`;
  }

  // 转换为小时
  const hours = Math.floor(minutes / 60);
  if (hours < 24) {
    return `${hours}小时前`;
  }

  // 转换为天
  const days = Math.floor(hours / 24);
  if (days < 30) {
    return `${days}天前`;
  }

  // 转换为月
  const months = Math.floor(days / 30);
  if (months < 12) {
    return `${months}个月前`;
  }

  // 转换为年
  const years = Math.floor(months / 12);
  return `${years}年前`;
}
