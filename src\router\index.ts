import { createRouter, createWeb<PERSON>ashHistory } from "vue-router";
import type { RouteRecordRaw } from "vue-router";
// Commented out for debugging
import { getToken } from "@/utils/user";
import Layout from "@/layouts/Index.vue";

const routes: RouteRecordRaw[] = [
  {
    path: "/",
    component: Layout,
    redirect: "/agents",
    children: [
      {
        path: "home",
        name: "home",
        component: () => import("@/views/home/<USER>"),
      },
      {
        path: "agents",
        name: "agents",
        component: () => import("@/views/agents/Index.vue"),
        meta: {
          title: "智能体",
          icon: "icon-avatar",
        },
      },
      {
        path: "agents/chat",
        name: "agentsChat",
        component: () => import("@/views/agents/Chat.vue"),
        meta: {
          title: "智能体对话",
          icon: "icon-component",
        },
      },
      {
        path: "agents/create",
        name: "agentsCreate",
        component: () => import("@/views/agents/Create.vue"),
        meta: {
          title: "创建智能体",
          icon: "icon-component",
        },
      },
      {
        path: "agents/edit/:id",
        name: "agentsEdit",
        component: () => import("@/views/agents/Index.vue"),
        meta: {
          title: "编辑智能体",
          icon: "icon-component",
        },
      },
      {
        path: "system/models",
        name: "systemModels",
        component: () => import("@/views/system/models/Index.vue"),
      },
      {
        path: "system/structure",
        name: "systemStructure",
        component: () => import("@/views/system/structure/Index.vue"),
      },
      {
        path: "qaAssistant",
        name: "qaAssistant",
        component: () => import("@/views/qaAssistant/Index.vue"),
        meta: {
          title: "AI问答助手",
          icon: "icon-component",
        },
      },
      {
        path: "knowledge",
        name: "knowledge",
        component: () => import("@/views/knowledge/Index.vue"),
        meta: {
          title: "知识库",
          icon: "icon-component",
        },
      },
      {
        path: "knowledge/create",
        name: "knowledgeCreate",
        component: () => import("@/views/knowledge/Create.vue"),
        meta: {
          title: "创建知识库",
          icon: "icon-component",
        },
      },
      {
        path: "knowledge/detail/:id",
        name: "knowledgeDetail",
        component: () => import("@/views/knowledge/Detail.vue"),
      },
      {
        path: "knowledge/edit/:id",
        name: "knowledgeEdit",
        component: () => import("@/views/knowledge/Edit.vue"),
        meta: {
          title: "编辑知识库",
          icon: "icon-component",
        },
      },
      {
        path: "knowledge/:knowledgeId/document/:documentId",
        name: "documentDetail",
        component: () => import("@/views/knowledge/DocumentDetail.vue"),
        meta: {
          title: "文档段落详情",
          icon: "icon-document",
        },
      },
      {
        path: "knowledge/space",
        name: "knowledgeSpace",
        component: () => import("@/views/knowledge/Space.vue"),
        meta: {
          title: "知识空间",
          icon: "icon-component",
        },
      },
      {
        path: "knowledge/space/:id",
        name: "knowledgeSpaceDetail",
        component: () => import("@/views/knowledge/Index.vue"),
        meta: {
          title: "知识空间详情",
          icon: "icon-component",
        },
      },
      {
        path: "toolbox/chatflow",
        name: "toolboxChatflow",
        component: () => import("@/views/toolbox/chatflow/Index.vue"),
      },
      {
        path: "toolbox/workflow",
        name: "toolboxWorkflow",
        component: () => import("@/views/toolbox/workflow/Index.vue"),
      },
    ],
  },
  {
    path: "/activate",
    name: "Activate",
    component: () => import("@/views/Activate.vue"),
  },
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/Login.vue"),
  },
  // 新增嵌入式聊天机器人路由
  {
    path: "/chatbot/:token",
    name: "chatbot",
    component: () => import("@/views/chatbot/Index.vue"),
    meta: {
      title: "聊天机器人",
      hideInMenu: true,
      layout: "blank", // 使用空白布局
    },
  },
  // 嵌入示例页面
  {
    path: "/chatbot-embed-example",
    name: "chatbotEmbedExample",
    component: () => import("@/views/chatbot/EmbedExample.vue"),
    meta: {
      title: "嵌入式聊天机器人示例",
    },
  },
];

const router = createRouter({
  history: createWebHashHistory(import.meta.env.VITE_BASE_URL),
  routes,
});

// 路由守卫
router.beforeEach((to, _, next) => {
  const token = getToken();

  if (to.path === "/activate") {
    next();
    return;
  }
  // 已登录状态下访问登录页，重定向到首页
  if (token && to.path === "/login") {
    next("/");
    return;
  }

  // 未登录状态下访问非登录页，重定向到登录页
  if (!token && to.path !== "/login") {
    next({
      path: "/login",
      query: { redirect: to.fullPath },
    });
    return;
  }

  // 已登录状态下访问登录页，重定向到首页
  // if (to.path === "/knowledge") {
  //   window.location.href = '/knowledge#' + new Date().getTime();
  //   next();
  //   return;
  // }

  next();
});

export default router;
