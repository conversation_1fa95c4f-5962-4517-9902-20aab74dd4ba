import axios from 'axios';
import { getChatbotAccessToken } from '@/utils/chatbot';

// 聊天机器人API基础URL
const CHATBOT_API_BASE = 'http://www.ttsgo.com:18080/api';

// 创建专用的axios实例用于聊天机器人API
const chatbotService = axios.create({
  baseURL: CHATBOT_API_BASE,
  timeout: 60000,
  headers: {
    "Content-Type": "application/json;charset=utf-8",
  },
});

// 聊天机器人请求拦截器
chatbotService.interceptors.request.use(
  (config) => {
    // 从URL获取token
    const pathParts = window.location.href.split('/');
    const token = pathParts[pathParts.length - 1] || '';

    // 获取token对应的访问令牌
    const accessToken = getChatbotAccessToken(token);
    console.log('访问令牌:', accessToken);

    if (accessToken && config.headers) {
      config.headers['Authorization'] = `Bearer ${accessToken}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 修改get方法定义，支持传递headers
const get = <T = unknown>(url: string, params?: any, config?: any): Promise<T> => {
  return chatbotService.get(url, { params, ...config });
};

export const post = <T = unknown>(url: string, data?: RequestData): Promise<T> => {
  return chatbotService.post(url, data);
};

/**
 * 获取访问令牌
 * @param token 聊天机器人token
 */
export const fetchAccessToken = (token: string) => {
  console.log('获取访问令牌:', token);
  return get<{ access_token: string }>('/passport', {}, {
    headers: {
      'X-App-Code': token
    }
  });
};

/**
 * 获取聊天机器人站点信息
 */
export const fetchSiteInfo = () => {
  return get<any>('/site');
};

/**
 * 获取聊天参数配置
 */
export const fetchChatParams = () => {
  return get<any>('/parameters');
};

/**
 * 获取聊天元数据
 */
export const fetchChatMeta = () => {
  return get<any>('/meta');
};

/**
 * 获取会话列表
 * @param params 查询参数
 */
export const fetchConversations = (params?: {
  last_id?: string;
  pinned?: boolean;
  limit?: number;
}) => {
  return get<any>('/conversations', params);
};

/**
 * 获取某个会话的消息列表
 * @param conversationId 会话ID
 */
export const fetchChatMessages = (conversationId: string) => {
  return get<any>('/messages', {
    conversation_id: conversationId,
    limit: 20,
    last_id: ''
  });
};

// 定义流式请求的回调函数类型
interface StreamCallbacks {
  onMessage: (text: string) => void;
  onError?: (error: any) => void;
  onComplete?: () => void;
}

// 定义请求参数类型
interface RequestData {
  [key: string]: any;
}

// 为chatbotService创建专用的postStream函数
const postStreamWithChatbot = (url: string, data: RequestData, callbacks: StreamCallbacks) => {
  const { onMessage, onError, onComplete } = callbacks;
  let previousResponseLength = 0;

  chatbotService
    .request({
      method: "post",
      url,
      data,
      responseType: "stream",
      onDownloadProgress: progressEvent => {
        try {
          const responseText = progressEvent.event.target.responseText;

          if (responseText && responseText.length > previousResponseLength) {
            // 只获取新增部分
            const newChunk = responseText.substring(previousResponseLength);
            previousResponseLength = responseText.length;
            onMessage(newChunk);
          }
        } catch (error) {
          onError?.(error as Error);
        }
      },
    })
    .then(() => {
      onComplete?.();
    })
    .catch(error => {
      onError?.(error);
    });
};

/**
 * 发送聊天消息
 * @param data 消息数据
 * @param callbacks 回调函数，原始数据将直接传递给组件处理
 */
export const sendChatMessage = (
  data: {
    conversation_id?: string;
    query: string;
    inputs?: Record<string, any>;
    files?: any[];
    response_mode?: string;
    parent_message_id?: string | null;
  },
  callbacks: {
    onMessage: (chunk: string) => void;
    onError?: (error: any) => void;
    onComplete?: () => void;
  }
) => {
  // 创建AbortController，以便可以取消请求

  // 调用postStreamWithChatbot函数处理请求，直接将原始数据传递给组件
  return postStreamWithChatbot('/chat-messages', data, {
    onMessage: callbacks.onMessage,
    onError: callbacks.onError,
    onComplete: callbacks.onComplete
  });

};

/**
 * 更新消息反馈
 * @param messageId 消息ID
 * @param feedback 反馈数据
 */
export const updateFeedback = (messageId: string, feedback: { rating: 'like' | 'dislike' }) => {
  return post<any>(`/messages/${messageId}/feedbacks`, feedback);
};

/**
 * 获取推荐问题
 * @param messageId 消息ID
 * @returns 推荐问题列表
 */
export const fetchSuggestedQuestions = (messageId: string) => {
  return get<{ data: string[] }>(`/messages/${messageId}/suggested-questions`);
};
