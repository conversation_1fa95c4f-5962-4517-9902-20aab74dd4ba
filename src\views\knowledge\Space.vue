<template>
  <div class="knowledge-space-container">
    <div class="space-header">
      <div class="space-info">
        <div class="space-avatar">
          <img v-if="spaceInfo.icon" :src="spaceInfo.icon" alt="space icon" />
          <div v-else class="default-avatar">{{ spaceInfo.name ? spaceInfo.name.substr(0, 1) : '?' }}</div>
        </div>
        <div class="space-title">
          <h2>{{ spaceInfo.name }}</h2>
          <div class="space-meta">
            <span>创建于 {{ spaceInfo.createdAt }}</span>
            <el-divider direction="vertical" />
            <span>{{ spaceInfo.memberCount }}人</span>
          </div>
        </div>
      </div>
      <div class="space-actions">
        <el-button type="primary" @click="showEditDialog">
          编辑信息
        </el-button>
      </div>
    </div>

    <div class="space-content">
      <div class="content-header">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索成员"
          :prefix-icon="Search"
          clearable
          @input="handleSearch"
          @clear="handleSearch"
        />
        <el-button type="primary" @click="handleAddMember">
          <el-icon><Plus /></el-icon>
          添加成员
        </el-button>
      </div>

      <div class="member-list">
        <el-table :data="memberList" style="width: 100%">
          <el-table-column prop="name" label="成员信息" />
          <el-table-column prop="role" label="角色">
            <template #default="scope">
              <span>{{ scope.row.role == 'admin' ? '管理员' : '成员' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="joinTime" label="加入时间" />
          <el-table-column label="操作" width="180">
            <template #default="scope">
              <el-button 
                v-if="scope.row.role !== 'admin'"
                type="primary" 
                size="small" 
                plain 
                @click="handleSetAdmin(scope.row)"
              >
                设为管理员
              </el-button>
              <el-button 
                v-else
                type="warning" 
                size="small" 
                plain 
                @click="handleCancelAdmin(scope.row)"
              >
                取消管理员
              </el-button>
              <el-button 
                type="danger" 
                size="small" 
                plain 
                @click="handleRemoveMember(scope.row)"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="empty-tip" v-if="memberList.length === 0 && !loading">
        <el-empty description="暂无成员" />
      </div>

      <div class="loading" v-if="loading">
        <el-skeleton :rows="3" animated />
      </div>
    </div>

    <!-- 编辑知识空间组件 -->
    <space-edit
      v-model:visible="editDialogVisible"
      :space-data="spaceInfo"
      :is-create="false"
      @save="handleSpaceSave"
    />

    <!-- 添加成员对话框 -->
    <el-dialog
      v-model="addMemberDialogVisible"
      title="添加成员"
      width="620px"
      :before-close="() => addMemberDialogVisible = false"
    >
      <div class="member-dialog-content">
        <el-select
          v-model="selectedMemberIds"
          multiple
          filterable
          placeholder="请选择成员"
          style="width: 100%"
          @change="handleSelectedMembersChange"
        >
          <el-option
            v-for="member in workspaceMembers"
            :key="member.id"
            :label="member.name"
            :value="member.id"
            :disabled="isExistingMember(member.id)"
          >
            <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
              <div style="display: flex; align-items: center;">
                <el-avatar 
                  :size="28" 
                  style="margin-right: 12px; background-color: #409EFF; color: white; font-size: 14px;"
                >
                  {{ member.avatar_url ? '' : member.name.charAt(0).toUpperCase() }}
                </el-avatar>
                <span style="font-size: 14px;">{{ member.name }}</span>
              </div>
              <el-tag size="small" type="success" v-if="isExistingMember(member.id)">已添加</el-tag>
            </div>
          </el-option>
        </el-select>
        
        <div class="selected-members-container" v-if="selectedMembers.length > 0">
          <div class="selected-title">已选择 {{ selectedMembers.length }} 位成员</div>
          <div class="selected-members-list">
            <div v-for="member in selectedMembers" :key="member.id" class="selected-member-item">
              <div class="selected-member-info">
                <el-avatar 
                  :size="28"
                  class="selected-member-avatar"
                  style="background-color: #409EFF; color: white; font-size: 14px;"
                >
                  {{ member.avatar_url ? '' : member.name.charAt(0).toUpperCase() }}
                </el-avatar>
                <span class="selected-member-name">{{ member.name }}</span>
              </div>
              <el-icon class="remove-selected" @click="removeSelectedMember(member)">
                <Close />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addMemberDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddMembers">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 使用SpaceMember组件 (暂时隐藏) -->
    <!-- 
    <space-member
      v-model:visible="addMemberDialogVisible"
      :space-id="spaceId"
      :current-members="memberList"
      @save="handleMemberSave"
    />
    -->
  </div>
</template>

<script>
import { Search, Plus, Close } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import SpaceEdit from '@/components/knowledge/SpaceEdit.vue';
// import SpaceMember from '@/components/knowledge/SpaceMember.vue';
import { 
  getSpaceDetail, 
  updateSpace, 
  getSpaceMembers, 
  addSpaceMembers, 
  updateMemberRole, 
  removeMember 
} from '@/api/knowledge/space';
import { fetchWorkspaceMembers } from '@/api/knowledge/index';

export default {
  name: 'KnowledgeSpace',
  components: {
    SpaceEdit,
    // SpaceMember
  },
  data() {
    return {
      spaceId: '',
      spaceInfo: {
        id: '',
        name: '',
        description: '',
        icon: '',
        memberCount: 0,
        createdAt: ''
      },
      searchKeyword: '',
      memberList: [],
      loading: false,
      editDialogVisible: false,
      addMemberDialogVisible: false,
      selectedMemberIds: [],
      workspaceMembers: [],
      selectedMembers: []
    };
  },
  created() {
    this.spaceId = this.$route.query.id || '';
    this.fetchSpaceInfo();
    this.fetchMembers();
    this.fetchWorkspaceMembers(); // 获取工作空间成员列表
  },
  methods: {
    fetchSpaceInfo() {
      this.loading = true;
      getSpaceDetail(this.spaceId)
        .then(res => {
          this.spaceInfo = {
            id: res.id,
            name: res.name,
            description: res.description || '',
            logo: res.logo || '',
            memberCount: 0,
            createdAt: res.created_at ? new Date(res.created_at).toLocaleDateString() : '未知'
          };
        })
        .catch(error => {
          console.error('获取空间详情失败', error);
          ElMessage.error('获取空间详情失败');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    fetchMembers() {
      // 获取成员列表
      this.loading = true;
      getSpaceMembers(this.spaceId)
        .then(res => {
          if (res.members && Array.isArray(res.members)) {
            // 将API返回的成员数据转换为前端需要的格式
            this.memberList = res.members.map(member => ({
              id: member.account_id,
              name: member.name, // 暂时使用ID作为名称，实际可能需要额外API获取用户信息
              role: member.role,
              joinTime: '未知', // API中可能没有加入时间
              email: '',
              account_id: member.account_id // 保留原始ID用于API调用
            }));
            console.log("res.members", res.members);
            console.log("this.memberList", this.memberList);
          } else {
            this.memberList = [];
          }
        })
        .catch(error => {
          console.error('获取成员列表失败', error);
          ElMessage.error('获取成员列表失败');
          this.memberList = [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    fetchWorkspaceMembers() {
      this.loading = true;
      fetchWorkspaceMembers()
        .then(res => {
          if (res && res.accounts) {
            this.workspaceMembers = res.accounts.map(account => ({
              id: account.id,
              name: account.name || account.id,
              email: account.email || '',
              avatar_url: account.avatar_url || null
            }));
          }
        })
        .catch(error => {
          console.error('获取工作空间成员失败', error);
          ElMessage.error('获取工作空间成员失败');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSearch() {
      // 搜索成员
      this.fetchMembers();
    },
    handleAddMember() {
      // 打开添加成员对话框前，初始化已选成员
      this.selectedMembers = [];
      this.selectedMemberIds = [];
      
      // 获取现有成员信息
      this.fetchMembers();
      
      // 打开对话框
      this.addMemberDialogVisible = true;
    },
    handleSelectedMembersChange(ids) {
      this.selectedMembers = this.workspaceMembers.filter(member => ids.includes(member.id));
    },
    removeSelectedMember(member) {
      this.selectedMemberIds = this.selectedMemberIds.filter(id => id !== member.id);
      this.selectedMembers = this.selectedMembers.filter(m => m.id !== member.id);
    },
    confirmAddMembers() {
      // 添加成员
      if (!this.selectedMemberIds || this.selectedMemberIds.length === 0) {
        return;
      }
      
      this.loading = true;
      
      // 成功添加的成员计数
      let successCount = 0;
      // 总添加请求数
      const totalRequests = this.selectedMemberIds.length;
      // 已完成的请求数
      let completedRequests = 0;
      
      // 批量添加所有成员，通过Promise.all并行处理
      const addPromises = this.selectedMemberIds.map(accountId => {
        return addSpaceMembers(this.spaceId, accountId, 'member')
          .then(() => {
            successCount++;
          })
          .catch(error => {
            console.error(`添加成员 ${accountId} 失败:`, error);
          })
          .finally(() => {
            completedRequests++;
            
            // 所有请求完成后进行处理
            if (completedRequests === totalRequests) {
              if (successCount > 0) {
                ElMessage.success(`成功添加${successCount}个成员`);
                this.addMemberDialogVisible = false;
                this.fetchMembers(); // 刷新成员列表
              }
              this.loading = false;
            }
          });
      });
      
      // 开始执行所有添加请求
      Promise.all(addPromises).catch(() => {
        this.loading = false;
      });
    },
    showEditDialog() {
      this.editDialogVisible = true;
    },
    handleSpaceSave(spaceData) {
      this.loading = true;
      updateSpace(this.spaceId, {
        name: spaceData.name,
        description: spaceData.description || '',
        logo: spaceData.logo || ''
      })
        .then(res => {
          ElMessage.success('空间信息保存成功');
          this.editDialogVisible = false;
          this.fetchSpaceInfo(); // 刷新空间信息
        })
        .catch(error => {
          console.error('更新空间信息失败', error);
          ElMessage.error('更新空间信息失败');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSetAdmin(member) {
      ElMessageBox.confirm(
        `确定将 ${member.name} 设为管理员吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        this.loading = true;
        // 使用member.account_id作为API调用的ID
        updateMemberRole(this.spaceId, member.account_id, 'admin')
          .then(() => {
            ElMessage.success(`已将 ${member.name} 设为管理员`);
            this.fetchMembers(); // 刷新成员列表
          })
          .catch(error => {
            console.error('设置管理员失败', error);
            ElMessage.error('设置管理员失败');
          })
          .finally(() => {
            this.loading = false;
          });
      }).catch(() => {});
    },
    handleCancelAdmin(member) {
      ElMessageBox.confirm(
        `确定取消 ${member.name} 的管理员权限吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        this.loading = true;
        // 使用member.account_id作为API调用的ID
        updateMemberRole(this.spaceId, member.account_id, 'member')
          .then(() => {
            ElMessage.success(`已取消 ${member.name} 的管理员权限`);
            this.fetchMembers(); // 刷新成员列表
          })
          .catch(error => {
            console.error('取消管理员失败', error);
            ElMessage.error('取消管理员失败');
          })
          .finally(() => {
            this.loading = false;
          });
      }).catch(() => {});
    },
    handleRemoveMember(member) {
      ElMessageBox.confirm(
        `确定将 ${member.name} 从空间中移除吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'danger'
        }
      ).then(() => {
        this.loading = true;
        // 使用member.account_id作为API调用的ID
        removeMember(this.spaceId, member.account_id)
          .then(() => {
            ElMessage.success(`已将 ${member.name} 从空间中移除`);
            this.fetchMembers(); // 刷新成员列表
          })
          .catch(error => {
            console.error('移除成员失败', error);
            ElMessage.error('移除成员失败');
          })
          .finally(() => {
            this.loading = false;
          });
      }).catch(() => {});
    },
    isExistingMember(id) {
      return this.memberList.some(member => member.id === id);
    }
  }
};
</script>

<style scoped lang="scss">
.knowledge-space-container {
  padding: 24px;
  
  .space-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e6e6e6;
    
    .space-info {
      display: flex;
      align-items: center;
      
      .space-avatar {
        width: 64px;
        height: 64px;
        border-radius: 4px;
        margin-right: 16px;
        background-color: #f0f2f5;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28px;
        color: #1890ff;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 4px;
        }
        
        .default-avatar {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #e6f7ff;
          color: #1890ff;
          font-weight: bold;
        }
      }
      
      .space-title {
        h2 {
          margin: 0 0 8px 0;
          font-size: 20px;
        }
        
        .space-meta {
          color: #8c8c8c;
          font-size: 14px;
          display: flex;
          align-items: center;
        }
      }
    }
  }
  
  .space-content {
    .content-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .el-input {
        max-width: 300px;
      }
    }
    
    .member-list {
      margin-bottom: 24px;
    }
  }
  
  .empty-tip {
    margin: 48px 0;
  }
}

.member-dialog-content {
  max-height: 500px;
  overflow-y: auto;
}

.selected-members-container {
  margin-top: 20px;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.selected-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.selected-members-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.selected-member-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 10px;
  background-color: #f0f2f5;
  border-radius: 4px;
  font-size: 14px;
  min-width: 120px;
}

.selected-member-info {
  display: flex;
  align-items: center;
}

.selected-member-avatar {
  margin-right: 8px;
}

.selected-member-name {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-selected {
  cursor: pointer;
  color: #909399;
  margin-left: 5px;
}

.remove-selected:hover {
  color: #f56c6c;
}
</style>
