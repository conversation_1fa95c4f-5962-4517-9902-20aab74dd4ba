<template>
  <div class="chatbot-container">
    <chatbot-provider :ready-to-init="pageInitialized">
      <embedded-chatbot />
    </chatbot-provider>
  </div>
</template>

<script lang="ts">
import { onMounted, defineComponent, ref } from 'vue';
import EmbeddedChatbot from './components/EmbeddedChatbot.vue';
import ChatbotProvider from './components/ChatbotProvider.vue';
import { getTokenFromPath, getChatbotAccessToken, setChatbotAccessToken } from '@/utils/chatbot';
import { fetchAccessToken } from '@/api/chatbot';
import { ElMessage } from 'element-plus';

export default defineComponent({
  name: 'ChatbotIndex',
  components: {
    ChatbotProvider,
    EmbeddedChatbot
  },
  setup() {
    // 初始化状态
    const pageInitialized = ref(false);

    // 检查或设置访问令牌
    const checkOrSetAccessToken = async () => {
      try {
        const token = getTokenFromPath();
        // 检查是否已有访问令牌
        let accessToken = getChatbotAccessToken(token);

        // 只有当缓存中没有token时才调用API获取
        if (!accessToken) {
          console.log('缓存中没有token，正在获取新token...');
          try {
            const res = await fetchAccessToken(token);
            // 根据API返回结构进行处理
            // 使用类型断言来避免类型错误
            accessToken = (res as any).data.access_token;
            if (accessToken) {
              setChatbotAccessToken(token, accessToken);
              console.log('新token已保存到缓存');
            } else {
              console.error('获取的token无效');
            }
          } catch (err) {
            console.error('获取访问令牌失败:', err);
          }
        } else {
          console.log('使用缓存中的token');
        }

        console.log('访问令牌:', accessToken);
      } catch (error: any) {
        ElMessage.error(error.message || '初始化聊天机器人失败');
        console.error('初始化聊天机器人失败:', error);
      }
    };

    onMounted(async () => {
      console.log('页面 onMounted 开始执行');
      await checkOrSetAccessToken();
      // 设置页面初始化完成标志，通知 ChatbotProvider 可以开始初始化
      pageInitialized.value = true;
      console.log('页面 onMounted 执行完成，已设置 pageInitialized = true');
    });

    return {
      pageInitialized
    };
  }
});
</script>

<style scoped>
.chatbot-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f9fafb;
}
</style>
