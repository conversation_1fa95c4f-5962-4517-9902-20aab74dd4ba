<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`创建${AgentMode[props.mode as keyof typeof AgentMode]}`"
    width="650px"
    :before-close="handleClose"
    align-center
  >
    <div class="create-workflow-form">
      <div class="form-content">
        <!-- 左侧图标上传区域 -->
        <div class="left-section">
          <el-upload
            class="icon-upload"
            :show-file-list="false"
            :before-upload="beforeUpload"
            :http-request="customUpload"
            accept=".jpg,.jpeg,.png,.bmp,.gif"
          >
            <div v-if="!form.icon && !form.useDefaultIcon" class="upload-placeholder">
              <el-icon class="plus-icon"><Plus /></el-icon>
              <div class="upload-text">上传图标</div>
            </div>
            <div v-else-if="form.icon" class="preview-uploaded">
              <el-image :src="iconPreviewUrl" fit="cover" class="uploaded-icon"></el-image>
              <div class="upload-hover-mask">
                <el-icon><Edit /></el-icon>
              </div>
            </div>
            <div v-else class="preview-uploaded">
              <img src="@/assets/images/ic_qa_robot.png" class="uploaded-icon" alt="默认图标" />
              <div class="upload-hover-mask">
                <el-icon><Edit /></el-icon>
              </div>
            </div>
          </el-upload>
          <div class="upload-format-text">支持 JPG、PNG、JPEG、BMP、GIF 格式<br />建议尺寸 128×128px</div>
          <el-progress
            v-if="uploadProgress > 0 && uploadProgress < 100"
            :percentage="uploadProgress"
            type="circle"
            :width="60"
            class="upload-progress"
          />
        </div>

        <!-- 右侧表单区域 -->
        <div class="right-section">
          <el-form ref="formRef" :model="form" :rules="rules" label-position="top" @submit.prevent>
            <el-form-item :label="`${AgentMode[props.mode as keyof typeof AgentMode]}名称`" prop="name" required>
              <el-input
                v-model="form.name"
                :placeholder="`请输入${AgentMode[props.mode as keyof typeof AgentMode]}名称`"
                :maxlength="30"
                show-word-limit
                @keydown.enter.stop
              />
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                :rows="4"
                :placeholder="`请输入${AgentMode[props.mode as keyof typeof AgentMode]}描述`"
                @keydown.enter.stop
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">下一步</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { Plus, Edit } from "@element-plus/icons-vue";
import type { FormInstance, FormRules, UploadRawFile } from "element-plus";
import { post } from "@/utils/request";
import { uploadFile } from "@/utils/request";
import type { CreateWorkflowRequest, FileUploadResponse } from "../types";
import { AgentMode } from "@/views/agents/types"; // 导入 AgentMode 类型

const props = defineProps<{
  visible: boolean;
  mode: string;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "created", payload: { id: string; name: string }): void;
}>();

// 表单引用
const formRef = ref<FormInstance>();

// 文件上传相关
const uploadProgress = ref(0);
const iconPreviewUrl = ref("");

// 表单数据
const form = ref<{
  name: string;
  description: string;
  icon: string;
  icon_background: string;
  icon_type: string;
  useDefaultIcon: boolean;
}>({
  name: "",
  description: "",
  icon: "",
  icon_background: "#3370ff", // 默认背景色
  icon_type: "image", // 当上传图片时使用的类型
  useDefaultIcon: true, // 默认使用默认图标
});

// 表单验证规则
const rules = ref<FormRules>({
  name: [{ required: true, message: "请输入工作流名称", trigger: "blur" }],
});

// 提交状态
const submitting = ref(false);
const dialogVisible = ref(false);

// 监听visible属性变化
watch(
  () => props.visible,
  newVal => {
    dialogVisible.value = newVal;
  }
);

// 监听对话框可见性变化
watch(dialogVisible, newVal => {
  if (!newVal) {
    emit("update:visible", false);
  }
});

// 上传前验证
const beforeUpload = (file: UploadRawFile) => {
  // 验证文件类型
  const allowedTypes = [".jpg", ".png", ".jpeg", ".bmp", ".gif"];
  const fileExt = file.name.substring(file.name.lastIndexOf(".")).toLowerCase();
  if (!allowedTypes.includes(fileExt)) {
    ElMessage.error("只支持PNG和SVG格式的图片");
    return false;
  }

  // 验证文件大小（限制为2MB）
  if (file.size > 2 * 1024 * 1024) {
    ElMessage.error("文件大小不能超过2MB");
    return false;
  }

  return true;
};

// 自定义上传方法
const customUpload = async (options: any) => {
  const { file } = options;
  uploadProgress.value = 0;

  try {
    // 使用封装的uploadFile方法
    const response = await uploadFile<FileUploadResponse>("/files/upload", file, progress => {
      uploadProgress.value = progress;
    });

    // 文件上传成功
    form.value.icon = response.id;
    form.value.useDefaultIcon = false; // 上传了自定义图标，不再使用默认图标
    iconPreviewUrl.value = URL.createObjectURL(file);
  } catch (error) {
    console.error("图标上传失败", error);
  } finally {
    setTimeout(() => {
      uploadProgress.value = 0;
    }, 500);
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  form.value = {
    name: "",
    description: "",
    icon: "",
    icon_background: "#3370ff",
    icon_type: "image", // 当上传图片时使用的类型
    useDefaultIcon: true, // 重置为默认使用默认图标
  };
  iconPreviewUrl.value = "";
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (valid) {
      submitting.value = true;
      try {
        // 构建请求数据
        const requestData: CreateWorkflowRequest = {
          name: form.value.name,
          description: form.value.description || "",
          mode: props.mode,
          icon_type: form.value.icon ? "image" : (form.value.useDefaultIcon ? "image" : "emoji"),
        };

        // 如果上传了自定义图标
        if (form.value.icon) {
          requestData.icon = form.value.icon;
          requestData.icon_background = form.value.icon_background;
        }
        // 如果使用默认图标
        else if (form.value.useDefaultIcon) {
          // 使用默认图标路径中的图片
          requestData.icon = "default_robot_icon"; // 这里使用一个标识符，后端会识别并使用默认图标
          requestData.icon_background = "#FFFFFF";
          requestData.icon_type = "image";
        }
        // 如果既没有上传图标也不使用默认图标，则使用emoji
        else {
          requestData.icon = "🤖"; // 机器人表情符号
          requestData.icon_background = "#FFEAD5";
          requestData.icon_type = "emoji";
        }

        // 发送创建请求
        const response = await post<{ id: string }>("/apps", requestData as any);
        ElMessage.success(`${AgentMode[props.mode as keyof typeof AgentMode]}创建成功`);

        // 关闭对话框并通知父组件
        dialogVisible.value = false;
        emit("created", { id: response.id, name: form.value.name });
        resetForm();
      } catch (error) {
        console.error("创建失败", error);
      } finally {
        submitting.value = false;
      }
    }
  });
};
</script>

<style lang="scss" scoped>
.create-workflow-form {
  padding: 0 20px;
}

.form-content {
  display: flex;
  gap: 30px;
}

.left-section {
  width: 180px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;

  .icon-upload {
    width: 160px;
    height: 160px;
    margin-bottom: 10px;

    :deep(.el-upload) {
      width: 100%;
      height: 100%;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      position: relative;
      border: 1px dashed #dcdfe6;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        border-color: #409eff;
        .upload-hover-mask {
          opacity: 1;
        }
      }
    }
  }

  .upload-format-text {
    font-size: 12px;
    color: #909399;
    text-align: center;
    line-height: 1.4;
    margin-top: 8px;
    max-width: 160px;
  }

  .upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: #909399;
    text-align: center;

    .plus-icon {
      font-size: 24px;
      margin-bottom: 4px;
    }

    .upload-text {
      font-size: 14px;
    }
  }

  .preview-uploaded {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 8px;
    overflow: hidden;

    .uploaded-icon {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .upload-hover-mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s;
      color: #fff;
      font-size: 24px;
      border-radius: 8px;
    }
  }

  .upload-progress {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }
}

.right-section {
  flex: 1;

  :deep(.el-form-item__label) {
    padding-bottom: 8px;
  }

  :deep(.el-form-item__content) {
    line-height: 1;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
}
</style>
