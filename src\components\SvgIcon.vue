<template>
  <component
    :is="svgComponent"
    class="svg-icon"
    :class="customClass"
    :style="{
      width: size + 'px',
      height: size + 'px',
      color: color || 'inherit',
    }"
  />
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = withDefaults(
  defineProps<{
    // SVG组件
    icon: any;
    // 图标大小
    size?: number | string;
    // 图标颜色，不传则继承父元素颜色
    color?: string;
    // 自定义类名
    customClass?: string;
  }>(),
  {
    size: 16,
    color: "",
    customClass: "",
  }
);

// 计算SVG组件
const svgComponent = computed(() => {
  return props.icon;
});
</script>

<style scoped>
.svg-icon {
  display: inline-block;
  vertical-align: middle;
  fill: currentColor; /* 关键：使用currentColor继承父元素颜色 */
}

/* 确保SVG内部的path等元素也继承颜色 */
.svg-icon :deep(path),
.svg-icon :deep(circle),
.svg-icon :deep(rect),
.svg-icon :deep(polygon) {
  fill: currentColor !important;
  stroke: currentColor !important;
}
</style>
