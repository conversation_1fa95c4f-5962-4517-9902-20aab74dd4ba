<template>
  <div class="datasources-container">
    <div class="page-header">
      <h2 class="page-title">数据源管理</h2>
      <div class="page-actions">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索数据源"
          prefix-icon="Search"
          clearable
          class="search-input"
        />
        <el-button type="primary">
          <el-icon><Plus /></el-icon>
          添加数据源
        </el-button>
      </div>
    </div>

    <el-card>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="全部数据源" name="all">
          <el-table :data="filteredDatasources" style="width: 100%">
            <el-table-column prop="name" label="数据源名称" min-width="180">
              <template #default="{ row }">
                <div class="datasource-name">
                  <el-icon :class="getIconClass(row.type)">
                    <component :is="getIconByType(row.type)" />
                  </el-icon>
                  <span>{{ row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getTagType(row.type)">{{ getTypeName(row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="connection" label="连接信息" min-width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === 'connected' ? 'success' : 'danger'">
                  {{ row.status === 'connected' ? '已连接' : '未连接' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="创建时间" width="180" />
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button size="small" type="primary" plain @click="handleEdit(row)">编辑</el-button>
                <el-button size="small" type="success" plain @click="handleConnect(row)" v-if="row.status !== 'connected'">连接</el-button>
                <el-button size="small" type="warning" plain @click="handleDisconnect(row)" v-else>断开</el-button>
                <el-button size="small" type="danger" plain @click="handleDelete(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="数据库" name="database">
          <el-table :data="databaseDatasources" style="width: 100%">
            <!-- 与全部数据源相同的列 -->
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="文件存储" name="file">
          <el-table :data="fileDatasources" style="width: 100%">
            <!-- 与全部数据源相同的列 -->
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="API" name="api">
          <el-table :data="apiDatasources" style="width: 100%">
            <!-- 与全部数据源相同的列 -->
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { 
  Plus, 
  Search, 
  DataAnalysis, 
  FolderOpened, 
  Link, 
  Connection 
} from '@element-plus/icons-vue';

// 数据源类型
const datasourceTypes = [
  { label: '数据库', value: 'database' },
  { label: '文件存储', value: 'file' },
  { label: 'API', value: 'api' },
  { label: '其他', value: 'other' }
];

// 数据源数据
const datasources = ref([
  {
    id: 1,
    name: 'MySQL 主数据库',
    type: 'database',
    connection: 'mysql://user:****@localhost:3306/main',
    status: 'connected',
    createdAt: '2023-10-15 14:30:22'
  },
  {
    id: 2,
    name: 'PostgreSQL 分析库',
    type: 'database',
    connection: 'postgresql://user:****@localhost:5432/analytics',
    status: 'connected',
    createdAt: '2023-11-05 09:15:43'
  },
  {
    id: 3,
    name: 'S3 文件存储',
    type: 'file',
    connection: 's3://my-bucket/data',
    status: 'connected',
    createdAt: '2023-09-20 11:22:35'
  },
  {
    id: 4,
    name: '本地文件系统',
    type: 'file',
    connection: '/data/local/files',
    status: 'connected',
    createdAt: '2023-08-12 16:45:10'
  },
  {
    id: 5,
    name: '天气 API',
    type: 'api',
    connection: 'https://api.weather.com/v1',
    status: 'disconnected',
    createdAt: '2023-12-01 10:30:00'
  },
  {
    id: 6,
    name: '用户服务 API',
    type: 'api',
    connection: 'https://api.internal.com/users',
    status: 'connected',
    createdAt: '2024-01-15 08:20:15'
  },
  {
    id: 7,
    name: 'Redis 缓存',
    type: 'database',
    connection: 'redis://localhost:6379/0',
    status: 'disconnected',
    createdAt: '2024-02-10 13:40:22'
  },
  {
    id: 8,
    name: 'ElasticSearch',
    type: 'database',
    connection: 'http://localhost:9200',
    status: 'connected',
    createdAt: '2024-03-05 15:10:30'
  }
]);

// 搜索和筛选状态
const searchKeyword = ref('');
const activeTab = ref('all');

// 获取类型名称
const getTypeName = (type: string) => {
  const found = datasourceTypes.find(t => t.value === type);
  return found ? found.label : type;
};

// 获取标签类型
const getTagType = (type: string) => {
  switch (type) {
    case 'database': return 'primary';
    case 'file': return 'success';
    case 'api': return 'warning';
    default: return 'info';
  }
};

// 获取图标
const getIconByType = (type: string) => {
  switch (type) {
    case 'database': return DataAnalysis;
    case 'file': return FolderOpened;
    case 'api': return Link;
    default: return Connection;
  }
};

// 获取图标类名
const getIconClass = (type: string) => {
  return `icon-${type}`;
};

// 筛选数据源
const filteredDatasources = computed(() => {
  return datasources.value.filter(ds => {
    if (searchKeyword.value && !ds.name.toLowerCase().includes(searchKeyword.value.toLowerCase())) {
      return false;
    }
    return true;
  });
});

// 按类型筛选的数据源
const databaseDatasources = computed(() => {
  return filteredDatasources.value.filter(ds => ds.type === 'database');
});

const fileDatasources = computed(() => {
  return filteredDatasources.value.filter(ds => ds.type === 'file');
});

const apiDatasources = computed(() => {
  return filteredDatasources.value.filter(ds => ds.type === 'api');
});

// 处理操作
const handleEdit = (row: any) => {
  console.log('编辑数据源', row);
};

const handleConnect = (row: any) => {
  console.log('连接数据源', row);
  row.status = 'connected';
};

const handleDisconnect = (row: any) => {
  console.log('断开数据源', row);
  row.status = 'disconnected';
};

const handleDelete = (row: any) => {
  console.log('删除数据源', row);
};
</script>

<style scoped lang="scss">
.datasources-container {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .page-title {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
    }
    
    .page-actions {
      display: flex;
      gap: 12px;
      
      .search-input {
        width: 240px;
      }
    }
  }
  
  .datasource-name {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .el-icon {
      font-size: 18px;
      
      &.icon-database {
        color: #409eff;
      }
      
      &.icon-file {
        color: #67c23a;
      }
      
      &.icon-api {
        color: #e6a23c;
      }
      
      &.icon-other {
        color: #909399;
      }
    }
  }
}
</style> 