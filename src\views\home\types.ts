import { ListPageResponse, ListPageRequest } from "@/types/common";

export interface AppListPageRequest extends ListPageRequest {
  name?: string | null;
  is_created_by_me?: boolean | null;
  mode?: string | null;
}

export interface AppsRecord {
  created_at: number;
  created_by: string;
  description: string | null;
  icon: string | null;
  icon_background: string;
  icon_type: string;
  icon_url: string | null;
  id: string;
  max_active_requests: string | null;
  mode: string;
  name: string;
  updated_at: number;
  updated_by: string;
  use_icon_as_answer_icon: boolean;
  workflow: unknown;
  model_config: unknown;
  tags: unknown;
}

export type AppsListPageResponse = ListPageResponse<AppsRecord[]>;

export enum AgentMode {
  "agent-chat" = "AGENT",
  "chat" = "聊天助手",
}
