export interface IMessage {
  answer?: string;
  conversation_id: string;
  created_at?: number;
  files?: string[];
  id?: string;
  inputs?: Record<string, unknown>;
  parent_message_id?: null | string;
  query: string;
  response_mode?: string;
  status?: string;
  [key: string]: unknown;
}

export interface IConversation {
  id: string;
  inputs: Record<string, unknown>;
  introduction: string;
  name: string;
  status: string;
  created_at: number;
  updated_at: number;
  pinned?: boolean;
}

export interface IConversationResponse {
  data: IConversation[];
  has_more: boolean;
  limit: number;
}

export interface IMessageResponse {
  data: IMessage[];
  has_more: boolean;
  limit: number;
}

// types.ts 中更新 ChatParams 接口
export interface ChatParams {
  query: string;
  response_mode: string;
  conversation_id?: string;
  parent_message_id?: string | null;
  files?: any[];
  inputs?: Record<string, any>;
  model_config?: ModelConfig; // 添加 model_config 字段
}

export interface ModelConfig {
  pre_prompt: string;
  prompt_type: string;
  chat_prompt_config: Record<string, any>;
  completion_prompt_config: Record<string, any>;
  user_input_form?: any[];
  dataset_query_variable?: string;
  opening_statement?: string;
  more_like_this?: {
    enabled: boolean;
  };
  suggested_questions?: any[];
  suggested_questions_after_answer?: {
    enabled: boolean;
  };
  text_to_speech?: {
    enabled: boolean;
  };
  speech_to_text?: {
    enabled: boolean;
  };
  retriever_resource?: {
    enabled: boolean;
  };
  sensitive_word_avoidance?: {
    enabled: boolean;
    type: string;
    configs: any[];
  };
  agent_mode?: {
    enabled: boolean;
    max_iteration: number;
    strategy: string;
    tools: any[];
  };
  dataset_configs?: {
    retrieval_model: string;
    top_k: number;
    reranking_enable: boolean;
    datasets: {
      datasets: any[];
    };
  };
  file_upload?: any;
  annotation_reply?: {
    enabled: boolean;
  };
  supportAnnotation?: boolean;
  appId?: string;
  supportCitationHitInfo?: boolean;
  model?: {
    provider: string;
    name: string;
    mode: string;
    completion_params: Record<string, any>;
  };
}

import { ListPageResponse, ListPageRequest } from "@/types/common";

export interface AppListPageRequest extends ListPageRequest {
  name?: string | null;
  is_created_by_me?: boolean | null;
  mode?: string | null;
}

export interface AppsRecord {
  created_at: number;
  created_by: string;
  description: string | null;
  icon: string | null;
  icon_background: string;
  icon_type: string;
  icon_url: string | null;
  id: string;
  max_active_requests: string | null;
  mode: string;
  name: string;
  updated_at: number;
  updated_by: string;
  use_icon_as_answer_icon: boolean;
  workflow: unknown;
  model_config: unknown;
  tags: unknown;
}

export type AppsListPageResponse = ListPageResponse<AppsRecord[]>;

export enum AgentMode {
  "agent-chat" = "AGENT",
  "chat" = "聊天助手",
  "advanced-chat" = "对话流",
  "workflow" = "工作流",
}

export interface InstalledApp {
  id: string;
  app_id: string;
  created_at: number;
  updated_at: number;
  created_by: string;
  updated_by: string;
  name: string;
  description: string | null;
  icon_url: string | null;
  app: {
    name: string;
    icon_url: string | null;
  };
  [key: string]: unknown;
}

export interface InstalledAppsResponse {
  installed_apps: InstalledApp[];
}

export interface ParametersRecord {
  opening_statement: string;
  suggested_questions_after_answer: {
    enabled: boolean;
  };
  suggested_questions?: string[];
  [key: string]: unknown;
}

export interface AgentAppRecord {
  model_config: {
    model: {
      mode: string;
      name: string;
      provider: string;
    };
    file_upload: {
      allowed_file_extensions: string[];
      allowed_file_types: string[];
      allowed_file_upload_methods: string[];
      enabled: boolean;
      image: {
        detail: string;
        enabled: boolean;
        number_limits: number;
        transfer_methods: string[];
      };
      number_limits: number;
    };
  };
  [key: string]: unknown;
}

// 上传文件的类型定义
export interface UploadedFile {
  transfer_method: string;
  type: string;
  upload_file_id: string;
  url: string;
  fileUrl?: string;
  name?: string;
  size?: number;
  filename?: string;
}
