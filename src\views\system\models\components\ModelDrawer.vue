<template>
  <el-drawer v-model="visible" title="添加 Xorbits Inference" direction="rtl" size="600px" :destroy-on-close="true">
    <div class="model-drawer" v-loading="loading">
      <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
        <!-- 模型类型表单项 -->
        <el-form-item
          label="模型类型"
          prop="model_type"
          :rules="[{ required: true, message: '请选择模型类型', trigger: ['blur', 'change'] }]"
        >
          <el-radio-group v-model="formData.model_type" size="large" class="radio-group">
            <el-radio v-for="type in currentProvider?.supported_model_types" :key="type" :value="type" border>
              {{ type }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 模型名称表单项 -->
        <el-form-item
          :label="modelSchema?.label?.zh_Hans"
          prop="model_name"
          :rules="[{ required: true, message: '请输入模型名称', trigger: ['blur', 'change'] }]"
        >
          <el-input v-model="formData.model_name" :placeholder="modelSchema?.placeholder?.zh_Hans" size="large" />
        </el-form-item>

        <!-- 动态生成的表单项 -->
        <template v-if="credentialSchemas?.length">
          <el-form-item
            v-for="schema in credentialSchemas"
            :key="schema.variable"
            :label="schema.label.zh_Hans"
            :prop="schema.variable"
            v-show="shouldShowField(schema)"
          >
            <!-- 文本输入框 -->
            <el-input
              v-if="schema.type.includes('input') || schema.type === 'secret'"
              v-model="formData[schema.variable]"
              :placeholder="schema.placeholder?.zh_Hans"
              :type="schema.type === 'secret' ? 'password' : 'text'"
              size="large"
            />

            <!-- 下拉选择框 -->
            <el-select
              v-else-if="schema.type === 'select'"
              v-model="formData[schema.variable]"
              :placeholder="schema.placeholder?.zh_Hans"
              size="large"
              class="w-full"
              clearable
            >
              <el-option
                v-for="option in schema.options"
                :key="option.value"
                :label="option.label.zh_Hans"
                :value="option.value"
              />
            </el-select>

            <!-- 单选框 -->
            <el-radio-group
              v-else-if="schema.type === 'radio'"
              v-model="formData[schema.variable]"
              size="large"
              class="radio-group"
            >
              <el-radio v-for="option in schema.options" :key="option.value" :value="option.value" border>
                {{ option.label.zh_Hans }}
              </el-radio>
            </el-radio-group>

            <!-- 多选框 -->
            <el-checkbox-group
              v-else-if="schema.type === 'checkbox'"
              v-model="formData[schema.variable]"
              size="large"
              class="checkbox-group"
            >
              <el-checkbox v-for="option in schema.options" :key="option.value" :label="option.value" border>
                {{ option.label.zh_Hans }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </template>
      </el-form>

      <div class="button-group">
        <el-button plain size="large" block @click="handleCancel">取消</el-button>
        <el-button type="primary" size="large" block @click="handleSave">保存</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { PropType } from "vue";
import type { ProviderRecord } from "../types";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import { post } from "@/utils/request";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  currentProvider: {
    type: Object as PropType<ProviderRecord | null>,
    default: null,
  },
});

const emit = defineEmits(["update:modelValue", "refresh"]);
const formRef = ref<FormInstance>();
const loading = ref(false);

const visible = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emit("update:modelValue", val),
});

const credentialSchemas = computed(() => {
  return props.currentProvider?.model_credential_schema?.credential_form_schemas || [];
});

const modelSchema = computed(() => {
  return props.currentProvider?.model_credential_schema?.model;
});

const formData = ref<Record<string, any>>({
  model_type: props.currentProvider?.supported_model_types?.[0] || "",
  model_name: "",
});
const rules = ref<FormRules>({});

// 判断是否显示表单项
const shouldShowField = (schema: any) => {
  if (!schema.show_on?.length) return true;

  return schema.show_on[0].value === formData.value.model_type;
};

// 初始化表单数据和规则
const initFormData = (schemas: any[]) => {
  const newFormData: Record<string, any> = {
    model_type: props.currentProvider?.supported_model_types?.[0] || "",
    model_name: "",
  };
  const newRules: FormRules = {};

  schemas.forEach(schema => {
    // 根据类型设置默认值
    if (schema.type === "checkbox") {
      newFormData[schema.variable] = schema.default || [];
    } else {
      newFormData[schema.variable] = schema.default || "";
    }

    // 生成校验规则
    newRules[schema.variable] = [
      {
        required: schema.required ?? false,
        message: `请${schema.type === "checkbox" ? "选择" : "输入"}${schema.label.zh_Hans}`,
        trigger: ["blur", "change"],
      },
    ];
  });

  formData.value = newFormData;
  rules.value = newRules;
};

// 监听表单schema变化
watch(
  [visible, credentialSchemas],
  ([newVisible, newSchemas]) => {
    if (newSchemas?.length && newVisible) {
      // 初始化表单
      initFormData(newSchemas);
    }
  },
  { immediate: true }
);

const handleSave = async () => {
  if (!formRef.value) return;
  try {
    loading.value = true;
    await formRef.value.validate(async valid => {
      if (valid) {
        const { model_type, model_name, ...credentials } = formData.value;
        await post(`/workspaces/current/model-providers/${props.currentProvider?.provider}/models`, {
          model_type: model_type,
          model: model_name,
          credentials,
          load_balancing: {
            enabled: false,
            configs: [],
          },
        });
        ElMessage.success("保存成功");
        emit("refresh");
        visible.value = false;
      }
    });
  } catch (error) {
    console.error("保存失败:", error);
  } finally {
    loading.value = false;
  }
};

const handleCancel = () => {
  formRef.value?.resetFields();
  visible.value = false;
};
</script>

<style scoped lang="scss">
.model-drawer {
  padding: 0 20px;
  position: relative;
  min-height: 200px;

  :deep(.w-full) {
    width: 100%;
  }

  .radio-group,
  .checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;

    :deep(.el-radio),
    :deep(.el-checkbox) {
      width: 100%;
      margin: 0;
      padding: 12px;
      border-radius: 4px;
    }
  }

  .button-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 40px;

    :deep(.el-button) {
      width: 100%;
      margin: 0;
      padding-left: 0;
      padding-right: 0;
    }
  }
}
</style>
