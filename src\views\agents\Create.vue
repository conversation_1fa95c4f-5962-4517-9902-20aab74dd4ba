<template>
  <div class="create-agent-container">
    <div class="header">
      <div class="back" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
      </div>
      <div class="title">{{ currentAgentId ? '编辑智能体' : '创建智能体' }}</div>
    </div>

    <div class="steps-container">
      <el-steps :active="currentStep" finish-status="success">
        <el-step title="选择类型" />
        <el-step title="基础配置（完成创建）" />
        <el-step title="高级设置" />
        <!-- <el-step title="完成创建" /> -->
      </el-steps>
    </div>
    <div v-if="currentStep === 1" class="step-content">
      <div class="type-selection">
        <div
          class="type-card"
          :class="{ active: selectedType === 'chat' }"
          @click="selectChatAssistant"
        >
          <div class="icon-container">
            <img src="@/assets/images/ltzs.png" alt="聊天助手" class="type-icon" />
          </div>
          <div class="type-title">聊天助手</div>
          <div class="type-desc">创建一个智能对话助手，可以进行自然语言交互，回答问题并提供帮助，支持知识库输入，可以基于已有内容进行对话。</div>
        </div>

        <div
          class="type-card"
          :class="{ active: selectedType === 'agent' }"
          @click="selectType('agent')"
        >
          <div class="icon-container">
            <img src="@/assets/images/znt.png" alt="智能体" class="type-icon" />
          </div>
          <div class="type-title">智能体</div>
          <div class="type-desc">创建一个自动化智能体，可以执行特定任务，支持API集成和自定义工作流，适合需要自动化处理的业务场景。</div>
        </div>
      </div>

      <div class="step-actions">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="nextStep" :disabled="!selectedType">下一步</el-button>
      </div>
    </div>

    <div v-if="currentStep === 2" class="step-content">
      <div class="basic-config">
        <div class="icon-upload-container">
          <el-upload
            class="icon-upload"
            :show-file-list="false"
            :before-upload="beforeUpload"
            :http-request="customUpload"
            accept="image/*"
          >
            <div v-if="!basicForm.icon" class="upload-placeholder">
              <el-icon class="upload-icon"><Setting /></el-icon>
              <div class="upload-text">请上传图标</div>
            </div>
            <div v-else class="preview-uploaded">
              <el-image :src="basicForm.iconUrl" fit="cover" class="uploaded-icon"></el-image>
              <div class="upload-hover-mask">
                <el-icon><Edit /></el-icon>
              </div>
            </div>
          </el-upload>
          <el-progress
            v-if="uploadProgress > 0 && uploadProgress < 100"
            :percentage="uploadProgress"
            type="circle"
            :width="60"
            class="upload-progress"
          />
        </div>

        <div class="form-container">
          <div class="form-item">
            <div class="form-label">名称</div>
            <el-input
              v-model="basicForm.name"
              placeholder="请输入名称"
              maxlength="30"
              show-word-limit
            />
          </div>

          <div class="form-item">
            <div class="form-label">描述</div>
            <el-input
              v-model="basicForm.description"
              type="textarea"
              placeholder="请输入描述"
              maxlength="200"
              show-word-limit
              rows="5"
            />
          </div>
        </div>
      </div>

      <div class="step-actions">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>
    </div>

    <div v-if="currentStep === 3" class="step-content advanced-step">
      <div class="publish-button-container" style="text-align: right; margin-bottom: 12px;">
        <el-dropdown split-button type="primary" size="large" @click="goToNextStep" @command="handleCommand">
          发布更新
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="embed">嵌入网站</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <!-- 嵌入网站弹窗 -->
      <el-dialog
        v-model="embedDialogVisible"
        title="嵌入到网站中"
        width="800px"
        :close-on-click-modal="false"
      >
        <div class="embed-dialog-content">
          <p>选择一种方式将聊天应用嵌入到你的网站中</p>

          <div class="embed-options">
            <div class="embed-option" :class="{ active: selectedEmbedOption === 'fullpage' }" @click="selectEmbedOption('fullpage')">
              <div class="option-image">
                <svg width="200" height="150" viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg">
                  <rect x="10" y="10" width="180" height="130" rx="5" fill="#f0f2f5" stroke="#d9d9d9" stroke-width="2"/>
                  <rect x="20" y="20" width="160" height="110" rx="3" fill="white" stroke="#d9d9d9" stroke-width="1"/>
                  <rect x="30" y="30" width="140" height="90" rx="2" fill="#e6f7ff" stroke="#1890ff" stroke-width="1"/>
                  <text x="100" y="75" font-family="Arial" font-size="12" fill="#1890ff" text-anchor="middle">全页面嵌入</text>
                </svg>
              </div>
              <div class="option-title">全页面嵌入</div>
            </div>

            <div class="embed-option" :class="{ active: selectedEmbedOption === 'sidebar' }" @click="selectEmbedOption('sidebar')">
              <div class="option-image">
                <svg width="200" height="150" viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg">
                  <rect x="10" y="10" width="180" height="130" rx="5" fill="#f0f2f5" stroke="#d9d9d9" stroke-width="2"/>
                  <rect x="120" y="20" width="60" height="110" rx="3" fill="white" stroke="#d9d9d9" stroke-width="1"/>
                  <rect x="130" y="30" width="40" height="90" rx="2" fill="#e6f7ff" stroke="#1890ff" stroke-width="1"/>
                  <text x="150" y="75" font-family="Arial" font-size="10" fill="#1890ff" text-anchor="middle">侧边栏</text>
                </svg>
              </div>
              <div class="option-title">侧边栏嵌入</div>
            </div>

            <div class="embed-option" :class="{ active: selectedEmbedOption === 'popup' }" @click="selectEmbedOption('popup')">
              <div class="option-image">
                <svg width="200" height="150" viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg">
                  <rect x="10" y="10" width="180" height="130" rx="5" fill="#f0f2f5" stroke="#d9d9d9" stroke-width="2"/>
                  <rect x="60" y="40" width="80" height="70" rx="3" fill="white" stroke="#d9d9d9" stroke-width="1"/>
                  <rect x="70" y="50" width="60" height="50" rx="2" fill="#e6f7ff" stroke="#1890ff" stroke-width="1"/>
                  <text x="100" y="75" font-family="Arial" font-size="10" fill="#1890ff" text-anchor="middle">弹窗</text>
                </svg>
              </div>
              <div class="option-title">弹窗嵌入</div>
            </div>
          </div>

          <div class="embed-code-container">
            <p>将以下 iframe 嵌入到你的网站中的目标位置</p>
            <div class="code-box">
              <pre><code>{{ embedCode }}</code></pre>
              <el-button class="copy-btn" type="primary" @click="copyEmbedCode">
                <el-icon><Document /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </el-dialog>
      <div class="advanced-config">
        <div class="advanced-left">
          <!-- 角色指令 -->
          <div class="config-section">
            <div class="section-title">角色指令</div>
            <el-input
              v-model="advancedForm.rolePrompt"
              type="textarea"
              placeholder="请输入角色指令"
              :rows="4"
            />
            <!-- <div class="ai-assist">
              <el-button size="small" type="primary" plain @click="generateRolePrompt">
                <span>AI生成</span>
              </el-button>
            </div> -->
          </div>

          <!-- 对话体验 -->
          <div class="config-section">
            <div class="section-title">对话体验</div>
            <div class="toggle-item">
              <span>开场白</span>
              <el-switch v-model="advancedForm.opening.enabled" />
            </div>
            <div v-if="advancedForm.opening.enabled" class="opening-statement-container">
              <el-input
                v-model="advancedForm.opening.opening_statement"
                type="textarea"
                placeholder="请输入开场白内容"
                :rows="3"
              />
              <div class="save-button-container">
                <el-button type="primary" @click="handleSaveOpening" size="small">保存</el-button>
              </div>
            </div>

            <div class="toggle-item with-description" style="margin-top: 15px;">
              <div class="toggle-header">
                <span>下一步问题建议</span>
                <el-switch
                  v-model="advancedForm.suggestedQuestions.enabled"
                  @change="handleSuggestedQuestionsChange"
                />
              </div>
              <div class="toggle-description">智能引导用户根据当前对话提供一些建议选项</div>
            </div>


            <!-- <div class="toggle-item with-description" style="margin-top: 15px;">
              <div class="toggle-header">
                <span>下一步问题建议</span>
                <el-switch v-model="advancedForm.suggested.enabled" />
              </div>
              <div class="toggle-description">智能引导用户根据当前对话提供一些建议选项</div>
            </div>

            <div class="toggle-item with-description" style="margin-top: 15px;">
              <div class="toggle-header">
                <span>文字转语音</span>
                <el-switch v-model="advancedForm.text2speech.enabled" />
              </div>
              <div class="toggle-description">文本可以转换成语音</div>
            </div>

            <div class="toggle-item with-description" style="margin-top: 15px;">
              <div class="toggle-header">
                <span>语音转文字</span>
                <el-switch v-model="advancedForm.speech2text.enabled" />
              </div>
              <div class="toggle-description">您可以使用语音输入</div>
            </div> -->

            <div class="toggle-item with-description" style="margin-top: 15px;">
              <div class="toggle-header">
                <span>引用和归属</span>
                <el-switch v-model="advancedForm.citation.enabled" />
              </div>
              <div class="toggle-description">显示源文档和知识库中内容的引用部分</div>
            </div>

            <div class="toggle-item with-description" style="margin-top: 15px;">
              <div class="toggle-header">
                <span>内容审查</span>
                <div class="action-buttons">
                  <el-button
                    v-if="advancedForm.moderation.enabled"
                    size="small"
                    type="primary"
                    text
                    @click="openModerationDialog"
                  >
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                  <el-switch v-model="advancedForm.moderation.enabled" @change="handleModerationToggle" />
                </div>
              </div>
              <div class="toggle-description">您可以调用审查API或者维护敏感词库来使模型更安全地输出</div>
            </div>

            <div class="toggle-item with-description" style="margin-top: 15px;">
              <div class="toggle-header">
                <span>标注回复</span>
                <el-switch v-model="advancedForm.annotation_reply.enabled" />
              </div>
              <div class="toggle-description">启用后，将标注用户的回复，以便在用户重复提问时快速响应</div>
              <div v-if="advancedForm.annotation_reply.enabled" class="annotation-settings">
                <div class="annotation-reply-score">
                  <div class="score-labels">
                    <span>模糊匹配</span>
                    <span>精准匹配</span>
                  </div>
                  <el-slider v-model="advancedForm.annotation_reply.score_threshold" :min="0.5" :max="1" :step="0.01" />
                  <div class="score-text">匹配分数阈值：{{ advancedForm.annotation_reply.score_threshold }}</div>
                </div>
                <div class="embedding-model">
                  <el-select v-model="advancedForm.annotation_reply.embedding_model.model_name"
                    placeholder="请选择Embedding模型"
                    class="full-width-select"
                    value-key="model"
                    filterable
                    @change="handleEmbeddingModelChange"
                  >
                    <el-option-group
                      v-for="provider in embeddingModelList"
                      :key="provider.provider"
                      :label="provider.label.zh_Hans || provider.label.en_US"
                    >
                      <el-option
                        v-for="model in provider.models"
                        :key="model.model"
                        :label="model.model"
                        :value="model.model"
                      />
                    </el-option-group>
                  </el-select>
                </div>
              </div>
            </div>
          </div>

          <!-- 变量 -->
          <div class="config-section">
            <div class="section-title">
              变量
              <el-button link type="primary" @click="showVariableDialog">+ 添加变量</el-button>
            </div>
            <div v-if="variables.length === 0" class="empty-section">文本变量</div>
            <div v-else class="variables-list">
              <div v-for="(variable, index) in variables" :key="index" class="variable-item">
                <div class="variable-name">
                  <span class="variable-type-tag" :class="'type-' + variable.type">{{ getVariableTypeLabel(variable.type) }}</span>
                  {{ variable.name }}
                </div>
                <div class="variable-actions">
                  <el-button type="primary" text @click="editVariable(index)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button type="danger" text @click="deleteVariable(index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 知识库 -->
          <div class="config-section">
            <div class="section-title">
              知识库
              <el-button link type="primary" @click="openDatasetDialog">+ 添加知识库</el-button>
            </div>
            <div v-if="selectedDatasets.length === 0" class="empty-section">文档知识库</div>
            <div v-else class="dataset-list">
              <div v-for="dataset in selectedDatasets" :key="dataset.id" class="dataset-item">
                <div class="dataset-info">
                  <el-icon><Document /></el-icon>
                  <span class="dataset-name" :title="dataset.name">{{ dataset.name }}</span>
                </div>
                <div class="dataset-actions">
                  <el-button type="danger" text @click="removeDataset(dataset.id)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>

        </div>

        <div class="advanced-right">
          <!-- 预览调试 -->
          <div class="preview-debug">
            <div class="preview-header">
              <span>调试与预览</span>
              <div class="actions">
                <el-tooltip content="刷新">
                  <el-button size="small" circle @click="clearPreviewMessages">
                    <el-icon><RefreshRight /></el-icon>
                  </el-button>
                </el-tooltip>
                <div class="divider"></div>
                <el-tooltip content="展开/收起输入参数">
                  <el-button
                    size="small"
                    circle
                    :class="{ 'is-active': showInputFields }"
                    @click="showInputFields = !showInputFields"
                  >
                    <el-icon><SetUp /></el-icon>
                  </el-button>
                </el-tooltip>
              </div>
            </div>

            <div class="chat-area">
              <!-- 输入参数面板 -->
              <transition name="slide-fade">
                <div v-if="showInputFields" class="input-fields-panel">
                  <div class="panel-header">
                    <span>输入参数</span>
                  </div>
                  <div class="panel-content">
                    <el-form label-position="top">
                      <el-form-item
                        v-for="(item, index) in previewInputs"
                        :key="index"
                        :label="item.name"
                        :required="item.required"
                      >
                        <el-input v-model="item.value" :placeholder="`请输入${item.name}`" />
                      </el-form-item>
                    </el-form>
                  </div>
                </div>
              </transition>

              <!-- 变量表单 -->
              <div v-if="variables.length > 0" class="variables-preview">
                <div v-for="(variable, index) in variables" :key="index" class="variable-preview-item">
                  <div class="variable-label">{{ variable.displayName }}</div>
                  <!-- 文本输入框 -->
                  <el-input v-if="variable.type === 'text'"
                    v-model="variableValues[variable.name]"
                    :placeholder="`请输入${variable.displayName}`" />

                  <!-- 段落文本框 -->
                  <el-input v-else-if="variable.type === 'paragraph'"
                    v-model="variableValues[variable.name]"
                    type="textarea"
                    :rows="3"
                    :placeholder="`请输入${variable.displayName}`" />

                  <!-- 数字输入框 -->
                  <el-input-number v-else-if="variable.type === 'number'"
                    v-model="variableValues[variable.name]"
                    :placeholder="`请输入${variable.displayName}`"
                    :min="1" />

                  <!-- 下拉选择框 -->
                  <el-select v-else-if="variable.type === 'dropdown'"
                    v-model="variableValues[variable.name]"
                    :placeholder="`请选择${variable.displayName}`"
                    style="width: 100%;">
                    <el-option v-for="(option, optIndex) in variable.options"
                      :key="optIndex"
                      :label="option"
                      :value="option" />
                  </el-select>
                </div>
              </div>

              <!-- 消息列表区域 -->
              <div class="message-list" ref="chatMessagesRef">
                <!-- 空状态时的欢迎信息 -->
                <template v-if="messages.length === 0">
                  <div class="empty-state">
                    <div class="empty-icon">
                      <el-icon><ChatDotRound /></el-icon>
                    </div>
                    <div class="empty-title">开始对话</div>
                    <div class="empty-desc">发送消息与智能体进行对话并测试效果</div>
                  </div>
                </template>

                <!-- 消息列表 -->
                <div v-for="(message, index) in messages" :key="index" class="message-item">
                  <!-- 只有非开场白消息才显示问题部分 -->
                  <div v-if="!message.isOpening" class="question">
                    <div class="message-content">
                      <div class="avatar">
                        <el-icon><UserFilled /></el-icon>
                      </div>
                      <div class="content">{{ message.query }}</div>
                    </div>
                  </div>
                  <div class="answer">
                    <div class="message-content">
                      <div class="avatar">
                        <el-image v-if="basicForm.iconUrl" :src="basicForm.iconUrl" fit="cover" class="robot-icon"></el-image>
                        <el-icon v-else><ChatRound /></el-icon>
                      </div>
                      <div class="content">
                        <template v-if="message.status === 'pending'">
                          <span class="loading-dots">思考中<span>.</span><span>.</span><span>.</span></span>
                        </template>
                        <template v-else>
                          <div class="reset-content" v-html="message.answer"></div>
                        </template>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 底部输入区域 -->
              <div class="input-area">
                <!-- 推荐问题区域 -->
                <div v-if="advancedForm.suggestedQuestions.enabled && advancedForm.suggestedQuestions.items.length > 0" class="suggested-questions-container">
                  <div class="suggested-title">试着问问</div>
                  <div class="suggested-questions-list">
                    <div
                      v-for="(question, index) in advancedForm.suggestedQuestions.items"
                      :key="index"
                      class="suggested-question-item"
                      @click="handleSuggestedQuestion(question)"
                    >
                      {{ question }}
                    </div>
                  </div>
                </div>

                <div class="input-box">
                  <el-input
                    v-model="inputMessage"
                    type="textarea"
                    :rows="3"
                    placeholder="输入您的问题..."
                    resize="none"
                    :disabled="isResponding"
                    @keydown.enter.prevent="sendPreviewMessage"
                  />
                  <el-button
                    type="primary"
                    class="send-btn"
                    :disabled="isResponding || !inputMessage.trim()"
                    @click="sendPreviewMessage"
                  >
                    发送
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="step-actions" v-if="currentStep < 2">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>
    </div>

    <!-- <div v-if="currentStep === 4" class="step-content completion-step">
      <div class="completion-container">
        <div class="success-icon">
          <el-icon :size="64" color="#67C23A"><CircleCheckFilled /></el-icon>
        </div>

        <h2 class="completion-title">恭喜，智能体创建成功！</h2>
        <p class="completion-subtitle">您的智能体已准备就绪，可以开始使用</p>

        <div class="agent-preview">
          <div class="preview-card">
            <div v-if="basicForm.icon" class="preview-icon">
              <el-image :src="basicForm.iconUrl" fit="cover" class="preview-icon-image"></el-image>
            </div>
            <div v-else class="preview-icon default-icon">{{ '🤖' }}</div>
            <div class="preview-content">
              <div class="preview-title">{{ basicForm.name || '未命名智能体' }}</div>
              <div class="preview-desc">{{ basicForm.description || '暂无描述' }}</div>
            </div>
          </div>
        </div>

        <div class="completion-actions">
          <el-button type="primary" @click="viewAgentDetail">查看详情</el-button>
          <el-button @click="goToAgentList">返回列表</el-button>
        </div>
      </div>
    </div> -->
  </div>

  <!-- 内容审查设置弹窗 -->
  <el-dialog
    v-model="moderationDialogVisible"
    title="内容审查设置"
    width="600px"
    :close-on-click-modal="false"
    @closed="handleModerationDialogClosed"
  >
    <div class="moderation-dialog-content">
      <div class="dialog-section">
        <div class="section-title">类别</div>
        <div class="radio-options">
          <el-radio v-model="moderationType" label="keyword" border>关键词</el-radio>
        </div>
      </div>

      <div class="dialog-section">
        <div class="section-title">关键词</div>
        <div class="keyword-input-container">
          <el-input
            type="textarea"
            v-model="moderationKeywords"
            :rows="5"
            placeholder="每行一个，用换行符分隔。每行最多 100 个字符。"
            @input="updateKeywordCount"
            show-word-limit
            :maxlength="100"
          />
        </div>
      </div>

      <!-- 审查输入内容 -->
      <div class="dialog-section">
        <div class="moderation-toggle">
          <div class="toggle-title">审查输入内容</div>
          <el-switch v-model="moderationConfig.inputEnabled" />
        </div>

        <div v-if="moderationConfig.inputEnabled" class="preview-response">
          <div class="preview-input-container">
            <el-input
              type="textarea"
              v-model="moderationConfig.inputResponse"
              :rows="3"
              placeholder="这里设置预览回复, 支持 Markdown"
              @input="updateInputResponseCount"
               show-word-limit
            :maxlength="100"
            />
          </div>
        </div>
      </div>

      <!-- 审查输出内容 -->
      <div class="dialog-section">
        <div class="moderation-toggle">
          <div class="toggle-title">审查输出内容</div>
          <el-switch v-model="moderationConfig.outputEnabled" />
        </div>

        <div v-if="moderationConfig.outputEnabled" class="preview-response">
          <div class="preview-input-container">
            <el-input
              type="textarea"
              v-model="moderationConfig.outputResponse"
              :rows="3"
              placeholder="这里设置预览回复, 支持 Markdown"
              @input="updateOutputResponseCount"
              show-word-limit
              :maxlength="100"
            />
          </div>
        </div>
      </div>

      <div class="dialog-footer-note">
        审查输入内容和审查输出内容至少启用一项
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelModeration">取消</el-button>
        <el-button type="primary" @click="saveModeration">保存</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 变量弹窗 -->
  <el-dialog
    v-model="variableDialogVisible"
    title="设置变量"
    width="600px"
    :close-on-click-modal="false"
    @closed="resetVariableForm"
  >
    <div class="variable-dialog-content">
      <div class="dialog-section">
        <div class="section-title">变量类型</div>
        <div class="variable-type-cards">
          <div
            v-for="(type, index) in variableTypeOptions"
            :key="index"
            class="variable-type-card"
            :class="{ active: selectedVariableType === type.value }"
            @click="selectedVariableType = type.value"
          >
            <div class="icon">{{ type.icon }}</div>
            <div class="label">{{ type.label }}</div>
          </div>
        </div>
      </div>

      <div class="dialog-section">
        <div class="section-title">变量信息</div>
        <el-form label-position="top">
          <el-form-item label="变量名称" required>
            <el-input v-model="variableForm.name" placeholder="请输入变量名称" />
          </el-form-item>
          <el-form-item label="显示名称" required>
            <el-input v-model="variableForm.displayName" placeholder="请输入显示名称" />
          </el-form-item>
          <el-form-item label="最大长度" v-if="selectedVariableType === 'text' || selectedVariableType === 'paragraph'">
            <el-input v-model="variableForm.maxLength" placeholder="请输入最大长度" type="number" />
          </el-form-item>
          <el-form-item label="是否必填" v-if="selectedVariableType === 'text' || selectedVariableType === 'paragraph'">
            <el-switch v-model="variableForm.required" />
          </el-form-item>
          <el-form-item label="选项" v-if="selectedVariableType === 'dropdown'">
            <div class="options-container">
              <div v-for="(option, index) in variableForm.options" :key="index" class="option-item">
                {{ option }}
                <el-button type="danger" size="small" circle @click="removeOption(index)">
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
              <div class="add-option-input" v-if="showOptionInput">
                <el-input v-model="newOption" placeholder="请输入选项" @keyup.enter="addOption" />
                <el-button type="primary" size="small" @click="addOption">添加</el-button>
                <el-button type="default" size="small" @click="cancelAddOption">取消</el-button>
              </div>
              <div v-else class="add-option-btn">
                <el-button type="primary" plain @click="showAddOption">+ 添加选项</el-button>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelVariable">取消</el-button>
        <el-button type="primary" @click="saveVariable">保存</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 知识库选择弹窗 -->
  <el-dialog
    v-model="datasetDialogVisible"
    title="选择引用知识库"
    width="720px"
    :close-on-click-modal="false"
    :before-close="closeDatasetDialog"
  >
    <div class="dataset-selection">
      <div v-for="dataset in datasetList" :key="dataset.id" class="dataset-selection-item">
        <el-checkbox v-model="dataset.selected" @change="(val) => handleDatasetSelect(dataset, val)">
          <div class="dataset-icon">
            <el-icon><Folder /></el-icon>
          </div>
          <span class="dataset-selection-name" :title="dataset.name">{{ dataset.name }}</span>
        </el-checkbox>
        <div class="dataset-tags">
          <el-tag type="info" size="small">{{ dataset.document_count || 0 }}文档</el-tag>
          <el-tag type="success" size="small">高质量·问题检索</el-tag>
        </div>
      </div>

      <div v-if="datasetList.length === 0" class="empty-dataset-list">
        <el-empty description="暂无知识库数据" />
      </div>

      <div v-if="datasetLoading" class="dataset-loading">
        <el-skeleton :rows="3" animated />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDatasetDialog">取消</el-button>
        <el-button type="primary" @click="confirmSelectDatasets">添加</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import {
  ArrowLeft,
  ChatDotRound,
  Service,
  Setting,
  Edit,
  RefreshRight,
  SetUp,
  ChatRound,
  UserFilled,
  CircleCheckFilled,
  Close,
  Delete,
  Document,
  Folder
} from '@element-plus/icons-vue';
import { fetchTextEmbeddingModelList, fetchDefaultTextEmbeddingModel } from '@/api/knowledge';
import { fetchDatasetList, type Dataset } from '@/api/knowledge';
import { ElMessage, ElUpload, ElLoading, ElMessageBox } from 'element-plus';
import * as agentApi from '@/api/agents';
import type { ChatParams } from './types';
import { log } from 'console';

// 路由
const router = useRouter();

// 步骤
const currentStep = ref(1);
const selectedType = ref('');
const uploadProgress = ref(0);

// 基础表单
const basicForm = ref({
  name: '',
  description: '',
  icon: '', // 保存上传后的文件ID
  iconUrl: '', // 保存图片URL用于显示
  icon_type: 'image', // 固定为image类型
  mode: '', // 根据选择的类型设置
});

// 高级设置表单
const advancedForm = ref({
  rolePrompt: '',
  opening: {
    enabled: false,
    opening_statement: '',
  },
  suggestedQuestions: {
    enabled: false,
    items: [] as string[],
  },
  suggested: {
    enabled: false,
  },
  text2speech: {
    enabled: false,
  },
  speech2text: {
    enabled: false,
  },
  citation: {
    enabled: false,
  },
  moderation: {
    enabled: false,
    keywords: '', // 存储关键词文本
    input: {
      enabled: false,
      response: ''
    },
    output: {
      enabled: false,
      response: ''
    }
  },
  annotation_reply: {
    enabled: false,
    score_threshold: 0.8, // 匹配分数阈值
    embedding_model: {
      provider_name: "",
      model_name: "",
      enabled: true
    }
  },
  embeddingModel: {
    embedding_provider_name: "",
    embedding_model_name: "",
    enabled: true
  },
  metadataFilter: '',
});

// 审查设置
const moderationConfig = ref({
  inputEnabled: false,
  inputResponse: '',
  outputEnabled: false,
  outputResponse: ''
});

//
/*
const addQuestion = () => {
  if (!newQuestion.value.trim()) {
    ElMessage.warning('请输入问题内容');
    return;
  }

  advancedForm.value.suggestedQuestions.items.push(newQuestion.value);
  newQuestion.value = '';
  ElMessage.success('问题添加成功');
};

//
const removeQuestion = (index: number) => {
  advancedForm.value.suggestedQuestions.items.splice(index, 1);
  ElMessage.success('问题删除成功');
};
*/
const embeddingModelList = ref<any[]>([]);
// 获取文本嵌入模型列表
const fetchTextEmbeddingModelListData = async () => {
  try {
    const response = await fetchTextEmbeddingModelList();
    if (response.data && Array.isArray(response.data)) {
      embeddingModelList.value = response.data;
    }
  } catch (error) {
    console.error('获取文本嵌入模型列表失败:', error);
  }
};

// 获取默认文本嵌入模型
const fetchDefaultTextEmbeddingModelData = async () => {
  try {
    const response = await fetchDefaultTextEmbeddingModel();
    if (response.data) {
      advancedForm.value.embeddingModel.embedding_model_name = response.data.model;
      advancedForm.value.embeddingModel.embedding_provider_name = response.data.provider.provider;
    }
  } catch (error) {
    console.error('获取默认文本嵌入模型失败:', error);
  }
};

// AI辅助生成角色指令
const generateRolePrompt = async () => {
  try {
    const loading = ElLoading.service({
      lock: true,
      text: '正在生成角色指令...',
      background: 'rgba(0, 0, 0, 0.7)',
    });

    // 这里模拟API调用，实际项目中需要替换为真实API
    await new Promise(resolve => setTimeout(resolve, 1500));

    // 根据名称和描述生成角色指令
    const generatedPrompt = `你是一个名为"${basicForm.value.name}"的AI助手，${basicForm.value.description || '专注于为用户提供有用的信息和帮助'}。
你应该遵循以下指南：
1. 提供准确、有帮助的回答
2. 使用礼貌友好的语气
3. 当不确定答案时，诚实告知用户
4. 避免提供有害或不适当的内容`;

    advancedForm.value.rolePrompt = generatedPrompt;

    loading.close();
    ElMessage.success('角色指令生成成功');
  } catch (error) {
    ElMessage.error('生成失败，请稍后重试');
    console.error('生成角色指令失败:', error);
  }
};

//
/*
const generateSuggestedQuestions = async () => {
  try {
    const loading = ElLoading.service({
      lock: true,
      text: '正在生成推荐问题...',
      background: 'rgba(0, 0, 0, 0.7)',
    });

    // 这里模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1200));

    // 根据智能体描述生成相关问题
    const questions = [
      `${basicForm.value.name}能提供哪些服务？`,
      `如何有效地使用${basicForm.value.name}？`,
      `${basicForm.value.name}的主要功能是什么？`,
    ];

    advancedForm.value.suggestedQuestions.items = questions;
    advancedForm.value.suggestedQuestions.enabled = true;

    loading.close();
    ElMessage.success('推荐问题生成成功');
  } catch (error) {
    ElMessage.error('生成失败，请重试');
    console.error('生成推荐问题失败:', error);
  }
};
*/

// 嵌入模型选择器改变事件处理函数
const handleEmbeddingModelChange = (value: string) => {
  console.log('嵌入模型选择器改变事件处理函数', value);
  // 根据选择的嵌入模型名称，找到对应的提供商
  for (const provider of embeddingModelList.value) {
    for (const model of provider.models) {
      if (model.model === value) {
        advancedForm.value.embeddingModel.embedding_provider_name = provider.provider;
        advancedForm.value.embeddingModel.embedding_model_name = value;
        console.log('已更新嵌入模型信息', advancedForm.value.embeddingModel);
        return;
      }
    }
  }
};

// 预览消息
const messages = ref<Array<{
  query: string;
  answer: string;
  status: 'pending' | 'done';
  conversation_id?: string;
  id?: string;
  parent_message_id?: string | null;
  isOpening?: boolean; // 添加标记开场白的字段
}>>([]);

// 预览相关状态
const inputMessage = ref('');
const isResponding = ref(false);
const showInputFields = ref(false);
const chatMessagesRef = ref<HTMLElement | null>(null);
const messageIds = ref<Array<{id: string; type: 'question' | 'answer'}>>([]);
const previewInputs = ref<Array<{name: string; value: string; required: boolean}>>([
  { name: '用户ID', value: 'user123', required: true },
  { name: '上下文信息', value: '', required: false },
]);

// 处理推荐问题点击
const handleSuggestedQuestion = (question: string) => {
  inputMessage.value = question;
  sendPreviewMessage();
};

// 发送预览消息
const sendPreviewMessage = async () => {
  if (!inputMessage.value.trim() || isResponding.value) return;

  // 验证必填变量是否已填写
  if (variables.value.length > 0) {
    const requiredVariables = variables.value.filter(v => v.required);
    const missingVariables = requiredVariables.filter(v => {
      const value = variableValues.value[v.name];
      return value === undefined || value === null || value === '';
    });

    if (missingVariables.length > 0) {
      const missingNames = missingVariables.map(v => v.displayName).join('、');
      ElMessage.warning(`请填写必填变量: ${missingNames}`);
      return;
    }
  }

  // 获取当前的智能体ID
  const agentId = router.currentRoute.value.query.agentId as string;

  // 添加用户消息
  const userQuery = inputMessage.value;
  const questionId = `question-${Date.now()}`;
  messages.value.push({
    query: userQuery,
    answer: '',
    status: 'pending',
    id: questionId
  });

  // 记录用户问题ID
  messageIds.value.push({ id: questionId, type: 'question' });

  // 滚动到底部
  scrollToBottom();

  // 准备回复
  isResponding.value = true;

  // 清空输入
  inputMessage.value = '';

  try {
    // 如果有agentId，尝试调用真实API
    if (agentId) {
      // 使用API函数调用聊天消息服务
      const params: ChatParams = {
        query: userQuery,
        response_mode: "streaming",
        conversation_id: "",
        parent_message_id: null,
        files: [],
        inputs: variableValues.value, // 直接使用variableValues对象，其中包含了所有变量名和对应的用户输入值
        model_config: {
          pre_prompt: advancedForm.value.rolePrompt,
          prompt_type: "simple",
          chat_prompt_config: {},
          completion_prompt_config: {},
          user_input_form: [],
          dataset_query_variable: "",
          opening_statement: advancedForm.value.opening.enabled ? advancedForm.value.opening.opening_statement : "",
          more_like_this: {
            enabled: false
          },
          suggested_questions: [],
          suggested_questions_after_answer: {
            enabled: advancedForm.value.suggestedQuestions.enabled
          },
          text_to_speech: {
            enabled: false
          },
          speech_to_text: {
            enabled: false
          },
          retriever_resource: {
            enabled: true
          },
          sensitive_word_avoidance: {
            enabled: advancedForm.value.moderation.enabled,
            type: advancedForm.value.moderation.enabled ? "keywords" : "",
            config: advancedForm.value.moderation.enabled ? {
                inputs_config: {
                  enabled: advancedForm.value.moderation.input.enabled,
                  preset_response: advancedForm.value.moderation.input.response
                },
                outputs_config: {
                  enabled: advancedForm.value.moderation.output.enabled,
                  preset_response: advancedForm.value.moderation.output.response
                },
                keywords: advancedForm.value.moderation.keywords
              } : {}
          },
          agent_mode: {
            enabled: false,
            max_iteration: 5,
            strategy: "function_call",
            tools: []
          },
          dataset_configs: {
            retrieval_model: "multiple",
            top_k: 4,
            reranking_enable: false,
            metadata_filtering_mode: "disabled",
            datasets: {
              datasets: selectedDatasets.value.length > 0 ? selectedDatasets.value.map(dataset => ({
                dataset: {
                  enabled: true,
                  id: dataset.id
                }
              })) : []
            }
          },
          file_upload: {
            enabled: false,
            allowed_file_types: [],
            allowed_file_extensions: [
              ".JPG", ".JPEG", ".PNG", ".GIF", ".WEBP", ".SVG",
              ".MP4", ".MOV", ".MPEG", ".MPGA"
            ],
            allowed_file_upload_methods: ["remote_url", "local_file"],
            number_limits: 3,
            image: {
              detail: "high",
              enabled: false,
              number_limits: 3,
              transfer_methods: ["remote_url", "local_file"]
            },
            fileUploadConfig: {
              file_size_limit: 15,
              batch_count_limit: 5,
              image_file_size_limit: 10,
              video_file_size_limit: 100,
              audio_file_size_limit: 50,
              workflow_file_upload_limit: 10
            }
          },
          annotation_reply: {
            ...advancedForm.value.annotation_reply,
            embedding_model: {
              ...advancedForm.value.annotation_reply.embedding_model
            }
          }, // 直接传递annotation_reply对象
          supportAnnotation: true,
          supportCitationHitInfo: true,
          model: {
          provider: "langgenius/tongyi/tongyi",
          name: "qwen-max",
            mode: "chat",
            completion_params: {}
        },
        }
      };

      // 如果有变量，添加到 user_input_form
      if (variables.value.length > 0) {
        params.model_config.user_input_form = variables.value.map((variable: any) => {
          let result: any = {};
          console.log(888888, variable)
          // 根据变量类型生成不同的配置
          if (variable.type === 'text') {
            // 文本类型
            result = {
              "text-input": {
                label: variable.displayName,
                variable: variable.name,
                required: variable.required || false,
                max_length: variable.maxLength,
                default: ""
              }
            };
          } else if (variable.type === 'paragraph') {
            // 段落类型
            result = {
              "paragraph": {
                label: variable.displayName,
                variable: variable.name,
                required: variable.required || false,
                max_length: variable.maxLength ? parseInt(variable.maxLength) : undefined,
                default: ""
              }
            };
          } else if (variable.type === 'dropdown') {
            // 下拉选项类型
            result = {
              "select": {
                label: variable.displayName,
                variable: variable.name,
                required: variable.required || false,
                options: variable.options || [],
                default: ""
              }
            };
          } else if (variable.type === 'number') {
            // 数字类型
            result = {
              "number": {
                label: variable.displayName,
                variable: variable.name,
                required: variable.required || false,
                default: ""
              }
            };
          }

          return result;
        });
      } else {
        params.model_config.user_input_form = [];
      }

      // 调用更新API
      const response = await agentApi.sendChatMessage(agentId, params, {
        onMessage: (text: string) => {
          // 处理服务器返回的数据
          const messageLines = text.split("\n");
          messageLines.forEach((message: string) => {
            let jsonStr = message.replace(/^data: /, "").trim();
            if (!jsonStr) return;

            try {
              const data = JSON.parse(jsonStr);
              const lastMessage = messages.value[messages.value.length - 1];

              if (lastMessage) {
                // 拼接answer内容
                if (data.answer) {
                  lastMessage.answer += data.answer;
                  lastMessage.status = 'done';
                }

                // 更新其他字段
                if (data.conversation_id) {
                  lastMessage.conversation_id = data.conversation_id;
                }
                if (data.id) {
                  lastMessage.id = data.id;
                  // 记录消息id，方便后续操作
                  console.log('消息ID:', data.id);
                  // 可以添加到全局状态或本地存储
                  localStorage.setItem('lastMessageId', data.id);
                  messageIds.value.push({ id: data.id, type: 'answer' });
                }

                // 更新完整的answer
                // lastMessage.answer = fullAnswer;

                // 滚动到底部
                scrollToBottom();
              }
            } catch (error) {
              console.error("解析响应数据失败:", error);
            }
          });
        },
        onError: (error: any) => {
          console.error("发送消息失败:", error);
          ElMessage.error("发送消息失败，请重试");
          const lastMessage = messages.value[messages.value.length - 1];
          if (lastMessage) {
            lastMessage.status = 'done';
            lastMessage.answer = "很抱歉，发生了错误，请重试。";
          }
        },
        onComplete: () => {
          // 完成回复
          isResponding.value = false;

          // 如果有conversation_id，获取聊天历史
          const lastMessage = messages.value[messages.value.length - 1];
          if (lastMessage && lastMessage.conversation_id) {
            // 调用getChatMessages获取完整的聊天历史
            agentApi.getChatMessages(agentId, lastMessage.conversation_id)
              .then((response: any) => {
                console.log('获取聊天历史成功:', response);

                // 如果启用了推荐问题功能，获取推荐问题
                if (advancedForm.value.suggestedQuestions.enabled && lastMessage.id) {
                  agentApi.getSuggestedQuestions(agentId, lastMessage.id)
                    .then((suggestedResponse: any) => {
                      console.log('获取推荐问题成功:', suggestedResponse);
                      // 更新推荐问题列表
                      if (suggestedResponse && Array.isArray(suggestedResponse.data)) {
                        advancedForm.value.suggestedQuestions.items = suggestedResponse.data;
                      } else {
                        // 数据格式不符合预期，清空推荐问题
                        advancedForm.value.suggestedQuestions.items = [];
                        console.warn('推荐问题数据格式不符合预期', suggestedResponse);
                      }
                    })
                    .catch((error: any) => {
                      console.error('获取推荐问题失败:', error);
                      // 出错时清空推荐问题
                      advancedForm.value.suggestedQuestions.items = [];
                    });
                }
              })
              .catch((error: any) => {
                console.error('获取聊天历史失败:', error);
              });
          }
        }
      });
    } else {
      // 使用模拟数据
      setTimeout(() => {
        const mockResponse = {
          query: userQuery,
          answer: `这是对"${userQuery}"的回复。使用了角色指令:\n${advancedForm.value.rolePrompt || '未设置角色指令'}\n\n实际部署后，回复将由配置的模型和知识库生成。`,
          status: 'done' as const
        };

        // 更新消息状态
        messages.value[messages.value.length - 1] = mockResponse;

        // 滚动到底部
        scrollToBottom();

        // 完成回复
        isResponding.value = false;
      }, 1500);
    }
  } catch (error) {
    console.error("发送消息失败:", error);
    ElMessage.error("发送消息失败，请重试");
    const lastMessage = messages.value[messages.value.length - 1];
    if (lastMessage) {
      lastMessage.status = 'done';
      lastMessage.answer = "很抱歉，发生了错误，请重试。";
    }
    isResponding.value = false;
  }
};

// 清空预览消息
const clearPreviewMessages = () => {
  messages.value = [];
  isResponding.value = false;
  messageIds.value = [];

  // 如果有欢迎语，添加欢迎消息
  if (advancedForm.value.opening.enabled && advancedForm.value.opening.opening_statement) {
    messages.value.push({
      query: "",
      answer: advancedForm.value.opening.opening_statement,
      status: 'done',
      isOpening: true // 添加标记开场白的字段
    });

    // 滚动到底部
    scrollToBottom();

    // 添加推荐问题逻辑 - 解决handleSuggestedQuestion未使用的警告
    if (advancedForm.value.suggestedQuestions.enabled && advancedForm.value.suggestedQuestions.items.length > 0) {
      setTimeout(() => {
        // 这里是模拟点击第一个推荐问题，实际使用中可能需要UI上的按钮
        if (advancedForm.value.suggestedQuestions.items.length > 0) {
          handleSuggestedQuestion(advancedForm.value.suggestedQuestions.items[0]);
        }
      }, 1000);
    }
  } else {
    messages.value = [];
  }
};

// 滚动到最新消息
const scrollToBottom = () => {
  setTimeout(() => {
    if (chatMessagesRef.value) {
      chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight;
    }
  }, 100);
};

// 校验上传的文件类型
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!');
    return false;
  }
  return true;
};

// 自定义上传
const customUpload = async (options: any) => {
  const { file } = options;
  uploadProgress.value = 0;

  try {
    const response = await agentApi.uploadFile(file, (progress: number) => {
      uploadProgress.value = progress;
    });

    console.log('上传响应:', response.data);

    // 文件上传成功，只保存ID用于提交表单
    basicForm.value.icon = response.data.id;

    // 使用 File 对象的 URL 用于预览
    basicForm.value.iconUrl = URL.createObjectURL(file);

    ElMessage.success('图标上传成功');
  } catch (error) {
    console.error('图标上传失败', error);
    ElMessage.error('图标上传失败，请重试');
  } finally {
    setTimeout(() => {
      uploadProgress.value = 0;
    }, 500);
  }
};

// 返回
const goBack = () => {
  router.push('/agents');
};

// 取消
const cancel = () => {
  ElMessage.info('已取消创建');
  router.push('/agents');
};

// 选择聊天助手并自动进入下一步
const selectChatAssistant = () => {
  selectedType.value = 'chat';
  basicForm.value.mode = 'chat';
  // 自动进入下一步
  // setTimeout(() => {
  //   currentStep.value = 2;
  // }, 200);
};

// 选择类型
const selectType = (type: string) => {
  selectedType.value = type;
  basicForm.value.mode = type === 'agent' ? 'agent-chat' : type as any;

  // 记录选择的类型，供下一步使用
  localStorage.setItem('selectedAgentType', type);
};

// 下一步处理函数 - 保存并发布智能体
const goToNextStep = () => {
  if (currentStep.value == 3) {
    // 添加二次确认弹窗
    ElMessageBox.confirm(
      '确定要发布智能体更新吗？',
      '发布确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      // 用户点击确定，保留开关状态并重置聊天框
      clearPreviewMessages();
      ElMessage.success('已重置调试区域');
      // 发布更新
      saveAgent().catch(error => {
        console.error('发布失败:', error);
      });
    }).catch(() => {
      // 用户点击取消，恢复开关状态
      ElMessage.info('已取消发布');
    });
  } else {
    createAgentHandler().then(() => {
      // 创建成功后的操作
      nextStep();
    }).catch(error => {
      console.error('创建失败:', error);
    });
  }
};

// 嵌入网站相关变量
const embedDialogVisible = ref(false);
const selectedEmbedOption = ref('fullpage');
const embedCode = ref('');

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  if (command === 'embed') {
    openEmbedDialog();
  }
};

// 打开嵌入网站弹窗
const openEmbedDialog = () => {
  // 获取当前的智能体ID
  const agentId = router.currentRoute.value.query.agentId as string;

  if (!agentId) {
    ElMessage.error('请先创建智能体');
    return;
  }

  // 生成默认的嵌入代码
  generateEmbedCode('fullpage', agentId);

  // 显示弹窗
  embedDialogVisible.value = true;
};

// 选择嵌入选项
const selectEmbedOption = (option: string) => {
  selectedEmbedOption.value = option;

  // 获取当前的智能体ID
  const agentId = router.currentRoute.value.query.agentId as string;

  // 生成对应的嵌入代码
  generateEmbedCode(option, agentId);
};

// 生成嵌入代码
const generateEmbedCode = (option: string, agentId: string) => {
  // 基础URL
  const baseUrl = window.location.origin;

  // 根据选项生成不同的属性
  let style = '';

  if (option === 'fullpage') {
    style = 'width: 100%; height: 100%; min-height: 700px';
  } else if (option === 'sidebar') {
    style = 'width: 350px; height: 100%; min-height: 700px';
  } else if (option === 'popup') {
    style = 'width: 400px; height: 600px; min-height: 600px';
  }

  // 生成iframe代码
  embedCode.value = `<iframe
  src="${siteInfo.value.app_base_url}/chatbot/${siteInfo.value.access_token}"
  style="${style}"
  frameborder="0"
  allow="microphone">
</iframe>`;
};

// 复制嵌入代码
const copyEmbedCode = () => {
  navigator.clipboard.writeText(`${embedCode.value}`)
    .then(() => {
      ElMessage.success('已复制链接到剪贴板');
    })
    .catch(() => {
      ElMessage.error('复制失败，请手动复制');
    });
};

// 自动设置默认图标
const setDefaultIcon = () => {
  // 获取选择的类型
  const selectedAgentType = localStorage.getItem('selectedAgentType') || '';

  // 根据类型选择对应的图标
  let iconFileName = '';

  if (selectedAgentType === 'chat') {
    // 聊天助手对应ltzs.png
    iconFileName = 'ltzs.png';
  } else if (selectedAgentType === 'agent') {
    // 智能体对应znt.png
    iconFileName = 'znt.png';
  } else {
    // 如果没有选择类型，不设置图标
    return;
  }

  // 如果已经有图标，则不覆盖
  if (basicForm.value.icon) {
    return;
  }

  // 设置默认图标路径
  const iconPath = `/src/assets/images/${iconFileName}`;

  // 显示默认图标
  basicForm.value.iconUrl = iconPath;

  // 设置一个默认值，以便表单提交
  basicForm.value.icon = 'default_icon';
};

// 下一步
const nextStep = async () => {
  // 如果从第1步进入第2步，自动设置默认图标
  if (currentStep.value === 1) {
    setDefaultIcon();
  }

  // 第2步（基础配置）点击下一步时，创建智能体
  if (currentStep.value === 2) {
    // 表单验证
    if (!basicForm.value.name.trim()) {
      ElMessage.warning('请输入智能体名称');
      return;
    }

    // 移除图标必填验证
    // if (!basicForm.value.icon) {
    //   ElMessage.warning('请上传智能体图标');
    //   return;
    // }

    // 显示加载状态
    const loading = ElLoading.service({
      lock: true,
      text: '正在创建智能体...',
      background: 'rgba(0, 0, 0, 0.7)',
    });

    try {
      // 调用API创建智能体
      const createAgentParams: any = {
        name: basicForm.value.name,
        description: basicForm.value.description,
        icon_type: 'image', // 固定为image类型
        mode: basicForm.value.mode, // 使用选择的模式
      };

      // 如果有上传图标，则使用上传的图标ID
      if (basicForm.value.icon && basicForm.value.icon !== 'default_icon') {
        createAgentParams.icon = basicForm.value.icon;
      }

      console.log('创建智能体参数:', createAgentParams);

      const response = await agentApi.createAgent(createAgentParams);
      console.log('创建智能体成功:', response);

      // 获取创建成功后返回的智能体ID
      const agentId = response.id;

      // 将智能体ID添加到URL中
      router.replace({
        query: {
          ...router.currentRoute.value.query,
          agentId
        }
      });

      ElMessage.success('智能体创建成功');

      // 成功后进入下一步
      if (currentStep.value < 4) {
        currentStep.value++;
      }
    } catch (error) {
      console.error('创建智能体失败:', error);
      ElMessage.error('创建智能体失败，请重试');
    } finally {
      // 关闭加载状态
      loading.close();
    }
  } else {
    // 其他步骤直接前进
    if (currentStep.value < 4) {
      currentStep.value++;
    }
  }
};

// 上一步
const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

// 保存智能体
const saveAgent = async () => {
  try {
    const loading = ElLoading.service({
      lock: true,
      text: '发布中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 获取当前的智能体ID
    const agentId = router.currentRoute.value.query.agentId as string;

    // 如果没有agentId，提示错误并返回
    if (!agentId) {
      ElMessage.error('发布失败：未找到智能体ID，请重新创建智能体');
      loading.close();
      return;
    }

    // 构建 model_config 参数
    const modelConfig: any = {
      agent_mode: {
        max_iteration: 5,
        enabled: advancedForm.value.agentMode?.enabled || false,
        strategy: advancedForm.value.agentMode?.strategy || "function_call",
        tools: [],
        prompt: null
      },
      chat_prompt_config: {},
      completion_prompt_config: {},
      dataset_configs: {
        retrieval_model: "multiple",
        top_k: 4,
        reranking_mode: "reranking_model",
        reranking_model: {
          reranking_provider_name: "langgenius/tongyi/tongyi",
          reranking_model_name: "gte-rerank"
        },
        reranking_enable: true,
        metadata_filtering_mode: "disabled",
        datasets: {
          datasets: selectedDatasets.value.length > 0 ? selectedDatasets.value.map(dataset => ({
            dataset: {
              enabled: true,
              id: dataset.id
            }
          })) : []
        }
      },
      dataset_query_variable: "",
      file_upload: {},
      model: {
        provider: "langgenius/tongyi/tongyi",
        name: "qwen-max",
        mode: "chat",
        completion_params: {}
      },
      more_like_this: {
        enabled: advancedForm.value.moreLike?.enabled || false
      },
      opening_statement: advancedForm.value.opening.enabled ? advancedForm.value.opening.opening_statement : "",
      pre_prompt: advancedForm.value.rolePrompt || "",
      prompt_type: "simple",
      retriever_resource: {
        enabled: advancedForm.value.retrieverResource?.enabled || true
      },
      sensitive_word_avoidance: {
            enabled: advancedForm.value.moderation.enabled,
            type: advancedForm.value.moderation.enabled ? "keywords" : "",
            config: advancedForm.value.moderation.enabled ? {
                inputs_config: {
                  enabled: advancedForm.value.moderation.input.enabled,
                  preset_response: advancedForm.value.moderation.input.response
                },
                outputs_config: {
                  enabled: advancedForm.value.moderation.output.enabled,
                  preset_response: advancedForm.value.moderation.output.response
                },
                keywords: advancedForm.value.moderation.keywords
              } : {}
          },
      speech_to_text: {
        enabled: advancedForm.value.speech2text?.enabled || false
      },
      // suggested_questions: advancedForm.value.suggestedQuestions.enabled ?
      //                     advancedForm.value.suggestedQuestions.items : [],
      suggested_questions_after_answer: {
        enabled: advancedForm.value.suggestedQuestions?.enabled || false
      },
      text_to_speech: {
        enabled: advancedForm.value.text2speech?.enabled || false
      },
      annotation_reply: {
        enabled: advancedForm.value.citation?.enabled || false
      }
    };

    // 如果有变量，添加到 user_input_form
    if (variables.value.length > 0) {
      modelConfig.user_input_form = variables.value.map((variable: any) => {
        let result: any = {};

        // 根据变量类型生成不同的配置
        if (variable.type === 'text') {
          // 文本类型
          result = {
            "text-input": {
              label: variable.displayName,
              variable: variable.name,
              required: variable.required || false,
              max_length: variable.maxLength,
              default: ""
            }
          };
        } else if (variable.type === 'paragraph') {
          // 段落类型
          result = {
            "paragraph": {
              label: variable.displayName,
              variable: variable.name,
              required: variable.required || false,
              max_length: variable.maxLength ? parseInt(variable.maxLength) : undefined,
              default: ""
            }
          };
        } else if (variable.type === 'dropdown') {
          // 下拉选项类型
          result = {
            "select": {
              label: variable.displayName,
              variable: variable.name,
              required: variable.required || false,
              options: variable.options || [],
              default: ""
            }
          };
        } else if (variable.type === 'number') {
          // 数字类型
          result = {
            "number": {
              label: variable.displayName,
              variable: variable.name,
              required: variable.required || false,
              default: ""
            }
          };
        }

        return result;
      });
    } else {
      modelConfig.user_input_form = [];
    }

    console.log('发布智能体参数:', { modelConfig });

    // 调用更新API
    const response = await agentApi.updateAgent(agentId, modelConfig);
    console.log('发布智能体成功:', response);

    ElMessage.success('智能体发布更新成功');
    loading.close();

    return true;
  } catch (error) {
    console.error('发布智能体失败:', error);
    ElMessage.error('发布失败，请重试');
  }
};

// 创建智能体
const createAgentHandler = async () => {
  try {
    // 构造高级设置参数
    const advancedParams: any = {
      prompt: advancedForm.value.systemPrompt,
      temperature: advancedForm.value.temperature,
      top_p: advancedForm.value.topP,
      chat_context_count: advancedForm.value.chatContextCount,
      suggested_questions_count: advancedForm.value.suggestedQuestionsCount,
      suggested_questions: advancedForm.value.suggestedQuestions?.items || [],
      status: 'active',
      allow_file_upload: true,
      knowledge_ids: advancedForm.value.selectedKnowledgeIds,
      llm: {
        provider_name: advancedForm.value.model.provider,
        model_name: advancedForm.value.model.name,
      },
      metadata_filter: advancedForm.value.metadataFilter,
    };

    // 添加注释回复设置
    if (advancedForm.value.annotation_reply.enabled) {
      advancedParams.annotation_reply = {
        enabled: advancedForm.value.annotation_reply.enabled,
        score_threshold: advancedForm.value.annotation_reply.score_threshold,
        url: advancedForm.value.annotation_reply.url || ''
      };

      if (advancedForm.value.annotation_reply.embedding_model) {
        advancedParams.annotation_reply.embedding_model = {
          provider_name: advancedForm.value.annotation_reply.embedding_model.provider_name,
          model_name: advancedForm.value.annotation_reply.embedding_model.model_name
        };
      }
    }
    console.log("advancedForm.value", advancedForm.value);
    // 构造内容审查设置参数 - 转换为sensitive_word_avoidance格式
    if (advancedForm.value.moderation.enabled) {
      advancedParams.sensitive_word_avoidance = {
        type: "keywords",
        enabled: true,
        config: {
          inputs_config: {
            enabled: advancedForm.value.moderation.input.enabled,
            preset_response: advancedForm.value.moderation.input.response
          },
          outputs_config: {
            enabled: advancedForm.value.moderation.output.enabled,
            preset_response: advancedForm.value.moderation.output.response
          },
          keywords: advancedForm.value.moderation.keywords
        }
      };
    }

    // 创建智能体的完整参数
    const createAgentParams = {
      ...basicForm.value,
      ...advancedParams
    };

    // 调用创建API
    const response = await agentApi.createAgent(createAgentParams);
    return response;
  } catch (error) {
    ElMessage.error('创建失败，请检查表单并重试');
    console.error('创建智能体失败:', error);
  }
};

// 监听步骤变化
watch(currentStep, (newStep: number) => {
  if (newStep === 3) {
    // 当进入预览步骤时，初始化消息列表
    initPreviewMessages();
  }
});

// 初始化预览消息列表
const initPreviewMessages = () => {
  // 清空现有消息
  messages.value = [];

  // 如果有欢迎语，添加欢迎消息
  if (advancedForm.value.opening.enabled && advancedForm.value.opening.opening_statement) {
    messages.value.push({
      query: "",
      answer: advancedForm.value.opening.opening_statement,
      status: 'done',
      isOpening: true // 添加标记开场白的字段
    });

    // 滚动到底部
    scrollToBottom();

    // 添加推荐问题逻辑 - 解决handleSuggestedQuestion未使用的警告
    if (advancedForm.value.suggestedQuestions.enabled && advancedForm.value.suggestedQuestions.items.length > 0) {
      setTimeout(() => {
        // 这里是模拟点击第一个推荐问题，实际使用中可能需要UI上的按钮
        if (advancedForm.value.suggestedQuestions.items.length > 0) {
          handleSuggestedQuestion(advancedForm.value.suggestedQuestions.items[0]);
        }
      }, 1000);
    }
  } else {
    messages.value = [];
  }
};

// 在methods部分添加
const viewAgentDetail = () => {
  const agentId = router.currentRoute.value.query.agentId;
  // 跳转到智能体详情页
  if (agentId) {
    router.push(`/agents/detail/${agentId}`);
  }
};

const goToAgentList = () => {
  // 返回智能体列表页
  router.push('/agents');
};

// 处理推荐问题开关变化
const handleSuggestedQuestionsChange = (value: boolean) => {
  // 检查聊天框是否有除开场白外的内容
  const hasNonOpeningMessages = messages.value.some(message => !message.isOpening);

  if (hasNonOpeningMessages) {
    // 弹出确认对话框
    ElMessageBox.confirm(
      '修改编排将重置调试区域，确定吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(() => {
        // 用户点击确定，保留开关状态并重置聊天框
        clearPreviewMessages();
        ElMessage.success('已重置调试区域');
      })
      .catch(() => {
        // 用户点击取消，恢复开关状态
        advancedForm.value.suggestedQuestions.enabled = !value;
        ElMessage.info('已取消操作');
      });
  } else {
    // 聊天框无内容，直接重置推荐问题列表
    if (value) {
      advancedForm.value.suggestedQuestions.items = [];
    }
  }
};

// 保存开场白并在预览中显示
const handleSaveOpening = () => {
  if (!advancedForm.value.opening.opening_statement.trim()) {
    ElMessage.warning('请输入开场白内容');
    return;
  }

  // 清空预览消息并自动添加开场白
  clearPreviewMessages();

  ElMessage.success('开场白保存成功，预览已更新');
};

// 内容审查设置弹窗相关变量
const moderationDialogVisible = ref(false);
const moderationType = ref('keyword');
const moderationKeywords = ref('');
const keywordCount = ref(0);
const inputResponseCount = ref(0); // 审查输入内容字数计数
const outputResponseCount = ref(0); // 审查输出内容字数计数
const previousModerationState = ref(false); // 保存之前的开关状态

// 打开内容审查设置弹窗
const openModerationDialog = () => {
  // 若已有内容，则显示在文本框中
  if (advancedForm.value.moderation.keywords) {
    moderationKeywords.value = advancedForm.value.moderation.keywords;
    updateKeywordCount();
  }

  // 初始化输入输出审查开关和内容
  if (advancedForm.value.moderation.input) {
    moderationConfig.value.inputEnabled = advancedForm.value.moderation.input.enabled || false;
    moderationConfig.value.inputResponse = advancedForm.value.moderation.input.response || '';
    updateInputResponseCount();
  }

  if (advancedForm.value.moderation.output) {
    moderationConfig.value.outputEnabled = advancedForm.value.moderation.output.enabled || false;
    moderationConfig.value.outputResponse = advancedForm.value.moderation.output.response || '';
    updateOutputResponseCount();
  }

  moderationDialogVisible.value = true;
};

// 关闭内容审查设置弹窗
const closeModerationDialog = () => {
  moderationDialogVisible.value = false;
};

// 取消内容审查设置
const cancelModeration = () => {
  closeModerationDialog();
};

// 保存内容审查设置
const saveModeration = () => {
  // 验证是否至少启用了输入或输出审查中的一项
  if (moderationConfig.value.inputEnabled === false && moderationConfig.value.outputEnabled === false) {
    ElMessage.warning('审查输入内容和审查输出内容至少启用一项');
    return;
  }

  // 保存关键词
  advancedForm.value.moderation.keywords = moderationKeywords.value;

  // 保存输入审查设置
  if (!advancedForm.value.moderation.input) {
    advancedForm.value.moderation.input = { enabled: false, response: '' };
  }
  advancedForm.value.moderation.input.enabled = moderationConfig.value.inputEnabled;
  advancedForm.value.moderation.input.response = moderationConfig.value.inputResponse;

  // 保存输出审查设置
  if (!advancedForm.value.moderation.output) {
    advancedForm.value.moderation.output = { enabled: false, response: '' };
  }
  advancedForm.value.moderation.output.enabled = moderationConfig.value.outputEnabled;
  advancedForm.value.moderation.output.response = moderationConfig.value.outputResponse;

  // 设置状态为已配置
  previousModerationState.value = true;

  // 关闭弹窗
  closeModerationDialog();

  // 显示成功提示
  ElMessage.success('内容审查设置已保存');
};

// 内容审查设置弹窗关闭事件
const handleModerationDialogClosed = () => {
  // 不清空关键词输入框，保留状态
  // cancelModeration();?
  if(!advancedForm.value.moderation.input.enabled && !advancedForm.value.moderation.output.enabled) {
    advancedForm.value.moderation.enabled = false;
  }else {
    advancedForm.value.moderation.enabled = true;
  }
};

// 内容审查开关变化事件处理函数
const handleModerationToggle = (value: boolean) => {
  if (value) {
    // 保存之前的状态
    previousModerationState.value = false;
    // 打开弹窗
    openModerationDialog();
  } else {
    // 关闭时清空关键词
    advancedForm.value.moderation.keywords = '';
    // 重置输入输出审查状态
    if (advancedForm.value.moderation.input) {
      advancedForm.value.moderation.input.enabled = false;
      advancedForm.value.moderation.input.response = '';
    }
    if (advancedForm.value.moderation.output) {
      advancedForm.value.moderation.output.enabled = false;
      advancedForm.value.moderation.output.response = '';
    }
    // 关闭弹窗
    closeModerationDialog();
  }
};

// 关键词输入框输入事件处理函数
const updateKeywordCount = () => {
  // 按行分割，空行不计数
  const lines = moderationKeywords.value.split('\n').filter((line: string) => line.trim() !== '');
  keywordCount.value = lines.length;
};

// 审查输入内容输入事件处理函数
const updateInputResponseCount = () => {
  const lines = moderationConfig.value.inputResponse.split('\n').filter((line: string) => line.trim() !== '');
  inputResponseCount.value = lines.length;
};

// 审查输出内容输入事件处理函数
const updateOutputResponseCount = () => {
  const lines = moderationConfig.value.outputResponse.split('\n').filter((line: string) => line.trim() !== '');
  outputResponseCount.value = lines.length;
};

// 变量弹窗控制
const variableDialogVisible = ref(false);
const variableTypeOptions = ref([
  { label: '文本', value: 'text', icon: '📄' },
  { label: '段落', value: 'paragraph', icon: '📝' },
  { label: '下拉选项', value: 'dropdown', icon: '📋' },
  { label: '数字', value: 'number', icon: '#' }
]);
const selectedVariableType = ref('text');

// 变量表单
const variableForm = ref({
  name: '',
  displayName: '',
  maxLength: 48,
  required: false,
  options: [] as any[] // 仅用于下拉选项类型
});

// 变量选项操作
const newOption = ref('');
const showOptionInput = ref(false);

// 显示添加选项输入框
const showAddOption = () => {
  showOptionInput.value = true;
  newOption.value = '';
};

// 取消添加选项
const cancelAddOption = () => {
  showOptionInput.value = false;
  newOption.value = '';
};

// 添加选项
const addOption = () => {
  if (newOption.value.trim()) {
    variableForm.value.options.push(newOption.value);
    newOption.value = '';
    // 添加完成后，隐藏输入框，回到添加按钮状态
    showOptionInput.value = false;
  }
};

// 删除选项
const removeOption = (index: number) => {
  variableForm.value.options.splice(index, 1);
};

// 显示变量弹窗
const showVariableDialog = () => {
  resetVariableForm();
  variableDialogVisible.value = true;
};

// 重置变量表单
const resetVariableForm = () => {
  variableForm.value = {
    name: '',
    displayName: '',
    maxLength: 48,
    required: false,
    options: []
  };
  selectedVariableType.value = 'text';
  showOptionInput.value = false;
};

// 保存变量
const saveVariable = () => {
  // 验证变量名称是否是英文开头
  const nameRegex = /^[a-zA-Z]/;
  if (!nameRegex.test(variableForm.value.name)) {
    ElMessage.warning('变量名称必须以英文字母开头');
    return;
  }

  // 验证必填项
  if (!variableForm.value.name.trim()) {
    ElMessage.warning('请输入变量名称');
    return;
  }

  if (!variableForm.value.displayName.trim()) {
    ElMessage.warning('请输入显示名称');
    return;
  }

  // 验证最大长度是否为数字
  if (variableForm.value.maxLength && isNaN(Number(variableForm.value.maxLength))) {
    ElMessage.warning('最大长度必须是数字');
    return;
  }

  // 如果是下拉类型，验证至少有一个选项
  if (selectedVariableType.value === 'dropdown' && variableForm.value.options.length === 0) {
    ElMessage.warning('下拉类型至少需要添加一个选项');
    return;
  }

  // 构建变量对象
  const variable = {
    name: variableForm.value.name.trim(),
    displayName: variableForm.value.displayName.trim(),
    type: selectedVariableType.value,
    maxLength: Number(variableForm.value.maxLength) || 48,
    required: variableForm.value.required || false,
    options: selectedVariableType.value === 'dropdown' ? [...variableForm.value.options] : []
  };

  if (editingVariableIndex.value >= 0) {
    // 编辑模式：更新已有变量
    variables.value[editingVariableIndex.value] = variable;
    ElMessage.success('变量更新成功');
  } else {
    // 添加模式：检查变量名是否已存在
    const existingVar = variables.value.find(v => v.name === variable.name);
    if (existingVar) {
      ElMessage.warning(`变量名 "${variable.name}" 已存在`);
      return;
    }

    // 添加新变量
    variables.value.push(variable);

    // 初始化变量值
    if (variable.type === 'number') {
      variableValues.value[variable.name] = "";
    } else {
      variableValues.value[variable.name] = '';
    }

    ElMessage.success('变量添加成功');
  }

  // 关闭弹窗，重置编辑状态
  variableDialogVisible.value = false;
  editingVariableIndex.value = -1;
};

// 取消添加变量
const cancelVariable = () => {
  variableDialogVisible.value = false;
};

// 变量列表
const variables = ref<any[]>([]);

// 变量表单值
const variableValues = ref<Record<string, any>>({});

// 获取变量类型标签
const getVariableTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'text': '文本',
    'paragraph': '段落',
    'dropdown': '下拉',
    'number': '数字'
  };
  return typeMap[type] || type;
};

// 编辑变量
const editVariable = (index: number) => {
  const variable = variables.value[index];

  // 填充表单数据
  variableForm.value = { ...variable };
  selectedVariableType.value = variable.type;

  // 显示弹窗
  showOptionInput.value = false;
  variableDialogVisible.value = true;

  // 标记为编辑模式
  editingVariableIndex.value = index;
};

// 删除变量
const deleteVariable = (index: number) => {
  ElMessageBox.confirm(
    '确认删除此变量吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 删除变量
    const deletedVariable = variables.value.splice(index, 1)[0];

    // 删除对应的表单值
    if (deletedVariable && deletedVariable.name) {
      delete variableValues.value[deletedVariable.name];
    }

    ElMessage.success('变量已删除');
  }).catch(() => {
    // 取消删除
  });
};

// 编辑变量索引
const editingVariableIndex = ref(-1); // -1表示新增模式，≥0表示编辑模式的索引

// 知识库相关
const datasetDialogVisible = ref(false);
const datasetList = ref<(Dataset & { selected?: boolean })[]>([]);
const datasetLoading = ref(false);
const selectedDatasets = ref<Dataset[]>([]);

// 打开知识库选择弹窗
const openDatasetDialog = async () => {
  datasetDialogVisible.value = true;
  await loadDatasets();
};

// 加载知识库列表
const loadDatasets = async () => {
  datasetLoading.value = true;
  try {
    const response = await fetchDatasetList({ page: 1 });
    // 处理数据，添加选中状态
    datasetList.value = response.data.map(dataset => ({
      ...dataset,
      selected: selectedDatasets.value.some(item => item.id === dataset.id)
    }));
  } catch (error) {
    console.error('获取知识库列表失败:', error);
    ElMessage.error('加载知识库列表失败');
  } finally {
    datasetLoading.value = false;
  }
};

// 处理知识库选择
const handleDatasetSelect = (dataset: Dataset & { selected?: boolean }, val: boolean) => {
  if (dataset) {
    dataset.selected = val;
  }
};

// 确认选择知识库
const confirmSelectDatasets = () => {
  const newSelectedDatasets = datasetList.value
    .filter(dataset => dataset.selected)
    .map(({ selected, ...rest }) => rest as Dataset);

  selectedDatasets.value = newSelectedDatasets;
  closeDatasetDialog();
};

// 关闭知识库选择弹窗
const closeDatasetDialog = () => {
  datasetDialogVisible.value = false;
};

// 移除已选知识库
const removeDataset = (id: string) => {
  selectedDatasets.value = selectedDatasets.value.filter(dataset => dataset.id !== id);
};

const currentAgentId = ref('');
const siteInfo = ref({});
onMounted(async () => {
  // 检查URL中是否存在agentId参数
  const agentId = router.currentRoute.value.query.agentId as string;

  if (agentId) {
    // 如果存在agentId参数，则直接跳转到第三步（高级配置）
    currentStep.value = 3;
    currentAgentId.value = agentId;

    // 根据agentId获取智能体信息并填充到表单中
    await agentApi.getAgentDetail(agentId).then(async (response) => {
      console.log('智能体详情:', response);
      siteInfo.value = response.site;
      // 填充基础表单数据
      basicForm.value.name = response.name;
      basicForm.value.description = response.description;
      basicForm.value.mode = response.mode;
      basicForm.value.icon = response.icon;

      // 填充图标URL
      if (response.icon_url) {
        basicForm.value.iconUrl = import.meta.env.VITE_API_BASE_URL + response.icon_url;
      }

      // 填充高级表单设置
      if (response.model_config) {
        const config = response.model_config;
        console.log('配置详情:', config);

        // 回显开场白设置
        if (config.opening_statement) {
          advancedForm.value.opening.enabled = true;
          advancedForm.value.opening.opening_statement = config.opening_statement;
        }

        // 回显推荐问题设置
        if (config.suggested_questions && config.suggested_questions.length > 0) {
          advancedForm.value.suggestedQuestions.enabled = true;
          advancedForm.value.suggestedQuestions.items = config.suggested_questions;
        }

        // 回显推荐问题答案后设置
        if (config.suggested_questions_after_answer) {
          advancedForm.value.suggestedQuestions.enabled = config.suggested_questions_after_answer.enabled;
        }

        // 回显注释回复设置
        if (config.annotation_reply) {
          advancedForm.value.citation.enabled = config.annotation_reply.enabled;
        }

        // 回显敏感词回避设置
        if (config.sensitive_word_avoidance) {
          advancedForm.value.moderation.enabled = config.sensitive_word_avoidance.enabled;

          if (config.sensitive_word_avoidance.type) {
            moderationType.value = config.sensitive_word_avoidance.type;
          }

          // 如果有关键词配置，回显关键词
          if (config.sensitive_word_avoidance.config) {
            // 假设关键词是数组，转为换行分割的字符串
            const keywords = config.sensitive_word_avoidance.config.keywords;
            if (Array.isArray(keywords)) {
              advancedForm.value.moderation.keywords = keywords.join('\n');
            } else if (typeof keywords === 'string') {
              advancedForm.value.moderation.keywords = keywords;
            }
          }
           // 初始化输入输出审查开关和内容
            if (config.sensitive_word_avoidance?.config?.inputs_config) {
              advancedForm.value.moderation.input.enabled = config.sensitive_word_avoidance?.config?.inputs_config?.enabled || false;
              advancedForm.value.moderation.input.response = config.sensitive_word_avoidance?.config?.inputs_config?.preset_response || '';
              // updateInputResponseCount();
            }

            if (config.sensitive_word_avoidance?.config?.outputs_config) {
              advancedForm.value.moderation.output.enabled = config.sensitive_word_avoidance?.config?.outputs_config?.enabled || false;
              advancedForm.value.moderation.output.response = config.sensitive_word_avoidance?.config?.outputs_config?.preset_response || '';
              // updateOutputResponseCount();
            }
          }

        // 回显角色提示词
        if (config.pre_prompt) {
          advancedForm.value.rolePrompt = config.pre_prompt;
        }

        //回显变量
        if (config.user_input_form && Array.isArray(config.user_input_form)) {
          // 清空现有变量列表
          variables.value = [];
          variableValues.value = {};

          // 遍历API返回的表单配置并转换为前端变量格式
          config.user_input_form.forEach((item: any) => {
            if (item['text-input']) {
              const inputConfig = item['text-input'];
              variables.value.push({
                name: inputConfig.variable,
                displayName: inputConfig.label,
                type: 'text',
                maxLength: inputConfig.max_length || '',
                required: inputConfig.required || false,
                options: []
              });
              variableValues.value[inputConfig.variable] = inputConfig.default || '';
            }
            else if (item['paragraph']) {
              const paragraphConfig = item['paragraph'];
              variables.value.push({
                name: paragraphConfig.variable,
                displayName: paragraphConfig.label,
                type: 'paragraph',
                maxLength: paragraphConfig.max_length || '',
                required: paragraphConfig.required || false,
                options: []
              });
              variableValues.value[paragraphConfig.variable] = paragraphConfig.default || '';
            }
            else if (item['select']) {
              const selectConfig = item['select'];
              variables.value.push({
                name: selectConfig.variable,
                displayName: selectConfig.label,
                type: 'dropdown',
                required: selectConfig.required || false,
                options: selectConfig.options || []
              });
              variableValues.value[selectConfig.variable] = selectConfig.default || '';
            }
            else if (item['number']) {
              const numberConfig = item['number'];
              variables.value.push({
                name: numberConfig.variable,
                displayName: numberConfig.label,
                type: 'number',
                required: numberConfig.required || false
              });
              variableValues.value[numberConfig.variable] = numberConfig.default || '';
            }
          });

          console.log('变量配置回显完成:', variables.value);
        }

        // 回显智能体模式设置
        // if (config.agent_mode) {
        //   advancedForm.value.agentMode.enabled = config.agent_mode.enabled;
        //   if (config.agent_mode.strategy) {
        //     advancedForm.value.agentMode.strategy = config.agent_mode.strategy;
        //   }
        //   if (config.agent_mode.max_iteration) {
        //     advancedForm.value.agentMode.maxIteration = config.agent_mode.max_iteration;
        //   }
        // }

        // 回显模型配置
        if (config.model) {
          // 这里需要根据表单中实际的模型字段进行设置
          if (config.model.provider) {
            advancedForm.value.model = config.model.provider;
          }
          if (config.model.name) {
            (advancedForm.value as any).modelName = config.model.name;
          }
        }
      }

      // 处理知识库配置回显
    if (response.model_config?.dataset_configs?.datasets?.datasets &&
        Array.isArray(response.model_config.dataset_configs.datasets.datasets)) {

      const datasetIds = response.model_config.dataset_configs.datasets.datasets
        .filter(item => item.dataset && item.dataset.enabled)
        .map(item => item.dataset.id);

        console.log("datasetIdsdatasetIdsdatasetIdsdatasetIds0", datasetIds);
      if (datasetIds.length > 0) {
        // 如果有关联的知识库ID，加载知识库详情
        try {
          datasetLoading.value = true;
          // 先获取知识库列表数据
          const response = await fetchDatasetList({ page: 1 });
          // 筛选出已关联的知识库
          selectedDatasets.value = response.data.filter(dataset =>
            datasetIds.includes(dataset.id)
          );
          console.log('已关联的知识库:', selectedDatasets.value);
        } catch (error) {
          console.error('获取知识库详情失败:', error);
          ElMessage.error('获取知识库详情失败');
        } finally {
          datasetLoading.value = false;
        }
      }
    }
      ElMessage.success('智能体详情加载成功');
    }).catch(error => {
      console.error('获取智能体详情失败:', error);
      ElMessage.error('获取智能体详情失败，请重试');
    });

    // 获取文本嵌入模型列表和默认文本嵌入模型
    fetchTextEmbeddingModelListData();
    fetchDefaultTextEmbeddingModelData();

  } else {
    // 初始化时自动选择聊天助手类型
    selectChatAssistant();
  }

  // 初始化预览消息
  clearPreviewMessages();
});
</script>

<style scoped>
.create-agent-container {
  padding: 20px;
  background-color: #f6f8fa;
  min-height: calc(100vh - 40px);
}

.create-agent-container .header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.create-agent-container .header .back {
  cursor: pointer;
  font-size: 20px;
  margin-right: 10px;
}

.create-agent-container .header .back:hover {
  color: #409eff;
}

.create-agent-container .header .title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
}

.create-agent-container .steps-container {
  margin-bottom: 40px;
  max-width: 1600px;
  margin-left: auto;
  margin-right: auto;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
}

.create-agent-container .step-content {
  max-width: 1600px;
  margin: 0 auto;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
}

.create-agent-container .step-content .type-selection {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.create-agent-container .step-content .type-selection .type-card {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.create-agent-container .step-content .type-selection .type-card:hover {
  border-color: #409eff;
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.create-agent-container .step-content .type-selection .type-card.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.create-agent-container .step-content .type-selection .type-card.active::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  border-style: solid;
  border-width: 0 30px 30px 0;
  border-color: transparent #409eff transparent transparent;
}

.create-agent-container .step-content .type-selection .type-card .icon-container {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  font-size: 24px;
  color: #409eff;
}

.create-agent-container .step-content .type-selection .type-card .type-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
}

.create-agent-container .step-content .type-selection .type-card .type-desc {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
}

.create-agent-container .step-content .basic-config {
  margin-bottom: 30px;
}

.create-agent-container .step-content .basic-config .icon-upload-container {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  position: relative;
}

.create-agent-container .step-content .basic-config .icon-upload-container .icon-upload {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  overflow: hidden;
  background-color: #fafafa;
}

.create-agent-container .step-content .basic-config .icon-upload-container .icon-upload:hover {
  border-color: #409eff;
}

.create-agent-container .step-content .basic-config .icon-upload-container .icon-upload .upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.create-agent-container .step-content .basic-config .icon-upload-container .icon-upload .upload-placeholder .upload-icon {
  font-size: 28px;
  margin-bottom: 8px;
  color: #909399;
}

.create-agent-container .step-content .basic-config .icon-upload-container .icon-upload .upload-placeholder .upload-text {
  font-size: 14px;
  color: #909399;
}

.create-agent-container .step-content .basic-config .icon-upload-container .icon-upload .preview-uploaded {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.create-agent-container .step-content .basic-config .icon-upload-container .icon-upload .preview-uploaded:hover .upload-hover-mask {
  opacity: 1;
}

.create-agent-container .step-content .basic-config .icon-upload-container .icon-upload .preview-uploaded .uploaded-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.create-agent-container .step-content .basic-config .icon-upload-container .icon-upload .preview-uploaded .upload-hover-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: all 0.3s;
  color: white;
  font-size: 24px;
}

.create-agent-container .step-content .basic-config .icon-upload-container .upload-progress {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.create-agent-container .step-content .basic-config .form-container {
  max-width: 600px;
  margin: 0 auto;
}

.create-agent-container .step-content .basic-config .form-container .form-item {
  margin-bottom: 20px;
}

.create-agent-container .step-content .basic-config .form-container .form-item .form-label {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 10px;
}

.create-agent-container .step-content .step-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  gap: 12px;
}

.create-agent-container .step-content.advanced-step {
  max-width: 1600px;
}

.create-agent-container .step-content.advanced-step .advanced-config {
  display: flex;
  gap: 20px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-left {
  flex: 2;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-left .config-section {
  margin-bottom: 24px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-left .config-section .section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-left .config-section .toggle-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-left .config-section .toggle-item.with-description {
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 16px;
}


.create-agent-container .step-content.advanced-step .advanced-config .advanced-left .config-section .toggle-item.with-description .toggle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 4px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-left .config-section .toggle-item.with-description .toggle-description {
  font-size: 13px;
  color: #909399;
  margin-left: 0;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-left .config-section .hint-text {
  font-size: 12px;
  color: #909399;
  margin: 6px 0 12px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-left .config-section .add-question-btn {
  margin-bottom: 12px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-left .config-section .questions-list {
  margin-top: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-left .config-section .questions-list .question-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  border-bottom: 1px solid #e4e7ed;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-left .config-section .questions-list .question-item:last-child {
  border-bottom: none;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-left .config-section .questions-list .question-item .el-icon {
  color: #909399;
  cursor: pointer;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-left .config-section .questions-list .question-item .el-icon:hover {
  color: #f56c6c;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-left .config-section .empty-section {
  color: #909399;
  font-size: 14px;
  text-align: center;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.annotation-reply-score {
  margin-top: 12px;
  padding: 8px;
  background-color: #f7f8fa;
  border-radius: 6px;
  width: 100%;
}

.score-labels {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
  color: #606266;
  width: 100%;
}

.score-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  text-align: center;
  width: 100%;
}

.annotation-settings {
  width: 100%;
  margin-top: 8px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-left .config-section .empty-question-notice {
  color: #909399;
  font-size: 14px;
  text-align: center;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-top: 10px;
}

/* 推荐问题样式 */
.suggested-questions-container {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.suggested-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.suggested-questions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggested-question-item {
  padding: 8px 12px;
  border-radius: 8px;
  background-color: #ecf5ff;
  color: #409eff;
  cursor: pointer;
}

.suggested-question-item:hover {
  background-color: #d9ecff;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right {
  flex: 2;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug {
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f5f7fa;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .preview-header span {
  font-weight: 500;
  font-size: 14px;
  text-transform: uppercase;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .preview-header .actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .preview-header .actions .divider {
  width: 1px;
  height: 14px;
  background-color: #dcdfe6;
  margin: 0 4px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .preview-header .actions .el-button.is-active {
  background-color: #ecf5ff;
  color: #409eff;
}

/* 聊天预览区域 */
.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview {
  display: flex;
  flex-direction: column;
  height: calc(100% - 50px);
  overflow: hidden;
  position: relative;
}

.chat-area {
  display: block !important;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview .message-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 76px; /* 输入框高度 + padding */
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .message-item {
  margin-bottom: 16px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .message-item .question,
.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .message-item .answer {
  margin-bottom: 16px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .message-content {
  display: flex;
  /* align-items: start; */
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview .message-item .question .message-content {
  display: flex;
  /* align-items: start; */
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview .message-item .question .message-content .avatar {
  width: 32px;
  height: 32px;
  margin-right: 12px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #67c23a;
  color: white;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview .message-item .answer .message-content {
  display: flex;
  align-items: start;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview .message-item .answer .message-content .avatar {
  width: 32px;
  height: 32px;
  margin-right: 12px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview .message-item .answer .message-content .content {
  flex: 1;
  overflow: hidden;
  /* text-overflow: ellipsis; */
  /* white-space: nowrap; */
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .input-area {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview .input-area .input-box {
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview .input-area .el-input {
  flex: 1;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview .input-area .send-btn {
  margin-left: 8px;
}

/* 空状态 */
.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  text-align: center;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .empty-state .empty-icon {
  font-size: 36px;
  margin-bottom: 12px;
  color: #c0c4cc;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .empty-state .empty-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .empty-state .empty-desc {
  font-size: 14px;
  color: #909399;
}

/* 加载动画 */
.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview .message-item .answer .message-content .content .loading-dots {
  font-size: 14px;
  color: #909399;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview .message-item .answer .message-content .content .loading-dots span {
  animation: loading-dots 1s infinite;
}

@keyframes loading-dots {
  0% {
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview .message-item .answer .message-content .content .reset-content {
  line-height: 1.6;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview .message-item .answer .message-content .content .reset-content pre {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 12px;
  overflow-x: auto;
  margin: 10px 0;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview .message-item .answer .message-content .content .reset-content code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 3px;
  background-color: rgba(175, 184, 193, 0.2);
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview .message-item .answer .message-content .content .reset-content pre code {
  background-color: transparent;
  padding: 0;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .actions {
  display: flex;
  align-items: center;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .actions .divider {
  width: 1px;
  height: 16px;
  background-color: #dcdfe6;
  margin: 0 8px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .actions .is-active {
  color: #409EFF;
  background-color: #ecf5ff;
}

/* 空状态样式 */
.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .empty-state .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #DCDFE6;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .empty-state .empty-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .empty-state .empty-desc {
  font-size: 14px;
  color: #909399;
}

/* 过渡动画 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

/* 开场白样式 */
.opening-statement-container {
  margin-top: 8px;
  margin-bottom: 15px;
}

.save-button-container {
  margin-top: 8px;
  display: flex;
  justify-content: flex-end;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-left .config-section .empty-question-notice {
  color: #909399;
  font-size: 14px;
  text-align: center;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-top: 10px;
}

/* 推荐问题样式 */
.suggested-questions-container {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.suggested-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.suggested-questions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggested-question-item {
  padding: 8px 12px;
  border-radius: 8px;
  background-color: #ecf5ff;
  color: #409eff;
  cursor: pointer;
}

.suggested-question-item:hover {
  background-color: #d9ecff;
}

.annotation-reply-score {
  margin-top: 8px;
}

.score-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>
<style lang="scss" scoped>
.completion-step {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.completion-container {
  text-align: center;
  max-width: 600px;
  width: 100%;
}

.success-icon {
  margin-bottom: 24px;
}

.completion-title {
  font-size: 24px;
  color: #67C23A;
  margin-bottom: 8px;
}

.completion-subtitle {
  font-size: 16px;
  color: #606266;
  margin-bottom: 32px;
}

.agent-preview {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 32px;
}

.preview-card {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.preview-icon {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f2f5;
  font-size: 32px;
}

.default-icon {
  font-size: 32px;
}

.preview-icon-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.preview-content {
  flex: 1;
  text-align: left;
}

.preview-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
}

.preview-desc {
  font-size: 14px;
  color: #606266;
}

.completion-actions {
  margin-top: 24px;
}
</style>
<style lang="scss" scoped>
.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug {
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f5f7fa;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-area {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.chat-area {
  display: block !important;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .message-list {
  overflow-y: auto;
  padding: 16px;
  height: calc(100% - 106px);
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .message-item {
  margin-bottom: 20px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .message-item .question,
.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .message-item .answer {
  margin-bottom: 16px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .message-content {
  display: flex;
  /* align-items: start; */
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .chat-preview .message-item .question .message-content {
  display: flex;
  /* align-items: start; */
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f0f2f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .robot-icon {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .content {
  background-color: #f0f2f5;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.5;
  width: 100%;
  white-space: pre-wrap;
  word-break: break-word;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .answer .content {
  background-color: #ecf5ff;
  border-radius: 8px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .input-area {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .input-box {
  position: relative;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .send-btn {
  position: absolute;
  right: 10px;
  bottom: 10px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .input-fields-panel {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  background-color: white;
  z-index: 10;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .panel-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 500;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .panel-content {
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .loading-dots span {
  animation: loading-dots 1.4s infinite ease-in-out;
  display: inline-block;
}

@keyframes loading-dots {
  0% {
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .reset-content {
  line-height: 1.6;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .reset-content pre {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 12px;
  overflow-x: auto;
  margin: 10px 0;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .reset-content code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 3px;
  background-color: rgba(175, 184, 193, 0.2);
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .reset-content pre code {
  background-color: transparent;
  padding: 0;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .actions {
  display: flex;
  align-items: center;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .actions .divider {
  width: 1px;
  height: 16px;
  background-color: #dcdfe6;
  margin: 0 8px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .actions .is-active {
  color: #409EFF;
  background-color: #ecf5ff;
}

/* 空状态样式 */
.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .empty-state .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #DCDFE6;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .empty-state .empty-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.create-agent-container .step-content.advanced-step .advanced-config .advanced-right .preview-debug .empty-state .empty-desc {
  font-size: 14px;
  color: #909399;
}

/* 过渡动画 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}</style>
<style lang="scss" scoped>
.moderation-dialog-content {
  padding: 10px 0;
}

.dialog-section {
  margin-bottom: 20px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 10px;
  font-size: 14px;
}

.radio-options {
  display: flex;
  gap: 15px;
}

.keyword-input-container {
  position: relative;
}

.keyword-count {
  position: absolute;
  right: 10px;
  bottom: 10px;
  font-size: 12px;
  color: #909399;
}

:deep(.el-dialog__body) {
  padding: 20px 20px;
}

:deep(.el-radio.is-bordered) {
  border-color: #dcdfe6;
  height: 36px;
  padding: 0 15px 0 10px;
}

:deep(.el-radio.is-disabled.is-bordered) {
  border-color: #e4e7ed;
  background-color: #f5f7fa;
  color: #a8abb2;
}

:deep(.el-textarea__inner) {
  font-family: monospace;
}
</style>
<style lang="scss" scoped>
.variable-type-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.variable-type-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 80px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;

  &.active {
    border-color: #409EFF;
    background-color: #ecf5ff;
    color: #409EFF;
  }

  .icon {
    font-size: 24px;
    margin-bottom: 5px;
  }
}

.options-container {
  margin-top: 10px;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.add-option-input {
  display: flex;
  align-items: center;
  margin-top: 10px;
  gap: 5px;
}

.add-option-btn {
  margin-top: 10px;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 10px;
  margin-bottom: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
</style>
<style lang="scss" scoped>
.variables-list {
  margin-top: 10px;
}

.variable-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 8px;
}

.variable-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.variable-type-tag {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: #e6f1fc;
  color: #409eff;
}

.variable-type-tag.type-text {
  background-color: #e6f1fc;
  color: #409eff;
}

.variable-type-tag.type-paragraph {
  background-color: #e6f7eb;
  color: #67c23a;
}

.variable-type-tag.type-dropdown {
  background-color: #f0f5ff;
  color: #4b6bde;
}

.variable-type-tag.type-number {
  background-color: #fef6e7;
  color: #e6a23c;
}

.variable-actions {
  display: flex;
  gap: 5px;
}

.variables-preview {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.variable-preview-item {
  margin-bottom: 12px;
}

.variable-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
}

.chat-preview.with-variables {
  margin-top: 16px;
}
</style>
<style lang="scss" scoped>
.toggle-item .toggle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.toggle-item .toggle-header .action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-item .toggle-header .action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  height: auto;
}
</style>
<style lang="scss" scoped>
/* 知识库列表样式 */
.dataset-list {
  margin-top: 10px;
}

.dataset-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 8px;
}

.dataset-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dataset-name {
  font-size: 14px;
}

.dataset-actions {
  display: flex;
  gap: 5px;
}

/* 知识库选择弹窗样式 */
.dataset-selection {
  max-height: 400px;
  overflow-y: auto;
}

.dataset-selection-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #EBEEF5;
}

.dataset-icon {
  display: inline-flex;
  margin-right: 8px;
  color: #909399;
}

.dataset-selection-name {
  font-size: 14px;
}

.dataset-tags {
  display: flex;
  gap: 8px;
}

.empty-dataset-list {
  padding: 40px 0;
}

.dataset-loading {
  padding: 20px 0;
}

.type-icon {
  width: 40px;
  height: 40px;
  object-fit: contain;
  border-radius: 0; /* 覆盖可能的圆角设置 */
}

/* 嵌入网站弹窗样式 */
.embed-dialog-content {
  padding: 10px;
}

.embed-dialog-content p {
  margin-bottom: 20px;
  font-size: 16px;
}

.embed-options {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.embed-option {
  width: 30%;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.embed-option:hover {
  border-color: #409EFF;
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.embed-option.active {
  border-color: #409EFF;
  background-color: rgba(64, 158, 255, 0.1);
}

.option-image {
  width: 100%;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.option-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.embed-code-container {
  margin-top: 20px;
}

.code-box {
  position: relative;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
}

.code-box pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  max-height: 200px;
  overflow-y: auto;
}

.copy-btn {
  position: absolute;
  top: 10px;
  right: 10px;
}
</style>