<template>
  <div class="sidebar" :class="{ collapsed: isCollapsed, hidden: !hasSubMenus }" v-if="hasSubMenus">
    <div class="sidebar-content">
      <div class="menu-list">
        <router-link
          v-for="menu in subMenus"
          :key="menu.path"
          :to="menu.path"
          class="menu-item"
          :class="{ active: route.path === menu.path }"
          :title="isCollapsed ? menu.title : ''"
        >
          <template v-if="menu.url">
            <img class="menu-img" :src="menu.activeUrl" alt="" v-if="route.path === menu.path" />
            <img class="menu-img" :src="menu.url" alt="" v-else />
          </template>
          <el-icon v-else><component :is="menu.icon" /></el-icon>
          <span class="menu-title" v-show="!isCollapsed">{{ menu.title }}</span>
        </router-link>
      </div>

      <!-- 知识空间列表 -->
      <div class="knowledge-space-container" v-if="showKnowledgeSpace && !isCollapsed">
        <SpaceList />
      </div>
    </div>

    <div class="collapse-btn" @click="toggleCollapse">
      <el-icon>
        <component :is="collapseIcon" />
      </el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, markRaw, shallowRef } from "vue";
import { useRoute } from "vue-router";
import { ArrowRight, ArrowLeft } from "@element-plus/icons-vue";
import type { SubMenu } from "@/types/menu";
import SpaceList from "@/components/knowledge/SpaceList.vue";

defineOptions({
  name: "Sidebar",
});

// Props 定义
const props = defineProps<{
  subMenus: SubMenu[];
}>();

const route = useRoute();
const isCollapsed = ref(false);

// 使用 shallowRef 存储图标组件
const rightArrow = shallowRef(markRaw(ArrowRight));
const leftArrow = shallowRef(markRaw(ArrowLeft));

// 计算属性：是否有子菜单
const hasSubMenus = computed(() => {
  return props.subMenus && props.subMenus.length > 0;
});

// 子菜单数据
const subMenus = computed(() => {
  return props.subMenus || [];
});

// 处理折叠按钮图标
const collapseIcon = computed(() => {
  return isCollapsed.value ? rightArrow.value : leftArrow.value;
});

// 是否显示知识空间列表
const showKnowledgeSpace = computed(() => {
  return route.path.startsWith("/knowledge");
});

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};
</script>

<style scoped lang="scss">
.sidebar {
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #ebeef5;
  position: relative;
  transition: all 0.3s ease;
  &.hidden {
    display: none;
  }
  &.collapsed {
    width: 100px;
    .create-btn {
      padding: 0;
      justify-content: center;
      .el-icon {
        margin: 0;
      }
    }
    .menu-item {
      width: 50px !important;
      .el-icon {
        margin: 0;
      }
    }
  }

  .sidebar-content {
    padding: 32px;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  .create-btn {
    height: 40px;
    border-radius: 8px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background-color: #4080ff;
    margin-bottom: 8px;
    &:hover {
      background-color: #2e6be6;
    }
  }

  .menu-list {
    display: flex;
    flex-direction: column;
    .menu-item {
      width: 155px;
      padding: 12px 16px;
      border-radius: 3px;
      display: flex;
      align-items: center;
      text-decoration: none;
      margin-bottom: 16px;
      &:last-child {
        margin-bottom: 0;
      }
      .el-icon {
        font-size: 16px;
        flex-shrink: 0;
      }
      .menu-img {
        width: 20px;
        height: 20px;
        overflow: hidden;
      }
      .menu-title {
        font-weight: 400;
        font-size: 14px;
        color: #1a1a1a;
        line-height: 16px;
        white-space: nowrap;
        margin-left: 8px;
      }
      &:hover {
        background-color: #f1f4ff;
      }
      &.active {
        background-color: #f1f4ff;
        .menu-title {
          font-weight: 600;
          color: #3a67f8;
        }
      }
    }
  }

  .knowledge-space-container {
    margin-top: 24px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: calc(100% - 200px);
  }

  .collapse-btn {
    position: absolute;
    top: 50%;
    right: -12px;
    width: 24px;
    height: 24px;
    transform: translateY(-50%);
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1;
    transition: all 0.3s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);

    &:hover {
      background-color: #f2f3f5;
    }

    .el-icon {
      font-size: 14px;
      color: #909399;
    }
  }
}
</style>
