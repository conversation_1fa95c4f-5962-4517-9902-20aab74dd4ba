import { get, post, del, patch } from '@/utils/request';

// 文档成员相关接口类型定义
export interface DocumentMember {
  id: string;
  account_id: string;
  name: string;
  avatar_url?: string;
  role: 'admin' | 'member';
  created_at: number;
  updated_at: number;
  [key: string]: any;
}

export interface DocumentMembersResponse {
  members: DocumentMember[];
}

/**
 * 获取文档成员列表
 * @param documentId 文档ID
 * @returns 成员列表
 */
export const getDocumentMembers = (documentId: string): Promise<DocumentMembersResponse> => {
  return get(`/documents/${documentId}/members`);
};

/**
 * 添加文档成员
 * @param documentId 文档ID
 * @param accountId 成员ID
 * @param role 角色
 * @returns 添加结果
 */
export const addDocumentMembers = (documentId: string, accountId: string, role: string): Promise<any> => {
  return post(`/documents/${documentId}/members`, { account_id: accountId, role });
};

/**
 * 更新成员角色
 * @param documentId 文档ID
 * @param accountId 成员ID
 * @param role 角色
 * @returns 更新结果
 */
export const updateDocumentMemberRole = (documentId: string, accountId: string, role: string): Promise<any> => {
  return patch(`/documents/${documentId}/members/${accountId}`, { role });
};

/**
 * 删除文档成员
 * @param documentId 文档ID
 * @param accountId 成员ID
 * @returns 删除结果
 */
export const removeDocumentMember = (documentId: string, accountId: string): Promise<any> => {
  return del(`/documents/${documentId}/members/${accountId}`);
};

/**
 * 获取工作空间成员列表
 * @returns 工作空间成员列表
 */
export const fetchWorkspaceMembers = (): Promise<any> => {
  return get('/workspaces/current/members');
};
