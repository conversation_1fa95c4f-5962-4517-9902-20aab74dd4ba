<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <img class="login-logo" src="@/assets/images/logo.png" alt="logo" />
        <div class="login-title">LeAgent乐睿智能体平台</div>
      </div>
      <div class="login-content">
        <div class="login-wrapper">
          <div class="login-welcome">
            <div class="welcome-title">加入 {{ workspace_name }}</div>
            <div class="welcome-text">邀请你加入 {{ workspace_name }} 团队</div>
          </div>
          <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form">
            <el-form-item prop="email">
              <el-input
                v-model="loginForm.email"
                placeholder="输入邮箱"
                :prefix-icon="User"
                class="custom-input"
                disabled
              />
            </el-form-item>
            <el-form-item prop="name">
              <el-input v-model="loginForm.name" placeholder="输入名称" :prefix-icon="PriceTag" class="custom-input" />
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="输入密码"
                :prefix-icon="Lock"
                show-password
                class="custom-input"
                @keyup.enter="handleLogin"
              />
            </el-form-item>
            <div class="form-footer">
              <el-button
                type="primary"
                class="login-button"
                @click="handleLogin"
                :disabled="!(loginForm.email && loginForm.password && loginForm.name)"
                :loading="loading"
              >
                登录
              </el-button>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onUnmounted, onMounted } from "vue";
import { User, Lock, PriceTag } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { debounce } from "lodash-es";
import { post, get } from "@/utils/request";
import { setUser, setToken, setRefreshToken } from "@/utils/user";
import type { LoginForm, LoginResponse, LoginRequestData } from "@/types/auth";

const router = useRouter();
const route = useRoute();
const loginFormRef = ref();
const loading = ref(false);
const loginForm = reactive<LoginForm>({
  email: "",
  password: "",
  token: "",
  name: "",
  interface_language: "zh-Hans",
  timezone: "Asia/Shanghai",
});

const workspace_name = ref("");

// 定义激活检查响应类型
interface ActivationCheckResponse {
  organization_name?: string;
  status?: string;
  [key: string]: any;
}

// 检查激活状态
const checkActivation = async () => {
  try {
    // 从URL获取参数
    const token = route.query.token as string;
    loginForm.token = token;
    // 发送请求到/activate/check接口
    const response = await get<ActivationCheckResponse>("/activate/check", {
      token,
    });
    loginForm.email = response.data.email;
    workspace_name.value = response.data.workspace_name;
  } catch (error) {}
};

const loginRules = {
  email: [{ required: true, message: "请输入邮箱", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
};

const doLogin = async () => {
  if (!loginFormRef.value) return;

  await loginFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true;
      try {
        const requestData: LoginRequestData = {
          language: "zh-Hans",
          remember_me: true,
          email: loginForm.email,
          password: loginForm.password,
          invite_token: loginForm.token,
        };
        await post<LoginResponse>("/login", requestData);
        let resp = await post<LoginResponse>("/activate", {
          name: loginForm.name,
          token: loginForm.token,
          interface_language: loginForm.interface_language,
          timezone: loginForm.timezone,
        });
        setToken(resp.data.access_token);
        setRefreshToken(resp.data.refresh_token);
        const rsp = await get("/account/profile");
        setUser(rsp);
        loading.value = false;
        ElMessage.success("登录成功");
        router.push("/");
      } catch (error) {
        loading.value = false;
      }
    }
  });
};

// 使用防抖包装登录方法
const handleLogin = debounce(doLogin, 500, {
  leading: true, // 第一次调用立即执行
  trailing: false, // 不执行最后一次调用
});

// 组件挂载时检查激活状态
onMounted(() => {
  checkActivation();
});

// 组件卸载时取消未执行的防抖函数
onUnmounted(() => {
  handleLogin.cancel();
});
</script>

<style scoped lang="scss">
.login-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: url("@/assets/images/background.png");
  background-size: cover;
  padding: 32px;
}

.login-box {
  flex: 1;
  overflow: hidden;
  padding: 20px;
  background-color: #fff;
  border-radius: 32px;
  box-shadow: 0 0 #0000, 0 0 #0000, 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.login-header {
  display: flex;
  align-items: center;
  .login-logo {
    width: 48px;
    height: 48px;
    margin-right: 16px;
  }
  .login-title {
    font-size: 24px;
    font-weight: 600;
  }
}

.login-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  .login-wrapper {
    width: 400px;
    margin: auto;
  }
}

.login-form {
  :deep(.custom-input) {
    --el-input-bg-color: #f3f4f6;
    --el-input-border-color: transparent;
    --el-input-hover-border-color: var(--el-color-primary);
    --el-input-focus-border-color: var(--el-color-primary);
    --el-input-height: 42px;
    border-radius: 8px;

    .el-input__wrapper {
      padding: 0 16px;
      border-radius: 8px;
      box-shadow: none;
    }

    .el-input__inner {
      color: #1f2a37;
      &::placeholder {
        color: #9ca3af;
      }
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 16px;
  }
}

.form-options {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 24px;

  .forget-link {
    color: #3b82f6;
    font-size: 14px;
  }
}

.form-footer {
  margin-bottom: 24px;

  .login-button {
    width: 100%;
    height: 42px;
    border-radius: 8px;
    font-weight: 500;
  }
}

.login-welcome {
  margin-bottom: 24px;
  .welcome-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
  }
  .welcome-text {
    font-size: 14px;
    color: #6b7280;
  }
}
</style>
