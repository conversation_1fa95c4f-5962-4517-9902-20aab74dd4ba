<template>
  <div class="knowledge-container">
    <div class="knowledge-header">
      <div class="knowledge-title">{{ pageTitle }}</div>
      <el-button type="primary" class="create-btn" @click="handleCreateKnowledge">
        <el-icon><Plus /></el-icon>
        创建知识库
      </el-button>
    </div>
    
    <div class="knowledge-search">
      <el-input
        v-model="keyword"
        placeholder="搜索"
        :prefix-icon="Search"
        size="large"
        clearable
        @clear="handleSearch"
        @input="handleSearch"
      />
    </div>
    
    <div class="knowledge-list">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" class="mb-20" v-for="item in knowledgeList" :key="item.id">
          <div class="knowledge-card" @click="handleEditKnowledge2(item)">
            <div class="card-header">
              <div class="avatar-container">
                <div class="avatar">
                  <img class="avatar-image" :src="zhishikuIconSrc" alt="知识库图标" />
                </div>
              </div>
              <div class="card-title-container">
                <div class="card-title">{{ item.name }}</div>
                <div class="card-desc">{{ item.description || '暂无描述' }}</div>
              </div>
            </div>
            <div class="card-footer">
              <div class="doc-count">{{ item.document_count }}个文档</div>
              <div class="card-actions">
                <el-icon class="action-icon edit-icon" @click.stop="handleEditKnowledge(item, $event)"><Edit /></el-icon>
                <el-icon class="action-icon delete-icon" @click.stop="handleDeleteKnowledge(item)"><Delete /></el-icon>
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" class="mb-20">
          <div class="new-knowledge-card" @click="handleCreateKnowledge">
            <div class="plus-icon">
              <!-- <img class="avatar-image" :src="zhishikuIconSrc" alt="知识库图标" /> -->
              <el-icon size="30"><Plus /></el-icon>
            </div>
            <div class="new-card-title">新建知识库</div>
            <div class="new-card-desc">点击创建团队共享知识库</div>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <div class="empty-tip" v-if="knowledgeList.length === 0 && !loading">
      <el-empty description="暂无知识库" />
    </div>
    
    <div class="loading" v-if="loading">
      <el-skeleton :rows="3" animated />
    </div>
    
    <!-- 编辑对话框 -->
    <el-dialog v-model="editDialogVisible" title="知识库设置" width="500px">
      <el-form :model="editForm" label-position="top">
        <el-form-item label="知识库名称">
          <el-input v-model="editForm.name" placeholder="请输入知识库名称" />
        </el-form-item>
        <el-form-item label="知识库描述">
          <el-input v-model="editForm.description" type="textarea" rows="4" placeholder="请输入知识库描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveKnowledgeSettings">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Search, Plus, Delete, Edit } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from "element-plus";
import { fetchDatasetList, updateDataset, deleteDataset } from '@/api/knowledge';
import ZhishikuIcon from '@/assets/images/zhishikuIcon.png';

defineOptions({
  name: 'Knowledge',
});

// 状态管理
const router = useRouter();
const route = useRoute();
const keyword = ref('');
const knowledgeList = ref<any[]>([]);
const loading = ref(false);
const zhishikuIconSrc = ref(ZhishikuIcon);

// 页面标题
const pageTitle = ref('知识库');

// 编辑对话框相关状态
const editDialogVisible = ref(false);
const currentEditItem = ref<any>({});
const editForm = ref({
  name: '',
  description: ''
});

// 搜索处理
const handleSearch = () => {
  getKnowledgeList();
};

// 获取知识库列表
const getKnowledgeList = async () => {
  loading.value = true;
  try {
    const params: any = {
      keyword: keyword.value,
    };
    
    // 从路由参数获取空间ID
    const spaceId = route.query.spaceId as string;
    if (spaceId) {
      params.space_id = spaceId;
    }
    
    const res = await fetchDatasetList(params);
    knowledgeList.value = res.data || [];
  } catch (error) {
    console.error('获取知识库列表失败', error);
    // 临时模拟数据
    knowledgeList.value = [
      {
        id: '1',
        name: '产品需求规范',
        description: '产品团队标准化文档模板与规范指南',
        document_count: 15,
        created_at: '2023-01-15T10:30:00Z',
        updated_at: '2023-06-20T14:45:00Z',
        tags: ['产品', '文档'],
        icon: '📚',
        status: 'ready',
        creator: {
          id: '1',
          name: '张三',
          avatar: ''
        }
      },
      {
        id: '2',
        name: '技术架构文档',
        description: '系统架构设计与技术实现文档',
        document_count: 8,
        created_at: '2023-02-10T09:15:00Z',
        updated_at: '2023-06-18T11:20:00Z',
        tags: ['技术', 'API'],
        icon: '💻',
        status: 'ready',
        creator: {
          id: '2',
          name: '李四',
          avatar: ''
        }
      }
    ] as any[];
  } finally {
    loading.value = false;
  }
};

// 创建知识库
const handleCreateKnowledge = () => {
  router.push('/knowledge/create?spaceId=' + (route.query.spaceId || ""));
};

const handleEditKnowledge2 = (item: any) => {
  router.push(`/knowledge/edit/${item.id}`);
};

// 编辑知识库
const handleEditKnowledge = (item: any, e?: Event) => {
  // 阻止事件冒泡，避免触发卡片点击事件
  if (e) {
    e.stopPropagation();
  }
  
  // 设置当前编辑项
  currentEditItem.value = item;
  // 填充表单
  editForm.value.name = item.name || '';
  editForm.value.description = item.description || '';
  // 显示对话框
  editDialogVisible.value = true;
};

// 保存知识库设置
const saveKnowledgeSettings = async () => {
  try {
    // 调用API更新知识库
    await updateDataset(currentEditItem.value.id, {
      name: editForm.value.name,
      description: editForm.value.description
    });
    
    // 提示成功
    ElMessage.success('知识库设置已更新');
    // 关闭对话框
    editDialogVisible.value = false;
    // 刷新列表
    getKnowledgeList();
  } catch (error: any) {
    console.error('更新知识库失败', error);
    ElMessage.error(error.message || '更新知识库设置失败');
  }
};

// 删除知识库
const handleDeleteKnowledge = (item: any) => {
  ElMessageBox.confirm(
    `确定要删除知识库 "${item.name}" 吗？此操作不可撤销。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await deleteDataset(item.id);
        ElMessage.success('删除成功');
        getKnowledgeList();
      } catch (error) {
        console.error('删除知识库失败', error);
        ElMessage.error('删除失败，请重试');
      }
    })
    .catch(() => {
      // 用户取消删除操作
    });
};

// 生命周期
onMounted(() => {
  getKnowledgeList();
});

// 监听路由变化，确保从其他页面返回时也能刷新数据
watch(
  () => route.fullPath,
  () => {
    // 当路由变化且当前页面为知识库列表页时，刷新数据
    if (route.path === '/knowledge') {
      getKnowledgeList();
    }
  }
);

// 监听搜索关键词变化
watch(
  () => keyword.value,
  () => {
    handleSearch();
  }
);
</script>

<style lang="scss" scoped>
.mb-20 {
  margin-bottom: 20px;
}
.knowledge-container {
  padding: 24px;
  height: 100%;
  overflow: hidden;
  background-image: url('@/assets/images/default_bg.png');
  background-size: 100%;
  background-repeat: no-repeat;
  
  .knowledge-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .knowledge-title {
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }
    
    .create-btn {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  .knowledge-search {
    margin-bottom: 24px;
    max-width: 240px;
  }
  
  .knowledge-list {
    margin-bottom: 24px;
    height: 100%;
    overflow: auto;
    padding-bottom: 128px;
    
    .knowledge-card, .new-knowledge-card {
      height: 100%;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      overflow: hidden;
      transition: all 0.3s;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      background-color: #fff;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-3px);
      }
    }
    
    .knowledge-card {
      cursor: pointer;
      display: flex;
      flex-direction: column;
      
      .card-header {
        display: flex;
        align-items: flex-start;
        padding: 20px;
        background-color: #F8F8F8;
        height: 135px;
        
        .avatar-container {
          margin-right: 16px;
          
          .avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            
            .avatar-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 50%;
            }
          }
        }
        
        .card-title-container {
          flex: 1;
          overflow: hidden;
          
          .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 8px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          .card-desc {
            font-size: 12px;
            color: #909399;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            line-height: 1.5;
          }
        }
      }
      
      .card-footer {
        padding: 12px 20px;
        border-top: 1px solid #ebeef5;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: auto;
        height: 65px;
        .doc-count {
          font-size: 12px;
          color: #3A67F8;
          background: #F0F3FF;
          border-radius: 4px;
          padding: 6px 8px;
        }
        
        .card-actions {
          display: flex;
          gap: 20px;
          
          .action-icon {
            cursor: pointer;
            font-size: 18px;
            color: #909399;
            transition: all 0.3s;
            
            &:hover {
              color: #409eff;
              
              &.delete-icon {
                color: #f56c6c;
              }
            }
          }
        }
      }
    }
    
    .new-knowledge-card {
      cursor: pointer;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border: 1px dashed #c0c4cc;
      padding: 40px 20px;
      text-align: center;
      
      .plus-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
        
        .avatar-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 50%;
        }
      }
      
      .new-card-title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .new-card-desc {
        font-size: 14px;
        color: #909399;
      }
      
      &:hover {
        border-color: #409eff;
        
        // .plus-icon {
        //   background-color: #ecf5ff;
        // }
      }
    }
  }
  
  .empty-tip, .loading {
    margin-top: 48px;
    display: flex;
    justify-content: center;
  }
}
</style>
