<template>
  <div class="workflow-detail-container">
    <div class="header">
      <div class="back" @click="emit('goBack')">
        <el-icon><ArrowLeft /></el-icon>
      </div>
      <div class="title">{{ props.name }}</div>
    </div>
    <div class="workflow"></div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft } from "@element-plus/icons-vue";
import { ref } from "vue";

const props = defineProps<{
  id: string | null;
  name: string | null;
}>();

const emit = defineEmits<{
  (e: "goBack"): void;
}>();
</script>

<style lang="scss" scoped>
.workflow-detail-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  .back {
    cursor: pointer;
    font-size: 20px;
    margin-right: 10px;
    &:hover {
      color: #409eff;
    }
  }
  .title {
    font-size: 22px;
    font-weight: 600;
    color: #303133;
  }
}
.workflow {
  flex: 1;
  overflow: hidden;
}
</style>
