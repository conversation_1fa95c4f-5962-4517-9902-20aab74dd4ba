<template>
  <div class="workflow-container">
    <template v-if="!showIframe">
      <!-- 搜索和创建区域 -->
      <div class="header-actions">
        <div class="search-wrapper">
          <el-input
            v-model="searchQuery"
            :placeholder="`搜索${AgentMode[props.mode as keyof typeof AgentMode]}`"
            class="search-input"
            clearable
            @change="getFlowList"
          >
            <template #prefix>
              <el-icon class="search-icon"><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <el-button type="primary" class="create-button" @click="handleCreateWorkflow">
          <el-icon><Plus /></el-icon>
          新建{{ AgentMode[props.mode as keyof typeof AgentMode] }}
        </el-button>
      </div>

      <!-- 工作流列表 -->
      <div class="workflow-content">
        <div class="workflow-grid">
          <div class="workflow-card" v-for="item in flowList" :key="item.id" @click="handleSelectWorkflow(item)">
            <div class="card-content">
              <div class="card-main">
                <img
                  class="agent-image"
                  :src="iconUrl + item.icon_url"
                  alt=""
                  v-if="item.icon_type === 'image' && item.icon_url"
                />
                <img v-else class="agent-image" src="@/assets/images/ic_qa_robot.png" alt="" />
                <div class="workflow-info">
                  <h3 class="workflow-name">{{ item.name }}</h3>
                  <p class="workflow-desc line-clamp line-clamp-2">{{ item.description }}</p>
                </div>
              </div>
            </div>
            <div class="card-actions" :class="{ show: dropdownVisible && activeItemId === item.id }" @click.stop>
              <el-dropdown
                trigger="click"
                placement="bottom-end"
                @visible-change="(visible: boolean) => handleDropdownVisibleChange(visible, item)"
                @command="(command: string) => handleCommand(command, item)"
              >
                <div class="action-trigger">
                  <el-icon><MoreFilled /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <!-- <el-dropdown-item command="edit">编辑</el-dropdown-item> -->
                    <el-dropdown-item command="delete">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
    </template>
    <NewDetail v-else :id="activeItemId" :name="activeItemName" @goBack="showIframe = false" />

    <!-- 创建工作流弹窗 -->
    <CreateWorkflowDialog v-model:visible="createDialogVisible" :mode="props.mode" @created="handleWorkflowCreated" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { Search, Plus, MoreFilled } from "@element-plus/icons-vue";
import { get, del } from "@/utils/request";
import { ElMessageBox, ElMessage } from "element-plus";
import type { AppsRecord, AppsListPageResponse } from "@/views/agents/types";
import { AgentMode } from "@/views/agents/types"; // 导入 AgentMode 类型
import CreateWorkflowDialog from "./CreateWorkflowDialog.vue";
import { useRouter } from "vue-router";
import NewDetail from "./NewDetail.vue";

const router = useRouter();
const searchQuery = ref("");
const iconUrl = import.meta.env.VITE_API_ICON_URL;
const createDialogVisible = ref(false);
const dropdownVisible = ref(false);
const activeItemId = ref<string | null>(null);
const activeItemName = ref<string | null>(null);
const showIframe = ref<boolean>(false);

const props = defineProps<{
  mode: string;
}>();

// 工作流列表
const flowList = ref<AppsRecord[]>([]);

// 获取工作流列表
const getFlowList = async () => {
  try {
    let res = await get<AppsListPageResponse>("/apps", {
      page: 1,
      limit: 100,
      name: searchQuery.value,
      is_created_by_me: false,
      mode: props.mode,
    });
    flowList.value.length = 0;
    flowList.value = [...res.data];
  } catch (error) {
    console.error("获取工作流列表失败", error);
  }
};

// 创建工作流
const handleCreateWorkflow = () => {
  createDialogVisible.value = true;
};

// 工作流创建成功后的回调
const handleWorkflowCreated = (item: { id: string; name: string }) => {
  activeItemId.value = item.id;
  activeItemName.value = item.name;
  showIframe.value = true;
  // 刷新列表
  getFlowList();
};

// 选择工作流
const handleSelectWorkflow = async (workflow: AppsRecord) => {
  try {
    let res = await get("/account/profile");
    if (res) {
      activeItemId.value = workflow.id;
      activeItemName.value = workflow.name;
      showIframe.value = true;
    }
  } catch (error) {}
};

// 处理下拉菜单可见性变化
const handleDropdownVisibleChange = (visible: boolean, item: AppsRecord) => {
  dropdownVisible.value = visible;
  if (visible) {
    activeItemId.value = item.id;
  } else {
    // 延迟清除activeItemId，避免菜单关闭时操作图标立即消失
    setTimeout(() => {
      activeItemId.value = null;
    }, 100);
  }
};

// 处理下拉菜单命令
const handleCommand = (command: string, item: AppsRecord) => {
  switch (command) {
    case "edit":
      console.log("编辑工作流:", item.name);
      // 这里可以添加编辑工作流的逻辑
      break;
    case "delete":
      handleDeleteWorkflow(item);
      break;
  }
};

// 删除工作流
const handleDeleteWorkflow = (item: AppsRecord) => {
  ElMessageBox.confirm(
    `确定要删除${AgentMode[props.mode as keyof typeof AgentMode]}「${item.name}」吗？此操作不可恢复。`,
    "删除确认",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "error",
      confirmButtonClass: "el-button--danger",
    }
  ).then(async () => {
    try {
      await del(`/apps/${item.id}`);
      ElMessage.success("删除成功");
      // 刷新列表
      getFlowList();
    } catch (error) {
      console.error("删除失败:", error);
    }
  });
};

onMounted(() => {
  getFlowList();
});
</script>

<style lang="scss" scoped>
.workflow-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 32px 24px;
  .header-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .search-wrapper {
      flex: 1;
      max-width: 480px;

      .search-input {
        :deep(.el-input__wrapper) {
          background-color: #fff;
          border-radius: 24px;
          padding: 0 16px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
          height: 44px;

          &:hover,
          &.is-focus {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }

        :deep(.el-input__inner) {
          height: 44px;
          font-size: 14px;
          &::placeholder {
            color: #909399;
          }
        }

        :deep(.search-icon) {
          font-size: 16px;
          color: #909399;
          margin-right: 4px;
        }
      }
    }

    .create-button {
      height: 40px;
      padding: 0 20px;
      font-size: 14px;
      border-radius: 4px;

      .el-icon {
        margin-right: 4px;
      }
    }
  }
}

.workflow-content {
  flex: 1;
  overflow-y: auto;
}

.workflow-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  padding: 0;

  .workflow-card {
    background: #fff;
    border-radius: 8px;
    cursor: pointer;
    border: 1px solid #e4e7ed;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transition: all 0.3s;
    height: 100%;
    padding: 20px;
    padding-bottom: 60px;
    position: relative;
    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .card-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      position: relative;
      height: 100%;
    }

    .card-main {
      display: flex;
      flex-direction: row;
      gap: 12px;

      .agent-image {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        overflow: hidden;
      }
      .agent-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .workflow-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .workflow-name {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        margin: 0 0 4px 0;
      }

      .workflow-desc {
        font-size: 13px;
        color: #909399;
        margin: 0;
        line-height: 1.4;
      }
    }
  }
}

.workflow-card {
  .card-actions {
    position: absolute;
    bottom: 12px;
    right: 12px;
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 1;

    &.show {
      opacity: 1;
    }

    .action-trigger {
      cursor: pointer;
      width: 32px;
      height: 32px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #909399;

      &:hover {
        background-color: #f5f7fa;
        color: #409eff;
      }

      .el-icon {
        font-size: 18px;
      }
    }
  }

  &:hover {
    .card-actions {
      opacity: 1;
    }
  }
}

@media (max-width: 1200px) {
  .workflow-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .workflow-grid {
    grid-template-columns: 1fr;
  }
}
</style>
