<template>
  <router-view :key="$route.fullPath"></router-view>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";
import { getRefreshToken, setToken, setRefreshToken } from "@/utils/user";
import { post } from "@/utils/request";

// 定义刷新token的接口响应类型
interface RefreshTokenResponse {
  data: {
    access_token: string;
    refresh_token: string;
  };
}

// 定时器引用
const tokenRefreshTimer = ref<number | null>(null);

// 刷新token的函数
const refreshToken = async () => {
  try {
    // 检查是否存在刷新token
    const refreshToken = getRefreshToken();
    if (!refreshToken) {
      return;
    }

    // 调用刷新token接口
    const response = await post<RefreshTokenResponse>("/refresh-token", {
      refresh_token: refreshToken,
    });

    // 保存新的token
    if (response && response.data) {
      setToken(response.data.access_token);
      setRefreshToken(response.data.refresh_token);
    }
  } catch (error) {}
};

// 启动定时器
const startTokenRefreshTimer = () => {
  // 清除可能存在的旧定时器
  if (tokenRefreshTimer.value) {
    clearInterval(tokenRefreshTimer.value);
  }

  // 设置新的定时器，每10分钟执行一次
  tokenRefreshTimer.value = window.setInterval(() => {
    refreshToken();
  }, 10 * 60 * 1000); // 10分钟 = 10 * 60 * 1000毫秒
};

onMounted(() => {
  // 启动定时刷新
  startTokenRefreshTimer();
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (tokenRefreshTimer.value) {
    clearInterval(tokenRefreshTimer.value);
    tokenRefreshTimer.value = null;
  }
});
</script>

<style>
#app {
  height: 100%;
}
</style>
