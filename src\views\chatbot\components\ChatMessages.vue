<template>
  <div class="chat-messages">
    <div v-if="messages.length === 0 && openingStatement" class="open-box">
      <div class="open-icon">
        <img src="@/assets/images/ic_qa_robot.png" alt="" />
      </div>
      <div class="open-text">{{ openingStatement }}</div>
    </div>
    <div v-else-if="messages.length === 0" class="empty-chat">
      <div class="empty-icon">
        <el-icon><ChatDotRound /></el-icon>
      </div>
      <div class="empty-text">开始一段新的对话</div>
    </div>

    <div v-else class="message-list">
      <div
        v-for="message in messages"
        :key="message.id"
        class="message-item"
        :class="{ 'user-message': message.role === 'user', 'bot-message': message.role === 'assistant' }"
      >
        <!-- 用户消息 -->
        <div v-if="message.role === 'user'" class="message-content user">
          <div class="avatar">
            <img src="@/assets/images/ic_qa_user.png" alt="" />
          </div>
          <div class="content">{{ message.content }}</div>
        </div>

        <!-- 机器人消息 -->
        <div v-else-if="message.role === 'assistant'" class="message-content bot">
          <div class="avatar">
            <img src="@/assets/images/ic_qa_robot.png" alt="" />
          </div>
          <div class="content">
            <template v-if="message.status === 'pending' || message.isLoading">
              <span class="loading-dots">思考中<span>.</span><span>.</span><span>.</span></span>
            </template>
            <template v-else-if="message.status === 'failed'">
              <div class="error-message">消息发送失败，请重试</div>
            </template>
            <template v-else>
              <div class="reset-content" v-html="formatMessage(message.content)"></div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { ChatDotRound } from '@element-plus/icons-vue';

// 定义消息类型
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  rawAnswer?: string;
  timestamp?: number;
  isLoading?: boolean;
  status?: string; // pending, normal, failed
}

export default defineComponent({
  name: 'ChatMessages',
  components: {
    ChatDotRound
  },
  props: {
    messages: {
      type: Array as () => ChatMessage[],
      required: true
    },
    openingStatement: {
      type: String,
      default: ''
    }
  },
  setup() {
    // 格式化消息内容
    const formatMessage = (content: string) => {
      if (!content) return '';

      // 简单处理换行符和代码块，保持基本格式
      return content
        .replace(/\n/g, '<br>')
        .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
        .replace(/`([^`]+)`/g, '<code>$1</code>');
    };

    return {
      formatMessage
    };
  }
});
</script>

<style scoped>
.chat-messages {
  padding: 16px;
  overflow-y: auto;
}

.empty-chat {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  gap: 12px;
  padding: 40px 0;
}

.empty-icon {
  font-size: 48px;
  color: #c0c4cc;
}

.empty-text {
  font-size: 16px;
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.message-item {
  display: flex;
  flex-direction: column;
}

.message-content {
  display: flex;
  gap: 12px;
  max-width: 100%;
}

.message-content.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-content.bot {
  align-self: flex-start;
}

.avatar {
  width: 40px;
  height: 40px;
  margin-top: 8px;
}

.avatar img {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.content {
  padding: 12px 16px;
  border-radius: 12px;
  max-width: calc(100% - 60px);
  overflow-wrap: break-word;
  font-weight: 400;
  font-size: 16px;
  line-height: 19px;
}

.user-message .content {
  background-color: #3f56f0;
  color: white;
  margin-right: 16px;
}

.bot-message .content {
  background-color: #fff;
  color: #000000;
  margin-left: 16px;
}

/* 重置内容样式 */
.reset-content :deep(*) {
  all: revert;
}

.reset-content :deep(code) {
  font-family: monospace;
  padding: 2px 4px;
  background-color: #f4f4f4;
  border-radius: 3px;
  font-size: 0.9em;
}

.reset-content :deep(pre) {
  background-color: #f4f4f4;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 0.5em 0;
}

.reset-content :deep(pre code) {
  padding: 0;
  background-color: transparent;
}

/* 加载动画 */
.loading-dots {
  display: inline-block;
}

.loading-dots span {
  animation-name: loading-dots;
  animation-duration: 1.4s;
  animation-iteration-count: infinite;
  animation-fill-mode: both;
  font-size: 16px;
  opacity: 0.2;
}

.loading-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes loading-dots {
  0% {
    opacity: 0.2;
  }
  20% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}

.error-message {
  color: #f56c6c;
}
/* 开场白样式 */
.open-box {
  width: 60%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  padding: 40px 0;
}

.open-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.open-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.open-text {
  margin-top: 12px;
  line-height: 20px;
  word-break: break-all;
  text-align: center;
  color: #333;
}

/* 推荐问题样式 */
.suggest-box {
  padding: 0 12px;
  padding-top: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.suggest-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  margin-top: 8px;
}
</style>
