<template>
  <div class="qa-container">
    <!-- 左侧会话列表 -->
    <div class="conversation-list">
      <div class="new-chat-btn" @click="createNewChat">
        <img class="new-chat-btn-img" src="@/assets/images/ic_qa_side.png" alt="" />
        <div class="new-chat-btn-title">新建对话</div>
      </div>
      <div class="margin-line"></div>
      <div class="chat-list">
        <template v-if="pinnedConversations.length > 0">
          <div class="chat-desc">已置顶</div>
          <div
            v-for="chat in pinnedConversations"
            :key="chat.id"
            class="chat-item"
            :class="{ active: currentChat?.id === chat.id }"
            @click="selectChat(chat)"
          >
            <div class="chat-item-content">
              <div class="chat-info">
                <img class="chat-img" src="@/assets/images/ic_qa_history.png" alt="" />
                <div class="chat-title">{{ chat.name }}</div>
              </div>
              <div class="chat-actions">
                <el-dropdown trigger="hover" @command="handleCommand($event, chat)">
                  <el-button link class="more-btn">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="unpin" :icon="Paperclip">取消置顶</el-dropdown-item>
                      <el-dropdown-item command="rename" :icon="Edit">重命名</el-dropdown-item>
                      <el-dropdown-item command="delete" :icon="Delete">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </template>
        <template v-if="conversations.length > 0">
          <div class="chat-desc" v-if="pinnedConversations.length > 0">对话列表</div>
          <div
            v-for="chat in conversations"
            :key="chat.id"
            class="chat-item"
            :class="{ active: currentChat?.id === chat.id }"
            @click="selectChat(chat)"
          >
            <div class="chat-item-content">
              <div class="chat-info">
                <img class="chat-img" src="@/assets/images/ic_qa_history.png" alt="" />
                <div class="chat-title">{{ chat.name }}</div>
              </div>
              <div class="chat-actions" v-if="chat.status">
                <el-dropdown trigger="hover" @command="handleCommand($event, chat)">
                  <el-button link class="more-btn">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="pin" :icon="MagicStick">置顶</el-dropdown-item>
                      <el-dropdown-item command="rename" :icon="Edit">重命名</el-dropdown-item>
                      <el-dropdown-item command="delete" :icon="Delete">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <!-- 右侧对话区域 -->
    <div class="chat-area">
      <div class="message-list" ref="messageListRef">
        <template v-if="messages.length > 0">
          <div v-for="(message, index) in messages" :key="index" class="message-item">
            <div class="question" v-show="message.query">
              <div class="message-content">
                <div class="avatar">
                  <img src="@/assets/images/ic_qa_user.png" alt="" />
                </div>
                <div class="content-box">
                  <div class="content">{{ message.query }}</div>
                </div>
              </div>
            </div>
            <div class="answer" v-show="message.answer || message.status === 'pending'">
              <div class="message-content">
                <div class="avatar">
                  <img src="@/assets/images/ic_qa_robot.png" alt="" />
                </div>
                <div class="content-box">
                  <div class="content">
                    <template v-if="message.status === 'pending'">
                      <span class="loading-dots">思考中<span>.</span><span>.</span><span>.</span></span>
                    </template>
                    <template v-else>
                      <div class="reset-content" v-html="message.answer"></div>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <div class="open-box" v-show="parameterRecord?.opening_statement && messages.length === 0">
          <div class="open-icon">
            <img v-if="chatIconUrl" :src="iconUrl + chatIconUrl" alt="" />
            <img v-else src="@/assets/images/ic_qa_robot.png" alt="" />
          </div>
          <div class="open-text">{{ parameterRecord?.opening_statement }}</div>
        </div>
      </div>
      <div class="suggest-box" v-if="suggestList.length > 0 && messages.length > 0">
        <el-divider>试着问问</el-divider>
        <ul class="suggest-list">
          <el-button
            plain
            size="small"
            v-for="(item, index) in suggestList"
            :key="index"
            @click="handleSuggest(item)"
            >{{ item }}</el-button
          >
        </ul>
      </div>
      <!-- 底部输入区域 -->
      <div class="input-area">
        <div class="tool-bar">
          <!-- <el-button class="tool-btn">
            <el-icon><Picture /></el-icon>
          </el-button>
          <el-button class="tool-btn">
            <el-icon><DocumentCopy /></el-icon>
          </el-button>
          <el-button class="tool-btn">
            <el-icon><Microphone /></el-icon>
          </el-button>
          <el-dropdown trigger="click" class="model-select">
            <el-button>
              <el-icon><Monitor /></el-icon>
              DeepSeek v2
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
          </el-dropdown> -->
          <div class="dataset-select-container">
            <el-select
              v-model="selectedDatasets"
              multiple
              collapse-tags
              collapse-tags-tooltip
              placeholder="请选择知识库"
              class="dataset-select"
              clearable
              size="large"
            >
              <template #prefix>
                <el-icon><Reading /></el-icon>
              </template>
              <el-option v-for="item in datasets" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </div>
          <!-- <el-button class="tool-btn">
            <el-icon><Setting /></el-icon>
            联网模式
          </el-button> -->
        </div>
        <div class="input-box">
          <el-input
            v-model.trim="inputMessage"
            type="textarea"
            :rows="3"
            placeholder="输入您的问题..."
            resize="none"
            @keydown.enter.prevent="sendMessage"
          />
          <img
            class="send-btn"
            v-show="inputMessage"
            src="@/assets/images/ic_qasend_active.png"
            alt=""
            @click="sendMessage"
          />
          <img class="send-btn" v-show="!inputMessage" src="@/assets/images/ic_qasend.png" alt="" />
        </div>
      </div>
    </div>
    <rename-dialog v-if="selectedChat" v-model="showRenameDialog" :chat="selectedChat" @confirm="handleRename" />
  </div>
</template>

<script setup lang="ts">
// @ts-nocheck
import { ref, onMounted, nextTick, onUnmounted } from "vue";
import {
  Plus,
  UserFilled,
  Picture,
  DocumentCopy,
  Microphone,
  Monitor,
  ArrowDown,
  Reading,
  Setting,
  MoreFilled,
  Edit,
  Delete,
  Paperclip,
  MagicStick,
  Check,
} from "@element-plus/icons-vue";
import type {
  IConversation,
  IMessage,
  IConversationResponse,
  IMessageResponse,
  ChatParams,
  IDataset,
  IDatasetResponse,
} from "./types";
import { get, patch, del, post, postStream } from "@/utils/request";
import { ElMessage, ElMessageBox } from "element-plus";
import RenameDialog from "./components/RenameDialog.vue";
import hljs from "highlight.js";
import { marked } from "marked";
import DOMPurify from "dompurify"; // 用于净化HTML
import { debounce } from "lodash-es";
import type { ParametersRecord, AppsListPageResponse, InstalledAppsResponse } from "@/views/agents/types";

const messages = ref<IMessage[]>([]);
const messageListRef = ref<HTMLElement | null>(null);
const currentChat = ref<IConversation | null>(null);
const inputMessage = ref("");
const showRenameDialog = ref(false);
const selectedChat = ref<IConversation | null>(null);

// 头像域名
const iconUrl = import.meta.env.VITE_API_ICON_URL;
// 应用配置
const parameterRecord = ref<ParametersRecord | null>(null);
// 建议列表
const suggestList = ref<string[]>([]);

// 当前对话id
const chatId = ref<string>(import.meta.env.VITE_CHAT_ID);
const chatIconUrl = ref<string | null>(null);

// 知识库相关
const datasets = ref<IDataset[]>([]);
const selectedDatasets = ref<string[]>([]);

// 模拟数据
const conversations = ref<IConversation[]>([]);
const pinnedConversations = ref<IConversation[]>([]);
// 获取历史会话列表
const getConversations = async () => {
  try {
    let promiseList: Promise<IConversationResponse>[] = [
      get<IConversationResponse>(`/installed-apps/${chatId.value}/conversations`, {
        limit: 100,
        pinned: true,
      }),
      get<IConversationResponse>(`/installed-apps/${chatId.value}/conversations`, {
        limit: 100,
        pinned: false,
      }),
    ];
    const res = await Promise.all(promiseList);
    pinnedConversations.value = [...res[0].data];
    let newConversations = conversations.value.filter(item => item.id === "");
    if (newConversations.length > 0) {
      conversations.value = [...res[1].data, ...newConversations];
    } else {
      conversations.value = [...res[1].data];
    }

    if (currentChat.value) return;
    if (pinnedConversations.value.length > 0) {
      currentChat.value = pinnedConversations.value[0];
    } else if (conversations.value.length > 0) {
      currentChat.value = conversations.value[0];
    }
  } catch (error) {}
};

const getMessages = async (chat: IConversation) => {
  try {
    const res = await get<IMessageResponse>(`/installed-apps/${chatId.value}/messages`, {
      conversation_id: chat.id,
      limit: 100,
      last_id: "",
    });
    // 处理每条消息的markdown内容
    messages.value = res.data.map(message => {
      const processedMessage = { ...message, rawAnswer: message.answer || "" };
      return processMessage(processedMessage);
    });
    // 等待DOM更新后执行代码高亮
    nextTick(() => {
      debouncedHighlight();
    });
  } catch (error) {}
};

const getParameters = async () => {
  try {
    const res = await get<ParametersRecord>(`/installed-apps/${chatId.value}/parameters`);
    parameterRecord.value = res;
  } catch (error) {}
};

const getSuggestList = async (conversationId: string) => {
  try {
    const res = await get<{ data: string[] }>(
      `/installed-apps/${chatId.value}/messages/${conversationId}/suggested-questions`
    );
    suggestList.value = res.data;
  } catch (error) {}
};

// 获取知识库列表
const getDatasets = async () => {
  try {
    const res = await get<IDatasetResponse>("/datasets", {
      limit: 100,
    });
    datasets.value = res.data;
  } catch (error) {}
};

const getAppsList = async () => {
  let res = await get<AppsListPageResponse>("/apps", {
    page: 1,
    limit: 100,
    is_created_by_me: false,
    mode: "chat",
  });
  if (res.data.length > 0) {
    let appList = res.data.filter(item => item.name === "AI问答助手");
    let appId = "";
    if (appList.length > 0) {
      appId = appList[0].id;
    } else {
      appId = res.data[0].id;
    }
    let rsp = await get<InstalledAppsResponse>("/installed-apps", { app_id: appId });
    if (rsp?.installed_apps.length > 0) {
      chatId.value = rsp.installed_apps[0].id;
      chatIconUrl.value = rsp.installed_apps[0].icon_url;
    }
  }
};

// 页面加载时获取历史会话列表和知识库列表
onMounted(async () => {
  await getAppsList();
  await Promise.all([getParameters(), getConversations(), getDatasets()]);
  if (currentChat.value?.id) {
    await getMessages(currentChat.value);
  }
  scrollToBottom();
});

const createNewChat = () => {
  let empty = conversations.value.filter(item => item.status === "");
  if (empty.length > 0) {
    currentChat.value = empty[0];
    return;
  }
  // 实现新建对话逻辑
  const newChat = {
    id: "",
    name: "新建对话",
    created_at: new Date().getTime(),
    updated_at: new Date().getTime(),
    inputs: {},
    introduction: "",
    status: "",
  };
  conversations.value.unshift(newChat);
  currentChat.value = newChat;
  messages.value.length = 0;
};

const selectChat = (chat: IConversation) => {
  if (chat.id === currentChat.value?.id) return;
  currentChat.value = chat;
  messages.value.length = 0;
  if (chat.status !== "") {
    getMessages(chat);
  }
};

// 获取对话名称
const getChatName = async (id: string) => {
  if (!id) return;
  try {
    const res: any = await post(`/installed-apps/${chatId.value}/conversations/${id}/name`, {
      auto_generate: true,
    });
    let index = conversations.value.findIndex(item => item.id === id);
    if (index >= 0) {
      conversations.value[index].name = res.name;
    }
  } catch (error) {}
};

// 初始化 Marked 配置
marked.use({
  breaks: true,
  gfm: true,
});

// 安全解析函数
const safeMarkdownParse = (content: string): string => {
  const rawHtml = marked.parse(content) as string;
  const sanitized = DOMPurify.sanitize(rawHtml, {
    ALLOWED_TAGS: [
      "p",
      "pre",
      "code",
      "strong",
      "em",
      "blockquote",
      "ul",
      "ol",
      "li",
      "h1",
      "h2",
      "h3",
      "h4",
      "h5",
      "h6",
      "span",
    ],
    ALLOWED_ATTR: ["class"],
    FORBID_ATTR: ["style", "onerror"],
  });
  return sanitized;
};

// 为了保持一致性，添加一个将消息处理为一致格式的函数
const processMessage = (message: IMessage): IMessage => {
  if (!message.answer) return message;

  // 确保所有消息都有rawAnswer属性用于存储原始文本
  if (!message.rawAnswer) {
    message.rawAnswer = message.answer;
  }

  // 对所有消息使用一致的解析方法
  message.answer = safeMarkdownParse(message.rawAnswer);
  return message;
};

// 防抖高亮函数
const debouncedHighlight = debounce(() => {
  nextTick(() => {
    document.querySelectorAll("pre code").forEach(block => {
      if (!(block as HTMLElement).classList.contains("hljs")) {
        hljs.highlightElement(block as HTMLElement);
      }
    });
  });
}, 50);

// 添加防抖的滚动函数
const debouncedScrollToBottom = debounce(() => {
  scrollToBottom();
}, 100);

const handleSuggest = (value: string) => {
  inputMessage.value = value;
  sendMessage();
};

// 发送对话信息
const sendMessage = async () => {
  if (!inputMessage.value.trim()) return;

  let params: ChatParams = {
    conversation_id: "",
    parent_message_id: null,
    files: [],
    inputs: {},
    response_mode: "streaming",
    query: inputMessage.value,
  };

  // 如果选择了知识库，添加到请求参数中
  if (selectedDatasets.value.length > 0) {
    params.dataset_ids = selectedDatasets.value;
  }

  if (messages.value?.length > 0) {
    params.conversation_id = messages.value[messages.value.length - 1]?.conversation_id || "";
    params.parent_message_id = messages.value[messages.value.length - 1]?.id || null;
  }

  try {
    // 添加用户问题到消息列表
    const newMessage: IMessage = {
      answer: "",
      rawAnswer: "",
      conversation_id: "",
      inputs: {},
      parent_message_id: null,
      query: inputMessage.value,
      status: "pending",
    };
    messages.value.push(newMessage);

    // 清空输入框
    inputMessage.value = "";

    // 发送请求
    await postStream(`/installed-apps/${chatId.value}/chat-messages`, params, {
      onMessage: text => {
        // 处理服务器返回的data:前缀
        const lastMessage = messages.value[messages.value.length - 1];
        if (!lastMessage) return;

        // 将接收到的文本按换行符分割，处理每一条消息
        const messageLines = text.split("\n");
        console.log("messageLines", messageLines);

        let shouldUpdate = false;

        for (const line of messageLines) {
          if (!line.includes("data:")) continue;

          try {
            const jsonStr = line.replace(/^data: /, "").trim();
            if (!jsonStr) continue;

            const data = JSON.parse(jsonStr);

            // 增量更新answer内容
            if (data.answer) {
              // 更新原始文本内容
              lastMessage.rawAnswer = (lastMessage.rawAnswer || "") + data.answer;
              shouldUpdate = true;

              // 收到第一条数据就更新状态
              if (lastMessage.status === "pending") {
                lastMessage.status = "normal";
              }
            }

            // 更新其他字段
            if (data.conversation_id) {
              lastMessage.conversation_id = data.conversation_id;
            }
            if (data.id) {
              lastMessage.id = data.id;
            }
          } catch (error) {}
        }

        // 实时更新渲染
        if (shouldUpdate && lastMessage.rawAnswer) {
          // 直接解析并更新显示内容
          lastMessage.answer = safeMarkdownParse(lastMessage.rawAnswer);
          // 触发代码高亮
          debouncedHighlight();
          // 滚动到底部
          debouncedScrollToBottom();
        }
      },
      onError: error => {
        console.error("发送消息失败:", error);
      },
      onComplete: async () => {
        const lastMessage = messages.value[messages.value.length - 1];
        if (lastMessage) {
          lastMessage.status = "normal";
          // 完成时再次解析整个内容，确保格式完整
          if (lastMessage.rawAnswer) {
            lastMessage.answer = safeMarkdownParse(lastMessage.rawAnswer);
            // 最后执行一次代码高亮
            debouncedHighlight();
          }
          if (currentChat.value) {
            currentChat.value.id = lastMessage?.conversation_id;
          }
        }
        if (messages.value.length === 1 && lastMessage?.conversation_id) {
          await getConversations();
          getChatName(lastMessage.conversation_id);
        }
        if (messages.value.length === 1 && parameterRecord.value?.opening_statement) {
          const newMessage: IMessage = {
            answer: `<p>${parameterRecord.value?.opening_statement}</p>`,
            rawAnswer: "",
            conversation_id: "",
            inputs: {},
            parent_message_id: null,
            query: "",
          };
          messages.value.unshift(newMessage);
        }
        if (lastMessage?.id && parameterRecord.value?.suggested_questions_after_answer.enabled) {
          getSuggestList(lastMessage?.id);
        }
      },
    });
  } catch (error) {
    // 更新消息状态为失败
    const lastMessage = messages.value[messages.value.length - 1];
    if (lastMessage) {
      lastMessage.status = "failed";
    }
    ElMessage.error("发送消息失败，请重试");
  }
};

const handleCommand = async (command: "pin" | "unpin" | "rename" | "delete", chat: IConversation) => {
  switch (command) {
    case "pin":
    case "unpin":
      // 实现置顶/取消置顶逻辑
      try {
        await patch(`/installed-apps/${chatId.value}/conversations/${chat.id}/${command}`);
        ElMessage.success(`${command === "pin" ? "置顶" : "取消置顶"}成功`);
        getConversations();
      } catch (error) {}
      break;
    case "rename":
      selectedChat.value = chat;
      showRenameDialog.value = true;
      break;
    case "delete":
      // 实现删除逻辑
      ElMessageBox.confirm("确定要删除此对话吗？删除后将无法恢复", "删除确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          await del(`/installed-apps/${chatId.value}/conversations/${chat.id}`);
          messages.value.length = 0;
          if (currentChat.value?.id === chat.id) {
            currentChat.value = null;
          }
          ElMessage.success("删除成功");
          await getConversations();
          if (currentChat.value?.id) {
            getMessages(currentChat.value);
          }
        } catch (error) {}
      });
      break;
  }
};

const handleRename = async (newName: string) => {
  try {
    await post(`/installed-apps/${chatId.value}/conversations/${selectedChat.value?.id}/name`, {
      name: newName,
    });
    ElMessage.success("重命名成功");
    getConversations();
    selectedChat.value = null;
  } catch (error) {}
};

// 添加滚动到底部的方法
const scrollToBottom = () => {
  if (messageListRef.value) {
    messageListRef.value.scrollTo({
      top: messageListRef.value.scrollHeight,
      behavior: "smooth",
    });
  }
};

onUnmounted(() => {
  debouncedHighlight.cancel();
  debouncedScrollToBottom.cancel();
});
</script>

<style lang="scss" scoped>
.qa-container {
  display: flex;
  height: 100%;
  width: 100%;
  background-image: url("@/assets/images/ic_qa_bg.png");
  background-size: cover;
  background-repeat: no-repeat;
}

.conversation-list {
  width: 300px;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px 16px;
}

.new-chat-btn {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background: #ebf0ff;
  border-radius: 12px 12px 12px 12px;
  border: 1px solid #3a67f8;
  cursor: pointer;
  .new-chat-btn-img {
    width: 28px;
    height: 28px;
    overflow: hidden;
  }
  .new-chat-btn-title {
    font-weight: 400;
    font-size: 16px;
    color: #3a67f8;
    line-height: 19px;
    margin-left: 8px;
  }
}

.margin-line {
  width: 100%;
  height: 1px;
  background-color: #e8e5e5;
  margin: 24px 0;
}

.chat-list {
  flex: 1;
  overflow-y: auto;
}

.chat-desc {
  font-size: 14px;
  color: #676f83;
  margin-bottom: 12px;
  padding-left: 6px;
}

.chat-item {
  cursor: pointer;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
  &.active {
    background: #ffffff;
    box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
  }
}

.chat-item-content {
  padding: 16px;
  display: flex;
  align-items: center;
}

.chat-info {
  flex: 1;
  overflow: hidden;
  display: flex;
  align-items: center;
  .chat-img {
    width: 16px;
    height: 16px;
    overflow: hidden;
  }
  .chat-title {
    font-weight: 400;
    font-size: 14px;
    color: #4a4a4a;
    line-height: 16px;
    margin-left: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.more-btn {
  &:hover {
    background-color: #f0f2f5;
    border-radius: 4px;
  }

  :deep(.el-icon) {
    font-size: 18px;
    color: #666;
    transform: rotate(90deg);
  }
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 36px;
  padding-top: 24px;
}

.message-item {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.message-content {
  display: flex;
  .avatar {
    width: 40px;
    height: 40px;
    margin-top: 8px;
    img {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
    :deep(.el-icon) {
      font-size: 24px;
      margin-left: 8px;
    }
  }
  .content-box {
    flex: 1;
    overflow: hidden;
    display: flex;
    .content {
      display: inline-block;
      max-width: 100%;
      overflow: hidden;
      padding: 0 20px;
      font-weight: 400;
      font-size: 16px;
      color: #000000;
      line-height: 19px;
      border-radius: 12px;
      .reset-content * {
        all: revert;
      }
    }
  }
}

.question {
  align-items: flex-end;
  .message-content {
    flex-direction: row-reverse;
    .content-box {
      justify-content: flex-end;
      .content {
        padding: 20px;
        background-color: #3f56f0;
        margin-right: 16px;
        color: #fff;
      }
    }
    .content {
      padding: 20px;
      background-color: #3f56f0;
      margin-right: 16px;
      color: #fff;
    }
  }
}

.answer {
  margin-top: 20px;
  align-items: flex-start;
  .content {
    background-color: #fff;
    margin-left: 16px;
  }
}

.input-area {
  margin-top: 20px;
  .tool-bar {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.input-box {
  position: relative;
  padding: 20px 92px 16px 92px;
  :deep(.el-textarea) {
    .el-textarea__inner {
      padding: 12px;
      padding-right: 120px;
      min-height: 120px !important;
      border: 1px solid #dcdfe6;
      border-radius: 8px;
      font-size: 14px;
      line-height: 1.6;
      background-color: #fff;
      &:focus {
        border-color: #409eff;
      }
    }
  }
}

.send-btn {
  position: absolute;
  bottom: 26px;
  right: 116px;
  width: 32px;
  height: 32px;
  overflow: hidden;
  cursor: pointer;
}

.loading-dots {
  display: inline-block;
  padding: 20px;
  font-weight: 400;
  font-size: 16px;
  color: #000000;
  line-height: 19px;
  span {
    display: inline-block;
    animation: dotFade 1.4s infinite;
    opacity: 0;
    &:nth-child(1) {
      animation-delay: 0.2s;
    }

    &:nth-child(2) {
      animation-delay: 0.4s;
    }

    &:nth-child(3) {
      animation-delay: 0.6s;
    }
  }
}

@keyframes dotFade {
  0%,
  100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

.dataset-select-container {
  margin: 0 8px;
}

.dataset-select {
  width: 250px;
}

.dataset-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

:deep(.el-select .el-input__prefix) {
  display: flex;
  align-items: center;
  color: #606266;
}
.open-box {
  width: 60%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  .open-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .open-text {
    margin-top: 12px;
    line-height: 20px;
    word-break: break-all;
  }
}
.suggest-box {
  padding: 0 12px;
  padding-top: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .suggest-title {
    font-size: 14px;
  }
}
:deep(.el-divider__text) {
  background-color: transparent;
}
</style>
