<template>
  <div class="workflow-detail-container">
    <div class="header">
      <div class="back" @click="emit('goBack')">
        <el-icon><ArrowLeft /></el-icon>
      </div>
      <div class="title">{{ props.name }}</div>
    </div>
    <iframe
      v-if="props.id"
      :src="iframeUrl"
      class="workflow-iframe"
      frameborder="0"
      allowfullscreen
      ref="workflowIframe"
      sandbox="allow-scripts allow-same-origin"
    ></iframe>
    <div v-else class="no-workflow-message">未找到工作流ID，请返回列表页选择工作流</div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { getToken } from "@/utils/user";
import { ArrowLeft } from "@element-plus/icons-vue";

const props = defineProps<{
  id: string | null;
  name: string | null;
}>();

const emit = defineEmits<{
  (e: "goBack"): void;
}>();

const token = ref("");
const workflowIframe = ref<HTMLIFrameElement | null>(null);

// 计算iframe的URL，包含工作流ID和token参数
const iframeUrl = computed(() => {
  if (!props.id) return "";
  // 将token作为URL参数传递
  const url = `${import.meta.env.VITE_API_ICON_URL}/app/${props.id}/workflow?embedded=true&token=${token.value}`;
  return url;
});

onMounted(() => {
  // 获取当前用户的token
  const userToken = getToken();
  if (userToken && typeof userToken === "string") {
    token.value = userToken;
  }
});
</script>

<style scoped lang="scss">
.workflow-detail-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  .back {
    cursor: pointer;
    font-size: 20px;
    margin-right: 10px;
    &:hover {
      color: #409eff;
    }
  }
  .title {
    font-size: 22px;
    font-weight: 600;
    color: #303133;
  }
}

.workflow-iframe {
  flex: 1;
  overflow: hidden;
}

.no-workflow-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 16px;
  color: #909399;
}
</style>
