/**
 * SVG图标预处理工具
 * 用于处理SVG图标，使其能够继承父元素颜色
 */

import { h, defineComponent } from 'vue';

/**
 * 创建一个可以继承颜色的SVG组件
 * @param SvgComponent 原始SVG组件
 * @returns 处理后的SVG组件
 */
export function createColorableIcon(SvgComponent: any) {
  return defineComponent({
    name: 'ColorableIcon',
    props: {
      color: {
        type: String,
        default: 'currentColor'
      },
      size: {
        type: [Number, String],
        default: 16
      }
    },
    setup(props) {
      return () => h(
        'div',
        {
          class: 'colorable-icon-wrapper',
          style: {
            color: props.color,
            width: typeof props.size === 'number' ? `${props.size}px` : props.size,
            height: typeof props.size === 'number' ? `${props.size}px` : props.size,
            display: 'inline-flex'
          }
        },
        h(SvgComponent, {
          class: 'colorable-icon',
          style: {
            width: '100%',
            height: '100%'
          }
        })
      );
    }
  });
}

/**
 * 全局CSS，确保SVG图标能够继承颜色
 * 需要在应用启动时添加到文档中
 */
export const svgIconStyles = `
.colorable-icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.colorable-icon-wrapper .colorable-icon {
  fill: currentColor;
}

.colorable-icon-wrapper .colorable-icon path,
.colorable-icon-wrapper .colorable-icon circle,
.colorable-icon-wrapper .colorable-icon rect,
.colorable-icon-wrapper .colorable-icon polygon {
  fill: currentColor !important;
  stroke: currentColor !important;
}
`;

/**
 * 将SVG图标样式添加到文档中
 */
export function injectSvgStyles() {
  if (typeof document !== 'undefined') {
    const styleEl = document.createElement('style');
    styleEl.textContent = svgIconStyles;
    document.head.appendChild(styleEl);
  }
}
