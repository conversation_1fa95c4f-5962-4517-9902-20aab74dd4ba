import { markRaw } from "vue";
import { Document, Setting, MagicStick, User, DataAnalysis } from "@element-plus/icons-vue";
import type { MainMenu } from "@/types/menu";
// 使用SVG图标工具
import { SvgIcons } from "@/utils/svgIcons";

// 导入图片资源
import agentDefaultIcon from "@/assets/images/ic_agent_default.png";
import agentActiveIcon from "@/assets/images/ic_agent_active.png";
import toolboxDefaultIcon from "@/assets/images/ic_toolbox_default.png";
import toolboxActiveIcon from "@/assets/images/ic_toolbox_active.png";
import knowledgeDefaultIcon from "@/assets/images/ic_knowledge_default.png";
import knowledgeActiveIcon from "@/assets/images/ic_knowledge_active.png";
import settingDefaultIcon from "@/assets/images/ic_setting_default.png";
import settingActiveIcon from "@/assets/images/ic_setting_active.png";
import aiDefaultIcon from "@/assets/images/ic_ai_default.png";
import aiActiveIcon from "@/assets/images/ic_ai_active.png";
import modelDefaultIcon from "@/assets/images/ic_model_default.png";
import modelActiveIcon from "@/assets/images/ic_model_active.png";
import structureDefaultIcon from "@/assets/images/ic_structure_default.png";
import structureActiveIcon from "@/assets/images/ic_structure_active.png";

export const mainMenus: MainMenu[] = [
  // {
  //   id: "discover",
  //   title: "发现智能体",
  //   icon: markRaw(MagicStick),
  //   path: "/home",
  //   children: [],
  // },
  {
    id: "agents",
    title: "智能体",
    icon: markRaw(MagicStick),
    url: agentDefaultIcon,
    activeUrl: agentActiveIcon,
    path: "/agents",
  },
  {
    id: "tools",
    title: "工具箱",
    icon: markRaw(SvgIcons.Toolbox),
    url: toolboxDefaultIcon,
    activeUrl: toolboxActiveIcon,
    children: [
      {
        title: "工作流",
        path: "/toolbox/workflow",
        icon: markRaw(SvgIcons.Workflow),
        url: toolboxDefaultIcon,
        activeUrl: toolboxActiveIcon,
      },
      // {
      //   title: "对话流",
      //   path: "/toolbox/chatflow",
      //   icon: markRaw(SvgIcons.Chatflow),
      // },
    ],
  },
  {
    id: "knowledge",
    title: "知识库",
    icon: markRaw(DataAnalysis),
    url: knowledgeDefaultIcon,
    activeUrl: knowledgeActiveIcon,
    children: [
      {
        title: "AI问答助手",
        path: "/qaAssistant",
        icon: markRaw(SvgIcons.RobotDefault),
        url: aiDefaultIcon,
        activeUrl: aiActiveIcon,
      },
      {
        title: "知识库",
        path: "/knowledge",
        icon: markRaw(DataAnalysis),
        url: knowledgeDefaultIcon,
        activeUrl: knowledgeActiveIcon,
      },
    ],
  },
  {
    id: "system",
    title: "系统管理",
    icon: markRaw(Setting),
    url: settingDefaultIcon,
    activeUrl: settingActiveIcon,
    children: [
      {
        title: "模型市场",
        path: "/system/models",
        icon: markRaw(Document),
        url: modelDefaultIcon,
        activeUrl: modelActiveIcon,
      },
      {
        title: "组织架构",
        path: "/system/structure",
        icon: markRaw(User),
        url: structureDefaultIcon,
        activeUrl: structureActiveIcon,
      },
    ],
  },
];
