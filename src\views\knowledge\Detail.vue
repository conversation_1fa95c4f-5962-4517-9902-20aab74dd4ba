<template>
  <div class="knowledge-detail-container">
    <div class="detail-header">
      <div class="back-btn" @click="handleBack">
        <el-icon><Back /></el-icon>
        <span>返回知识库</span>
      </div>
      
      <div class="header-content">
        <div class="knowledge-info">
          <div class="knowledge-icon">{{ knowledgeDetail.icon || '📚' }}</div>
          <div class="knowledge-meta">
            <div class="knowledge-name">{{ knowledgeDetail.name }}</div>
            <div class="knowledge-desc">{{ knowledgeDetail.description }}</div>
            <div class="knowledge-tags">
              <el-tag v-for="(tag, index) in knowledgeDetail.tags" :key="index" size="small">{{ tag }}</el-tag>
            </div>
          </div>
        </div>
        
        <div class="knowledge-actions">
          <el-button @click="handleEdit">
            <el-icon><Edit /></el-icon>
            <span>编辑</span>
          </el-button>
          <el-button type="primary" @click="handleAddDocument">
            <el-icon><Plus /></el-icon>
            <span>添加文档</span>
          </el-button>
        </div>
      </div>
    </div>
    
    <div class="detail-statistics">
      <div class="stat-card">
        <div class="stat-value">{{ knowledgeDetail.document_count || 0 }}</div>
        <div class="stat-label">文档数量</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ formatWordCount(knowledgeDetail.word_count || 0) }}</div>
        <div class="stat-label">总字数</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ formatDate(knowledgeDetail.created_at) }}</div>
        <div class="stat-label">创建时间</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ formatDate(knowledgeDetail.updated_at) }}</div>
        <div class="stat-label">更新时间</div>
      </div>
    </div>
    
    <div class="detail-tabs">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="文档" name="documents">
          <div class="tab-content">
            <div class="filter-bar">
              <el-input 
                v-model="documentKeyword" 
                placeholder="搜索文档" 
                :prefix-icon="Search" 
                clearable
                @input="handleDocumentSearch"
              />
              <el-select
                v-model="documentType"
                placeholder="文档类型"
                clearable
                @change="handleDocumentSearch"
              >
                <el-option label="全部" value="" />
                <el-option label="PDF" value="pdf" />
                <el-option label="Word" value="docx" />
                <el-option label="Excel" value="xlsx" />
                <el-option label="TXT" value="txt" />
                <el-option label="网页" value="web" />
              </el-select>
            </div>
            
            <el-table 
              :data="documentList" 
              border 
              style="width: 100%"
              v-loading="loading"
            >
              <el-table-column prop="title" label="文档名称" min-width="200">
                <template #default="{ row }">
                  <div class="document-title">
                    <el-icon v-if="row.type === 'pdf'"><Document /></el-icon>
                    <el-icon v-else-if="row.type === 'docx'"><DocumentCopy /></el-icon>
                    <el-icon v-else-if="row.type === 'xlsx'"><Grid /></el-icon>
                    <el-icon v-else-if="row.type === 'web'"><Link /></el-icon>
                    <el-icon v-else><Document /></el-icon>
                    <span>{{ row.title }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="type" label="类型" width="100" align="center">
                <template #default="{ row }">
                  <el-tag size="small" :type="getDocumentTagType(row.type)">{{ row.type.toUpperCase() }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="word_count" label="字数" width="100" align="center" />
              <el-table-column prop="created_at" label="创建时间" width="160" align="center">
                <template #default="{ row }">
                  {{ formatDate(row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180" align="center">
                <template #default="{ row }">
                  <el-button link type="primary" @click="handleViewDocument(row)">查看</el-button>
                  <el-button link type="danger" @click="handleDeleteDocument(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalDocuments"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="设置" name="settings">
          <div class="tab-content">
            <el-form :model="settingsForm" label-position="top">
              <el-form-item label="使用策略">
                <el-card class="settings-card">
                  <template #header>
                    <div class="settings-card-header">
                      <span>文档切片设置</span>
                      <el-switch v-model="settingsForm.enableChunking" />
                    </div>
                  </template>
                  <div class="settings-card-content" v-if="settingsForm.enableChunking">
                    <div class="settings-option">
                      <span class="option-label">切片大小</span>
                      <el-slider 
                        v-model="settingsForm.chunkSize" 
                        :min="200" 
                        :max="2000" 
                        :step="100"
                        show-input
                      />
                    </div>
                    <div class="settings-option">
                      <span class="option-label">切片重叠</span>
                      <el-slider 
                        v-model="settingsForm.chunkOverlap" 
                        :min="0" 
                        :max="500" 
                        :step="50"
                        show-input
                      />
                    </div>
                  </div>
                </el-card>
              </el-form-item>
              
              <el-form-item label="高级设置">
                <el-card class="settings-card">
                  <template #header>
                    <div class="settings-card-header">
                      <span>处理引擎</span>
                    </div>
                  </template>
                  <div class="settings-card-content">
                    <el-radio-group v-model="settingsForm.processingEngine">
                      <el-radio label="standard">标准引擎</el-radio>
                      <el-radio label="premium">高级引擎</el-radio>
                    </el-radio-group>
                    <div class="engine-description" v-if="settingsForm.processingEngine === 'standard'">
                      标准引擎适用于大多数知识库，处理速度快，支持常见文档格式。
                    </div>
                    <div class="engine-description" v-else>
                      高级引擎提供更精确的内容提取和更好的语义理解，适合复杂文档和高精度要求场景。
                    </div>
                  </div>
                </el-card>
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="saveSettings">保存设置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <!-- 添加文档对话框 -->
    <el-dialog v-model="addDocumentDialog" title="添加文档" width="600px">
      <el-form :model="documentForm" label-position="top">
        <el-form-item label="文档类型">
          <el-radio-group v-model="documentForm.uploadType">
            <el-radio label="file">文件上传</el-radio>
            <el-radio label="web">网页URL</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <template v-if="documentForm.uploadType === 'file'">
          <el-form-item label="上传文件">
            <el-upload
              class="document-upload"
              drag
              multiple
              :auto-upload="false"
              :file-list="documentFiles"
              :on-change="handleDocumentFileChange"
              :on-remove="handleDocumentFileRemove"
            >
              <el-icon class="el-icon--upload"><Upload /></el-icon>
              <div class="el-upload__text">拖拽文件到此处 或 <em>点击上传</em></div>
              <template #tip>
                <div class="el-upload__tip">
                  支持PDF, Word, Excel, TXT, HTML等文件格式，单个文件不超过50MB
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </template>
        
        <template v-else>
          <el-form-item label="网页URL">
            <el-input 
              v-model="documentForm.webUrl" 
              placeholder="输入网页URL，如https://example.com/page" 
            />
          </el-form-item>
        </template>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDocumentDialog = false">取消</el-button>
          <el-button type="primary" @click="submitAddDocument" :loading="submitting">
            确认添加
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 删除文档确认框 -->
    <el-dialog v-model="deleteDocumentDialog" title="确认删除" width="400px">
      <div class="delete-dialog-content">
        <el-icon :size="32" color="#F56C6C"><WarningFilled /></el-icon>
        <p class="delete-warning">确定要删除文档"{{ selectedDocument?.title }}"吗？此操作不可恢复。</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDocumentDialog = false">取消</el-button>
          <el-button type="danger" @click="confirmDeleteDocument" :loading="submitting">确认删除</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { 
  Back, Edit, Plus, Search, Document, DocumentCopy, 
  Grid, Link, Upload, WarningFilled 
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { TabsPaneContext, UploadUserFile } from 'element-plus';
import { get, post, del } from '@/utils/request';

defineOptions({
  name: 'KnowledgeDetail',
});

const router = useRouter();
const route = useRoute();
const knowledgeId = computed(() => route.params.id as string);

// 状态
const activeTab = ref('documents');
const loading = ref(false);
const submitting = ref(false);
const addDocumentDialog = ref(false);
const deleteDocumentDialog = ref(false);
const documentFiles = ref<UploadUserFile[]>([]);
const selectedDocument = ref<any>(null);

// 知识库详情
const knowledgeDetail = ref<any>({
  id: '',
  name: '',
  description: '',
  icon: '📚',
  tags: [],
  document_count: 0,
  word_count: 0,
  created_at: '',
  updated_at: '',
});

// 文档列表
const documentKeyword = ref('');
const documentType = ref('');
const documentList = ref<any[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const totalDocuments = ref(0);

// 设置表单
const settingsForm = reactive({
  enableChunking: true,
  chunkSize: 1000,
  chunkOverlap: 200,
  processingEngine: 'standard',
});

// 添加文档表单
const documentForm = reactive({
  uploadType: 'file',
  files: [] as File[],
  webUrl: '',
});

// 返回知识库列表
const handleBack = () => {
  router.push('/knowledge');
};

// 编辑知识库
const handleEdit = () => {
  router.push(`/knowledge/edit/${knowledgeId.value}`);
};

// 标签切换处理
const handleTabClick = (tab: TabsPaneContext) => {
  activeTab.value = tab.props.name as string;
};

// 获取知识库详情
const getKnowledgeDetail = async () => {
  loading.value = true;
  try {
    // 模拟API调用
    // const res = await get(`/datasets/${knowledgeId.value}`);
    // knowledgeDetail.value = res.data;
    
    // 模拟数据
    knowledgeDetail.value = {
      id: knowledgeId.value,
      name: '产品知识库',
      description: '包含公司所有产品的详细信息和使用指南',
      icon: '📚',
      tags: ['产品', '文档'],
      document_count: 128,
      word_count: 256000,
      created_at: '2023-01-15T10:30:00Z',
      updated_at: '2023-06-20T14:45:00Z',
    };
  } catch (error) {
    console.error('获取知识库详情失败', error);
    ElMessage.error('获取知识库详情失败');
  } finally {
    loading.value = false;
  }
};

// 获取文档列表
const getDocumentList = async () => {
  loading.value = true;
  try {
    // 模拟API调用
    // const params = {
    //   page: currentPage.value,
    //   limit: pageSize.value,
    //   keyword: documentKeyword.value,
    //   type: documentType.value,
    // };
    // const res = await get(`/datasets/${knowledgeId.value}/documents`, { params });
    // documentList.value = res.data.documents;
    // totalDocuments.value = res.data.total;
    
    // 模拟数据
    const mockDocuments = [
      {
        id: '1',
        title: '产品使用手册.pdf',
        type: 'pdf',
        word_count: 12500,
        created_at: '2023-05-10T08:30:00Z',
      },
      {
        id: '2',
        title: '产品规格说明.docx',
        type: 'docx',
        word_count: 8600,
        created_at: '2023-05-15T10:45:00Z',
      },
      {
        id: '3',
        title: '销售数据分析.xlsx',
        type: 'xlsx',
        word_count: 5200,
        created_at: '2023-05-20T14:20:00Z',
      },
      {
        id: '4',
        title: '官方网站内容.web',
        type: 'web',
        word_count: 15800,
        created_at: '2023-05-25T11:10:00Z',
      },
    ];
    
    // 简单过滤模拟搜索
    let filtered = mockDocuments;
    if (documentKeyword.value) {
      filtered = filtered.filter(doc => 
        doc.title.toLowerCase().includes(documentKeyword.value.toLowerCase())
      );
    }
    if (documentType.value) {
      filtered = filtered.filter(doc => doc.type === documentType.value);
    }
    
    documentList.value = filtered;
    totalDocuments.value = filtered.length;
  } catch (error) {
    console.error('获取文档列表失败', error);
    ElMessage.error('获取文档列表失败');
  } finally {
    loading.value = false;
  }
};

// 分页大小变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  getDocumentList();
};

// 当前页变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  getDocumentList();
};

// 文档搜索
const handleDocumentSearch = () => {
  currentPage.value = 1;
  getDocumentList();
};

// 查看文档
const handleViewDocument = (document: any) => {
  ElMessage.info(`查看文档: ${document.title}`);
  // 可以跳转到文档详情页或打开预览
  // router.push(`/knowledge/document/${knowledgeId.value}/${document.id}`);
};

// 删除文档
const handleDeleteDocument = (document: any) => {
  selectedDocument.value = document;
  deleteDocumentDialog.value = true;
};

// 确认删除文档
const confirmDeleteDocument = async () => {
  if (!selectedDocument.value) return;
  
  submitting.value = true;
  try {
    // 模拟API调用
    // await del(`/datasets/${knowledgeId.value}/documents/${selectedDocument.value.id}`);
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    ElMessage.success('文档删除成功');
    deleteDocumentDialog.value = false;
    getDocumentList();
    getKnowledgeDetail(); // 更新文档计数
  } catch (error) {
    console.error('删除文档失败', error);
    ElMessage.error('删除文档失败');
  } finally {
    submitting.value = false;
  }
};

// 添加文档
const handleAddDocument = () => {
  documentForm.uploadType = 'file';
  documentForm.files = [];
  documentForm.webUrl = '';
  documentFiles.value = [];
  addDocumentDialog.value = true;
};

// 文档文件变化处理
const handleDocumentFileChange = (file: UploadUserFile) => {
  const rawFile = file.raw;
  if (rawFile) {
    documentForm.files.push(rawFile);
  }
};

// 移除文档文件处理
const handleDocumentFileRemove = (file: UploadUserFile) => {
  const index = documentForm.files.findIndex(item => item.name === file.name);
  if (index !== -1) {
    documentForm.files.splice(index, 1);
  }
};

// 提交添加文档
const submitAddDocument = async () => {
  if (documentForm.uploadType === 'file' && documentForm.files.length === 0) {
    ElMessage.warning('请上传至少一个文件');
    return;
  }
  
  if (documentForm.uploadType === 'web' && !documentForm.webUrl) {
    ElMessage.warning('请输入网页URL');
    return;
  }
  
  submitting.value = true;
  try {
    // 模拟API调用
    // const formData = new FormData();
    // if (documentForm.uploadType === 'file') {
    //   documentForm.files.forEach(file => {
    //     formData.append('files[]', file);
    //   });
    // } else {
    //   formData.append('web_url', documentForm.webUrl);
    // }
    // await post(`/datasets/${knowledgeId.value}/documents`, formData);
    
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    ElMessage.success('文档添加成功');
    addDocumentDialog.value = false;
    getDocumentList();
    getKnowledgeDetail(); // 更新文档计数
  } catch (error) {
    console.error('添加文档失败', error);
    ElMessage.error('添加文档失败');
  } finally {
    submitting.value = false;
  }
};

// 保存设置
const saveSettings = async () => {
  submitting.value = true;
  try {
    // 模拟API调用
    // await post(`/datasets/${knowledgeId.value}/settings`, settingsForm);
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    ElMessage.success('设置保存成功');
  } catch (error) {
    console.error('保存设置失败', error);
    ElMessage.error('保存设置失败');
  } finally {
    submitting.value = false;
  }
};

// 格式化字数
const formatWordCount = (count: number) => {
  if (count >= 10000) {
    return `${(count / 10000).toFixed(1)}万`;
  }
  return count.toString();
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '--';
  const date = new Date(dateString);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 获取文档标签类型
const getDocumentTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    pdf: 'danger',
    docx: 'primary',
    xlsx: 'success',
    web: 'info',
  };
  return typeMap[type] || '';
};

// 生命周期
onMounted(() => {
  getKnowledgeDetail();
  getDocumentList();
});
</script>

<style scoped lang="scss">
.knowledge-detail-container {
  padding: 24px;
}

.detail-header {
  margin-bottom: 30px;
  
  .back-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    margin-bottom: 20px;
    width: fit-content;
    
    &:hover {
      color: #4080ff;
    }
  }
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    
    .knowledge-info {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      
      .knowledge-icon {
        font-size: 40px;
      }
      
      .knowledge-meta {
        .knowledge-name {
          font-size: 24px;
          font-weight: 600;
          color: #333;
          margin-bottom: 8px;
        }
        
        .knowledge-desc {
          font-size: 14px;
          color: #666;
          margin-bottom: 12px;
          max-width: 500px;
        }
        
        .knowledge-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }
      }
    }
    
    .knowledge-actions {
      display: flex;
      gap: 12px;
    }
  }
}

.detail-statistics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
  
  .stat-card {
    background-color: #f5f7fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    
    .stat-value {
      font-size: 24px;
      font-weight: 600;
      color: #4080ff;
      margin-bottom: 8px;
    }
    
    .stat-label {
      font-size: 14px;
      color: #666;
    }
  }
}

.detail-tabs {
  :deep(.el-tabs__nav-wrap) {
    padding: 0 20px;
  }
  
  .tab-content {
    padding: 20px;
    
    .filter-bar {
      display: flex;
      gap: 16px;
      margin-bottom: 20px;
      
      .el-input {
        width: 300px;
      }
    }
    
    .document-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}

.settings-card {
  margin-bottom: 20px;
  
  :deep(.el-card__header) {
    padding: 16px 20px;
  }
  
  .settings-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
  }
  
  .settings-card-content {
    padding: 10px 0;
    
    .settings-option {
      margin-bottom: 20px;
      
      .option-label {
        display: block;
        margin-bottom: 8px;
        font-size: 14px;
        color: #666;
      }
    }
    
    .engine-description {
      margin-top: 12px;
      font-size: 14px;
      color: #666;
      padding: 10px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }
  }
}

.delete-dialog-content {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 20px 0;
  
  .delete-warning {
    flex: 1;
    margin: 0;
  }
}

.document-upload {
  :deep(.el-upload-dragger) {
    width: 100%;
  }
}
</style>
