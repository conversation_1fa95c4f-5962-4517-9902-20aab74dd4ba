// 聊天机器人工具函数

/**
 * 从URL中获取token
 */
export const getTokenFromPath = (): string => {
  const pathParts = window.location.href.split('/');
  console.log("pathParts", window.location.href.split('/'));
  
  return pathParts[pathParts.length - 1] || '';
};

/**
 * 获取某个token对应的访问令牌
 * @param token 聊天机器人token
 */
export const getChatbotAccessToken = (token: string): string => {
  const tokenKey = 'chatbot_tokens';
  const tokensStr = localStorage.getItem(tokenKey) || '{}';
  try {
    const tokens = JSON.parse(tokensStr);
    return tokens[token] || '';
  } catch (e) {
    return '';
  }
};

/**
 * 设置某个token对应的访问令牌
 * @param token 聊天机器人token
 * @param accessToken 访问令牌
 */
export const setChatbotAccessToken = (token: string, accessToken: string): void => {
  const tokenKey = 'chatbot_tokens';
  const tokensStr = localStorage.getItem(tokenKey) || '{}';
  let tokens = {};
  try {
    tokens = JSON.parse(tokensStr);
  } catch (e) {
    tokens = {};
  }
  console.log(11111111111, accessToken)
  
  tokens = { ...tokens, [token]: accessToken };
  localStorage.setItem(tokenKey, JSON.stringify(tokens));
};

/**
 * 获取或设置当前会话ID
 * @param token 聊天机器人token
 * @param conversationId 需要设置的会话ID，不传则为获取
 */
export const chatbotConversationId = (token: string, conversationId?: string): string => {
  const key = `chatbot_conversation_${token}`;
  
  if (conversationId !== undefined) {
    localStorage.setItem(key, conversationId);
    return conversationId;
  }
  
  return localStorage.getItem(key) || '';
};

/**
 * 清除某个token的所有数据
 * @param token 聊天机器人token
 */
export const clearChatbotData = (token: string): void => {
  const tokenKey = 'chatbot_tokens';
  const tokensStr = localStorage.getItem(tokenKey) || '{}';
  let tokens = {};
  
  try {
    tokens = JSON.parse(tokensStr);
    delete tokens[token];
    localStorage.setItem(tokenKey, JSON.stringify(tokens));
  } catch (e) {
    // 忽略错误
  }
  
  localStorage.removeItem(`chatbot_conversation_${token}`);
};
