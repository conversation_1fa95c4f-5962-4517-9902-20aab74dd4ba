export interface MemberItem {
  avatar?: string | null;
  avatarUrl?: string | null;
  created_at: number;
  email: string | null;
  id: string;
  last_active_at: number;
  last_login_at: number;
  name: string;
  role: string;
  status: string;
}

export enum RoleEnum {
  owner = "所有者",
  admin = "管理员",
  editor = "编辑",
  normal = "成员",
}

export interface InviteMember {
  email: string;
  status: string;
  url: string;
  message?: string;
}

export interface InviteRsp {
  result: string;
  invitation_results: InviteMember[];
}

export interface Organization {
  id: string;
  name: string;
  description?: string;
  member_count: number;
  status: string;
  created_at: string;
  updated_at: string;
  children: Organization[];
}

export interface OrganizationResponse {
  result: string;
  organizations: Organization[];
}
