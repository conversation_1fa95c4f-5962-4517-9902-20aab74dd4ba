import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";
import svgLoader from 'vite-svg-loader';
export default defineConfig(function (_a) {
    var mode = _a.mode;
    var env = loadEnv(mode, process.cwd());
    return {
        base: env.VITE_CONFIG_URL || "/",
        plugins: [
            vue(),
            svgLoader()
        ],
        resolve: {
            alias: {
                "@": path.resolve(__dirname, "./src"),
            },
        },
        server: {
            port: 8088,
            host: "0.0.0.0",
            proxy: {
                "/api": {
                    target: env.VITE_API_BASE_URL, // 这里修改为你的实际后端接口地址
                    changeOrigin: true,
                    rewrite: function (path) { return path.replace(/^\/api/, ""); },
                },
            },
        },
    };
});
