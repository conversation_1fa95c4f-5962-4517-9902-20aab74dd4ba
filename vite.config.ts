import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";
import svgLoader from 'vite-svg-loader'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  // 检查是否禁用TypeScript检查
  const disableTsCheck = process.env.DISABLE_TS_CHECK === 'true';

  return {
    base: env.VITE_CONFIG_URL || "/",
    plugins: [
      vue(),
      svgLoader()
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    // 在构建时忽略类型错误
    build: {
      // 即使有类型错误也继续构建
      typescript: {
        ignoreBuildErrors: true,
        noEmitOnError: false,
      },
      // 设置大小警告限制
      chunkSizeWarningLimit: 2000,
      terserOptions: {
        compress: {
          drop_console: mode === 'production',
          drop_debugger: mode === 'production'
        }
      },
      // 忽略构建警告
      rollupOptions: {
        onwarn(warning: any, warn: any) {
          // 忽略特定的警告
          if (warning.code === 'CIRCULAR_DEPENDENCY') return;
          if (warning.code === 'UNUSED_EXTERNAL_IMPORT') return;
          if (warning.code === 'THIS_IS_UNDEFINED') return;
          // 使用默认警告处理其他警告
          warn(warning);
        }
      }
    },
    esbuild: {
      // 忽略构建过程中的警告
      logOverride: {
        'unused-import': 'silent',
        'unused-variable': 'silent',
        'this-is-undefined-in-esm': 'silent'
      }
    },
    server: {
      port: 8088,
      host: "0.0.0.0",
      hmr: {
        overlay: false, // 禁用错误覆盖层
      },
      proxy: {
        "/api": {
          target: env.VITE_API_BASE_URL, // 这里修改为你的实际后端接口地址
          changeOrigin: true,
          rewrite: (path: string) => path.replace(/^\/api/, ""),
        },
      },
    },
  };
});
