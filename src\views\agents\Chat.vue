<template>
  <div class="chat-container" v-if="agentRecord">
    <div class="qa-container">
      <!-- 左侧会话列表 -->
      <div class="conversation-list">
        <div class="header">
          <div class="back" @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
          </div>
          <div class="title">{{ agentRecord.app.name }}</div>
        </div>
        <div class="new-chat-btn" @click="createNewChat">
          <img class="new-chat-btn-img" src="@/assets/images/ic_qa_side.png" alt="" />
          <div class="new-chat-btn-title">新建对话</div>
        </div>
        <div class="margin-line"></div>
        <div class="chat-list">
          <template v-if="pinnedConversations.length > 0">
            <div class="chat-desc">已置顶</div>
            <div
              v-for="chat in pinnedConversations"
              :key="chat.id"
              class="chat-item"
              :class="{ active: currentChat?.id === chat.id }"
              @click="selectChat(chat)"
            >
              <div class="chat-item-content">
                <div class="chat-info">
                  <img class="chat-img" src="@/assets/images/ic_qa_history.png" alt="" />
                  <div class="chat-title">{{ chat.name }}</div>
                </div>
                <div class="chat-actions">
                  <el-dropdown trigger="hover" @command="handleCommand($event, chat)">
                    <el-button link class="more-btn">
                      <el-icon><MoreFilled /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="unpin" :icon="Paperclip">取消置顶</el-dropdown-item>
                        <el-dropdown-item command="rename" :icon="Edit">重命名</el-dropdown-item>
                        <el-dropdown-item command="delete" :icon="Delete">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </template>
          <template v-if="conversations.length > 0">
            <div class="chat-desc" v-if="pinnedConversations.length > 0">对话列表</div>
            <div
              v-for="chat in conversations"
              :key="chat.id"
              class="chat-item"
              :class="{ active: currentChat?.id === chat.id }"
              @click="selectChat(chat)"
            >
              <div class="chat-item-content">
                <div class="chat-info">
                  <img class="chat-img" src="@/assets/images/ic_qa_history.png" alt="" />
                  <div class="chat-title">{{ chat.name }}</div>
                </div>
                <div class="chat-actions" v-if="chat.status">
                  <el-dropdown trigger="hover" @command="handleCommand($event, chat)">
                    <el-button link class="more-btn">
                      <el-icon><MoreFilled /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="pin" :icon="MagicStick">置顶</el-dropdown-item>
                        <el-dropdown-item command="rename" :icon="Edit">重命名</el-dropdown-item>
                        <el-dropdown-item command="delete" :icon="Delete">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <!-- 右侧对话区域 -->
      <div class="chat-area">
        <div class="message-list" ref="messageListRef">
          <template v-if="messages.length > 0">
            <div v-for="(message, index) in messages" :key="index" class="message-item">
              <div class="question" v-show="message.query">
                <div class="message-content">
                  <div class="avatar">
                    <img src="@/assets/images/ic_qa_user.png" alt="" />
                  </div>
                  <div class="content-box">
                    <div class="content">
                      <div class="query-file" v-if="message?.message_files?.length > 0">
                        <template v-for="(file, index) in message.message_files" :key="index">
                          <div class="content-file" v-if="file.type === 'image'">
                            <el-image
                              :src="file.id ? iconUrl + file.url : file.fileUrl"
                              class="content-file-image"
                              fit="contain"
                              :preview-src-list="[file.id ? iconUrl + file.url : file.url]"
                              :initial-index="0"
                              preview-teleported
                            />
                          </div>
                          <div v-else class="query-document">
                            <div class="query-document-name">{{ file.name || file.filename }}</div>
                            <div class="query-document-details">
                              <img :src="getFileIcon(file.name || file.filename)" class="query-document-icon" alt="" />
                              <div class="query-document-ext">
                                {{
                                  (file.name
                                    ? file.name.split(".").pop() || "xlsx"
                                    : file.filename.split(".").pop() || "xlsx"
                                  ).toUpperCase()
                                }}
                              </div>
                              <div class="query-document-size">
                                {{ file.size ? (file.size / 1024).toFixed(2) + "KB" : "" }}
                              </div>
                            </div>
                          </div>
                        </template>
                      </div>
                      <div>{{ message.query }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="answer" v-show="message.answer || message.status === 'pending'">
                <div class="message-content">
                  <div class="avatar">
                    <img src="@/assets/images/ic_qa_robot.png" alt="" />
                  </div>
                  <div class="content-box">
                    <div class="content">
                      <template v-if="message.status === 'pending'">
                        <span class="loading-dots">思考中<span>.</span><span>.</span><span>.</span></span>
                      </template>
                      <template v-else>
                        <div class="reset-content" v-linkify:options="options" v-html="message.answer"></div>
                        <template v-if="index === 0">
                          <div
                            class="suggested-question-button"
                            style="padding-bottom: 10px"
                            v-if="
                              messages?.length > 1 ||
                              ((parameterRecord?.suggested_questions?.length ?? 0) > 0 && isDone)
                            "
                          >
                            <div
                              class="content-suggest-button"
                              v-for="(item, index) in parameterRecord?.suggested_questions"
                            >
                              <el-button plain :key="index" @click="handleSuggest(item)">{{ item }}</el-button>
                            </div>
                          </div>
                        </template>
                      </template>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <div style="height: 100%" v-show="parameterRecord?.opening_statement && messages.length === 0">
            <div class="open-box" v-show="(parameterRecord?.suggested_questions?.length ?? 0) > 0">
              <div class="suggested-question">
                <div class="suggested-question-left">
                  <img v-if="agentRecord.app.icon_url" :src="iconUrl + agentRecord.app.icon_url" alt="" />
                  <img v-else src="@/assets/images/ic_qa_robot.png" alt="" />
                </div>
                <div class="suggested-question-right">
                  <div class="open-text">{{ parameterRecord?.opening_statement }}</div>
                  <div class="suggested-question-button">
                    <div class="content-suggest-button" v-for="(item, index) in parameterRecord?.suggested_questions">
                      <el-button plain :key="index" @click="handleSuggest(item)">{{ item }}</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="open-box" v-show="parameterRecord?.suggested_questions?.length === 0">
              <div class="open-icon">
                <img v-if="agentRecord.app.icon_url" :src="iconUrl + agentRecord.app.icon_url" alt="" />
                <img v-else src="@/assets/images/ic_qa_robot.png" alt="" />
              </div>
              <div class="open-text">{{ parameterRecord?.opening_statement }}</div>
            </div>
          </div>
        </div>
        <div class="suggest-box" v-if="suggestList.length > 0 && messages.length > 0">
          <el-divider>试着问问</el-divider>
          <ul class="suggest-list">
            <el-button
              plain
              size="small"
              v-for="(item, index) in suggestList"
              :key="index"
              @click="handleSuggest(item)"
              >{{ item }}</el-button
            >
          </ul>
        </div>
        <!-- 底部输入区域 -->
        <div class="input-area">
          <!-- <div class="tool-bar">
          <el-button class="tool-btn">
            <el-icon><Picture /></el-icon>
          </el-button>
          <el-button class="tool-btn">
            <el-icon><DocumentCopy /></el-icon>
          </el-button>
          <el-button class="tool-btn">
            <el-icon><Microphone /></el-icon>
          </el-button>
          <el-dropdown trigger="click" class="model-select">
            <el-button>
              <el-icon><Monitor /></el-icon>
              DeepSeek v2
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
          </el-dropdown>
          <el-button class="tool-btn">
            <el-icon><Reading /></el-icon>
            选择知识库
          </el-button>
          <el-button class="tool-btn">
            <el-icon><Setting /></el-icon>
            联网模式
          </el-button>
        </div> -->
          <div class="input-box">
            <div class="file-box" v-if="uploadedFiles.length > 0">
              <div v-for="(file, index) in uploadedFiles" :key="file.upload_file_id" class="file-item">
                <div class="file-item-image" v-if="file.type === 'image'">
                  <el-image
                    :src="file.fileUrl"
                    class="file-image"
                    fit="contain"
                    :preview-src-list="[file.fileUrl]"
                    :initial-index="0"
                    preview-teleported
                  />
                </div>
                <div v-else class="query-document">
                  <div class="query-document-name">{{ file.name || file.filename }}</div>
                  <div class="query-document-details">
                    <img :src="getFileIcon(file.name)" class="query-document-icon" alt="" />
                    <div class="query-document-ext">
                      {{ (file.name ? file.name.split(".").pop() || "xlsx" : "").toUpperCase() }}
                    </div>
                    <div class="query-document-size">
                      {{ file.size ? (file.size / 1024).toFixed(2) + "KB" : "" }}
                    </div>
                  </div>
                </div>
                <div class="delete-icon" @click.stop="removeFile(index)">
                  <el-icon><Close /></el-icon>
                </div>
              </div>
            </div>
            <el-input
              v-model.trim="inputMessage"
              type="textarea"
              :rows="3"
              placeholder="输入您的问题..."
              resize="none"
              @keydown.enter.prevent="sendMessage"
            />
            <div class="send-box">
              <el-upload
                ref="uploadRef"
                v-if="isFileUploadEnabled"
                class="send-file-upload"
                :show-file-list="false"
                :accept="allowedFileExtensions"
                :before-upload="beforeFileUpload"
                :http-request="handleFileUpload"
                :on-exceed="handleExceed"
                multiple
                :limit="agentAppRecord?.model_config?.file_upload?.number_limits || 3"
              >
                <el-button class="send-file" plain :icon="Link" />
              </el-upload>
              <img
                class="send-btn"
                v-show="inputMessage"
                src="@/assets/images/ic_qasend_active.png"
                alt=""
                @click="sendMessage"
              />
              <img class="send-btn" v-show="!inputMessage" src="@/assets/images/ic_qasend.png" alt="" />
            </div>
          </div>
        </div>
      </div>
      <rename-dialog v-if="selectedChat" v-model="showRenameDialog" :chat="selectedChat" @confirm="handleRename" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, onUnmounted } from "vue";
import {
  ArrowLeft,
  MoreFilled,
  Edit,
  Delete,
  Link,
  Document,
  Close,
  MagicStick,
  Paperclip,
} from "@element-plus/icons-vue";
import type { IConversation, IMessage, IConversationResponse, IMessageResponse } from "@/views/qaAssistant/types";
import { get, patch, del, post, postStream, uploadFile } from "@/utils/request";
import { ElMessage, ElMessageBox } from "element-plus";
import RenameDialog from "@/views/qaAssistant/components/RenameDialog.vue";
import SvgIcon from "@/components/SvgIcon.vue";
import { SvgIcons } from "@/utils/svgIcons";
import hljs from "highlight.js";
import { marked } from "marked";
import DOMPurify from "dompurify"; // 用于净化HTML
import { debounce } from "lodash-es";
import { useRoute, useRouter } from "vue-router";

import type {
  ParametersRecord,
  InstalledAppsResponse,
  InstalledApp,
  AgentAppRecord,
  UploadedFile,
  ChatParams,
} from "./types";

import { fileIcon } from "@/utils";
// 导入所有文件图标
import excelIcon from "@/assets/files/excel.png";
import folderIcon from "@/assets/files/folder.png";
import jpgorpngIcon from "@/assets/files/jpgorpng.png";
import mp3Icon from "@/assets/files/mp3.png";
import mp4Icon from "@/assets/files/mp4.png";
import otherIcon from "@/assets/files/otherIcon.png";
import pdfIcon from "@/assets/files/pdf.png";
import pptIcon from "@/assets/files/ppt.png";
import txtIcon from "@/assets/files/txt.png";
import wordIcon from "@/assets/files/word.png";
import wpsIcon from "@/assets/files/wps.png";
import yswjIcon from "@/assets/files/yswj.png";

// 文件图标映射
const fileIconMap: Record<string, string> = {
  word: wordIcon,
  excel: excelIcon,
  ppt: pptIcon,
  pdf: pdfIcon,
  wps: wpsIcon,
  txt: txtIcon,
  jpgorpng: jpgorpngIcon,
  yswj: yswjIcon,
  mp3: mp3Icon,
  mp4: mp4Icon,
  folder: folderIcon,
  otherIcon: otherIcon,
};

// 获取文件图标的辅助函数
const getFileIcon = (fileName?: string): string => {
  if (!fileName) return fileIconMap.otherIcon;
  const ext = fileName.split(".").pop() || "";
  const iconKey = ext in fileIcon ? fileIcon[ext as keyof typeof fileIcon] : "otherIcon";
  return fileIconMap[iconKey] || fileIconMap.otherIcon;
};

const options = ref({
  className: "custom-link", // 自定义链接样式类
  target: "_blank", // 新窗口打开
  validate: (text: string) => {
    // 自定义验证逻辑
    return text.startsWith("https") || text.startsWith("http");
  },
});

const route = useRoute();
const router = useRouter();
const messages = ref<IMessage[]>([]);
const messageListRef = ref<HTMLElement | null>(null);
const currentChat = ref<IConversation | null>(null);
const inputMessage = ref("");
const showRenameDialog = ref(false);
const selectedChat = ref<IConversation | null>(null);
const uploadRef = ref<any>(null);

// 头像域名
const iconUrl = import.meta.env.VITE_API_ICON_URL;
// 应用配置
const parameterRecord = ref<ParametersRecord | null>(null);
// 建议列表
const suggestList = ref<string[]>([]);
// 智能体详情
const agentRecord = ref<InstalledApp | null>(null);
// 智能体配置详情
const agentAppRecord = ref<AgentAppRecord | null>(null);
// 是否启用文件上传
const isFileUploadEnabled = ref<boolean>(false);
// 文件上传后缀名
const allowedFileExtensions = ref<string>("");
// 文件上传数量限制
const uploadLimit = ref<number>(1);
// 文件上传类型
type FeatureKey = "vision" | "document";
const featuresType: Record<FeatureKey, string[]> = {
  vision: [".JPG", ".JPEG", ".PNG", ".GIF", ".WEBP", ".SVG", ".MP4", ".MOV", ".MPEG", ".MPGA"],
  document: [".DOC", ".DOCX", ".PDF", ".PPT", ".PPTX", ".XLS", ".XLSX", ".TXT"],
};
const featuresList: FeatureKey[] = ["vision", "document"];

const isDone = ref(true);

// 模拟数据
const conversations = ref<IConversation[]>([]);
const pinnedConversations = ref<IConversation[]>([]);
// 获取历史会话列表
const getConversations = async () => {
  try {
    let promiseList: Promise<IConversationResponse>[] = [
      get<IConversationResponse>(`/installed-apps/${agentRecord.value?.id}/conversations`, {
        limit: 100,
        pinned: true,
      }),
      get<IConversationResponse>(`/installed-apps/${agentRecord.value?.id}/conversations`, {
        limit: 100,
        pinned: false,
      }),
    ];
    const res = await Promise.all(promiseList);
    pinnedConversations.value = [...res[0].data];
    let newConversations = conversations.value.filter(item => item.id === "");
    if (newConversations.length > 0) {
      conversations.value = [...res[1].data, ...newConversations];
    } else {
      conversations.value = [...res[1].data];
    }

    if (currentChat.value) return;
    if (pinnedConversations.value.length > 0) {
      currentChat.value = pinnedConversations.value[0];
    } else if (conversations.value.length > 0) {
      currentChat.value = conversations.value[0];
    }
  } catch (error) {
    console.error("获取会话列表失败:", error);
  }
};

const getMessages = async (chat: IConversation) => {
  try {
    const res = await get<IMessageResponse>(`/installed-apps/${agentRecord.value?.id}/messages`, {
      conversation_id: chat.id,
      limit: 100,
      last_id: "",
    });
    // 处理每条消息的markdown内容
    messages.value = res.data.map(message => {
      const processedMessage = { ...message, rawAnswer: message.answer || "" };
      return processMessage(processedMessage);
    });
    if (parameterRecord.value?.opening_statement) {
      const newMessage: IMessage = {
        answer: `<p>${parameterRecord.value?.opening_statement}</p>`,
        rawAnswer: "",
        conversation_id: "",
        inputs: {},
        parent_message_id: null,
        query: "",
      };
      messages.value.unshift(newMessage);
    }

    // 等待DOM更新后执行代码高亮
    nextTick(() => {
      debouncedHighlight();
    });
  } catch (error) {
    console.error("获取消息列表失败:", error);
  }
};

const getParameters = async () => {
  try {
    const res = await get<ParametersRecord>(`/installed-apps/${agentRecord.value?.id}/parameters`);
    parameterRecord.value = res;
  } catch (error) {}
};

// 获取已安装的应用
const getInstalledApps = async () => {
  try {
    const res = await get<InstalledAppsResponse>("/installed-apps", { app_id: route.query.id });
    if (res.installed_apps.length > 0) {
      agentRecord.value = res.installed_apps[0];
    }
  } catch (error) {}
};

// 获取智能体配置详情
const getAgentAppRecord = async () => {
  try {
    const res = await get<AgentAppRecord>(`/apps/${route.query.id}`);
    agentAppRecord.value = res;
  } catch (error) {}
};

// 获取模型列表
const getModelList = async () => {
  try {
    const res = await get<any>(`/workspaces/current/models/model-types/llm`);
    let modelList: any = [];
    let currentModel: any = "";
    res.data.forEach((item: any) => {
      modelList.push(...item.models);
    });
    currentModel = modelList.find((item: any) => item.model === agentAppRecord.value?.model_config.model.name);
    let filterList = currentModel.features.filter((item: any) => featuresList.includes(item)) as FeatureKey[];
    if (filterList.length > 0) {
      isFileUploadEnabled.value = true;
      allowedFileExtensions.value = filterList.map((item: FeatureKey) => featuresType[item]).join(",");

      // 设置上传数量限制
      if (agentAppRecord.value?.model_config.file_upload.number_limits) {
        uploadLimit.value = agentAppRecord.value.model_config.file_upload.number_limits;
      }
    }
  } catch (error) {}
};

const getSuggestList = async (conversationId: string) => {
  try {
    const res = await get<{ data: string[] }>(
      `/installed-apps/${agentRecord.value?.id}/messages/${conversationId}/suggested-questions`
    );
    suggestList.value = res.data;
  } catch (error) {}
};

// 页面加载时获取历史会话列表
onMounted(async () => {
  // 重置文件上传状态
  resetFileUpload();
  await getInstalledApps();
  await getAgentAppRecord();
  await getModelList();
  await getParameters();
  await getConversations();
  if (currentChat.value?.id) {
    await getMessages(currentChat.value);
  }
  scrollToBottom();
});

const createNewChat = () => {
  let empty = conversations.value.filter(item => item.status === "");
  if (empty.length > 0) {
    currentChat.value = empty[0];
    // 重置文件上传状态
    resetFileUpload();
    return;
  }
  // 实现新建对话逻辑
  const newChat = {
    id: "",
    name: "新建对话",
    created_at: new Date().getTime(),
    updated_at: new Date().getTime(),
    inputs: {},
    introduction: "",
    status: "",
  };
  conversations.value.unshift(newChat);
  currentChat.value = newChat;
  messages.value.length = 0;
  // 重置文件上传状态
  resetFileUpload();
};

const selectChat = (chat: IConversation) => {
  if (chat.id === currentChat.value?.id) return;
  currentChat.value = chat;
  messages.value.length = 0;
  // 重置文件上传状态
  resetFileUpload();
  if (chat.status !== "") {
    getMessages(chat);
  }
};

// 获取对话名称
const getChatName = async (id: string) => {
  if (!id) return;
  try {
    const res: any = await post(`/installed-apps/${agentRecord.value?.id}/conversations/${id}/name`, {
      auto_generate: true,
    });
    let index = conversations.value.findIndex(item => item.id === id);
    if (index >= 0) {
      conversations.value[index].name = res.name;
    }
  } catch (error) {
    console.error("获取对话名称失败:", error);
  }
};

// 初始化 Marked 配置
marked.use({
  breaks: true,
  gfm: true,
});

// 安全解析函数
const safeMarkdownParse = (content: string): string => {
  const rawHtml = marked.parse(content) as string;
  const sanitized = DOMPurify.sanitize(rawHtml, {
    ALLOWED_TAGS: [
      "p",
      "pre",
      "code",
      "strong",
      "em",
      "blockquote",
      "ul",
      "ol",
      "li",
      "h1",
      "h2",
      "h3",
      "h4",
      "h5",
      "h6",
      "span",
    ],
    ALLOWED_ATTR: ["class"],
    FORBID_ATTR: ["style", "onerror"],
  });
  return sanitized;
};

// 为了保持一致性，添加一个将消息处理为一致格式的函数
const processMessage = (message: IMessage): IMessage => {
  if (!message.answer) return message;

  // 确保所有消息都有rawAnswer属性用于存储原始文本
  if (!message.rawAnswer) {
    message.rawAnswer = message.answer;
  }

  // 对所有消息使用一致的解析方法
  message.answer = safeMarkdownParse(message.rawAnswer);
  return message;
};

// 防抖高亮函数
const debouncedHighlight = debounce(() => {
  nextTick(() => {
    document.querySelectorAll("pre code").forEach(block => {
      if (!(block as HTMLElement).classList.contains("hljs")) {
        hljs.highlightElement(block as HTMLElement);
      }
    });
  });
}, 50);

// 添加防抖的滚动函数
const debouncedScrollToBottom = debounce(() => {
  scrollToBottom();
}, 100);

const handleSuggest = (value: string) => {
  inputMessage.value = value;
  sendMessage();
};

// 发送对话信息
const sendMessage = async () => {
  if (!inputMessage.value.trim()) return;
  suggestList.value.length = 0;

  let params: ChatParams = {
    conversation_id: "",
    parent_message_id: null,
    files: uploadedFiles.value,
    inputs: {},
    response_mode: "streaming",
    query: inputMessage.value,
  };

  if (messages.value?.length > 0) {
    params.conversation_id = messages.value[messages.value.length - 1]?.conversation_id || "";
    params.parent_message_id = messages.value[messages.value.length - 1]?.id || null;
  }

  isDone.value = false;

  try {
    // 添加用户问题到消息列表
    const newMessage: IMessage = {
      answer: "",
      rawAnswer: "",
      conversation_id: "",
      inputs: {},
      parent_message_id: null,
      query: inputMessage.value,
      message_files: uploadedFiles.value,
      status: "pending",
    };
    messages.value.push(newMessage);

    // 清空输入框和已上传文件列表
    inputMessage.value = "";
    resetFileUpload();

    // 发送请求
    await postStream(`/installed-apps/${agentRecord.value?.id}/chat-messages`, params, {
      onMessage: text => {
        // 处理服务器返回的data:前缀
        const lastMessage = messages.value[messages.value.length - 1];
        if (!lastMessage) return;

        // 将接收到的文本按换行符分割，处理每一条消息
        const messageLines = text.split("\n");
        let shouldUpdate = false;

        for (const line of messageLines) {
          if (!line.includes("data:")) continue;

          try {
            const jsonStr = line.replace(/^data: /, "").trim();
            if (!jsonStr) continue;

            const data = JSON.parse(jsonStr);
            if (data.event === "message_replace") continue;

            // 增量更新answer内容
            if (data.answer) {
              // 更新原始文本内容
              lastMessage.rawAnswer = (lastMessage.rawAnswer || "") + data.answer;
              shouldUpdate = true;

              // 收到第一条数据就更新状态
              if (lastMessage.status === "pending") {
                lastMessage.status = "normal";
              }
            }

            // 更新其他字段
            if (data.conversation_id) {
              lastMessage.conversation_id = data.conversation_id;
            }
            if (data.id) {
              lastMessage.id = data.id;
            }
          } catch (error) {
            console.error("解析响应数据失败:", error);
          }
        }

        // 实时更新渲染
        if (shouldUpdate && lastMessage.rawAnswer) {
          // 直接解析并更新显示内容
          lastMessage.answer = safeMarkdownParse(lastMessage.rawAnswer);
          // 触发代码高亮
          debouncedHighlight();
          // 滚动到底部
          debouncedScrollToBottom();
        }
      },
      onError: error => {
        isDone.value = true;
        console.error("发送消息失败:", error);
      },
      onComplete: async () => {
        const lastMessage = messages.value[messages.value.length - 1];
        if (lastMessage) {
          lastMessage.status = "normal";
          // 完成时再次解析整个内容，确保格式完整
          if (lastMessage.rawAnswer) {
            lastMessage.answer = safeMarkdownParse(lastMessage.rawAnswer);
            // 最后执行一次代码高亮
            debouncedHighlight();
          }
          if (currentChat.value) {
            currentChat.value.id = lastMessage?.conversation_id;
          }
        }
        if (messages.value.length === 1 && lastMessage?.conversation_id) {
          getConversations();
          getChatName(lastMessage.conversation_id);
        }
        if (messages.value.length === 1 && parameterRecord.value?.opening_statement) {
          const newMessage: IMessage = {
            answer: `<p>${parameterRecord.value?.opening_statement}</p>`,
            rawAnswer: "",
            conversation_id: "",
            inputs: {},
            parent_message_id: null,
            query: "",
          };
          messages.value.unshift(newMessage);
        }
        if (lastMessage?.id && parameterRecord.value?.suggested_questions_after_answer.enabled) {
          getSuggestList(lastMessage?.id);
        }
        isDone.value = true;
      },
    });
  } catch (error) {
    // 更新消息状态为失败
    const lastMessage = messages.value[messages.value.length - 1];
    if (lastMessage) {
      lastMessage.status = "failed";
    }
    isDone.value = true;
    console.error("发送消息失败:", error);
    ElMessage.error("发送消息失败，请重试");
  }
};

const handleCommand = async (command: "pin" | "unpin" | "rename" | "delete", chat: IConversation) => {
  switch (command) {
    case "pin":
    case "unpin":
      // 实现置顶/取消置顶逻辑
      try {
        await patch(`/installed-apps/${agentRecord.value?.id}/conversations/${chat.id}/${command}`);
        ElMessage.success(`${command === "pin" ? "置顶" : "取消置顶"}成功`);
        getConversations();
      } catch (error) {
        console.log(error);
      }
      break;
    case "rename":
      selectedChat.value = chat;
      showRenameDialog.value = true;
      break;
    case "delete":
      // 实现删除逻辑
      ElMessageBox.confirm("确定要删除此对话吗？删除后将无法恢复", "删除确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          await del(`/installed-apps/${agentRecord.value?.id}/conversations/${chat.id}`);
          messages.value.length = 0;
          // 重置文件上传状态
          resetFileUpload();
          if (currentChat.value?.id === chat.id) {
            currentChat.value = null;
          }
          ElMessage.success("删除成功");
          await getConversations();
          if (currentChat.value?.id) {
            getMessages(currentChat.value);
          }
        } catch (error) {
          console.log(error);
        }
      });
      break;
  }
};

const handleRename = async (newName: string) => {
  try {
    await post(`/installed-apps/${agentRecord.value?.id}/conversations/${selectedChat.value?.id}/name`, {
      name: newName,
    });
    ElMessage.success("重命名成功");
    getConversations();
    selectedChat.value = null;
  } catch (error) {
    console.log(error);
  }
};

// 添加滚动到底部的方法
const scrollToBottom = () => {
  if (messageListRef.value) {
    messageListRef.value.scrollTo({
      top: messageListRef.value.scrollHeight,
      behavior: "smooth",
    });
  }
};

// 文件上传相关
// 上传文件前的验证
const beforeFileUpload = (file: File) => {
  // 检查文件类型
  const fileExtension = "." + file.name.split(".").pop()?.toUpperCase();
  const allowedExtensions = allowedFileExtensions.value.split(",");
  if (!allowedExtensions.includes(fileExtension)) {
    ElMessage.warning(`不支持的文件类型，请上传${allowedFileExtensions.value}格式的文件`);
    return false;
  }

  // 检查上传数量限制
  const maxLimit = agentAppRecord.value?.model_config?.file_upload?.number_limits || 3;
  // 当前已上传的文件数量 + 1（当前文件）
  if (uploadedFiles.value.length >= maxLimit) {
    ElMessage.warning(`最多只能上传${maxLimit}个文件`);
    return false;
  }

  // 检查文件大小，默认限制为15MB
  const maxSize = 15 * 1024 * 1024;
  if (file.size > maxSize) {
    ElMessage.warning("文件大小不能超过15MB");
    return false;
  }

  return true;
};

// 存储已上传文件的信息
const uploadedFiles = ref<UploadedFile[]>([]);

// 处理超出文件数量限制
const handleExceed = (files: File[]) => {
  const maxLimit = agentAppRecord.value?.model_config?.file_upload?.number_limits || 3;
  const currentCount = uploadedFiles.value.length;
  const selectedCount = files.length;

  // 如果当前文件数量为0但仍然触发了exceed，说明组件内部状态可能不一致
  if (currentCount === 0) {
    // 重置组件状态
    resetFileUpload();

    // 尝试上传第一个文件
    if (files.length > 0 && uploadRef.value) {
      const file = files[0] as any;
      uploadRef.value.handleStart(file);
      return;
    }
  }

  ElMessage.warning(
    `当前已上传${currentCount}个文件，本次选择了${selectedCount}个文件，超出了最大上传限制${maxLimit}个`
  );
};

// 删除已上传的文件
const removeFile = (index: number) => {
  // 获取要删除的文件信息
  const fileToRemove = uploadedFiles.value[index];
  // 从数组中移除
  uploadedFiles.value.splice(index, 1);

  // 同时更新 Element Plus Upload 组件的内部状态
  if (uploadRef.value && fileToRemove) {
    // 由于我们使用的是自定义上传，Upload 组件内部可能没有对应的文件对象
    // 最安全的方式是重置整个上传组件状态
    uploadRef.value.clearFiles();

    // 如果需要保留其他文件，可以在这里重新添加剩余的文件到 Upload 组件
    // 但由于我们的实现方式，这里不需要额外操作
  }
};

// 重置文件上传状态
const resetFileUpload = () => {
  uploadedFiles.value = [];
  // 重置 Element Plus Upload 组件的内部状态
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

// 处理文件上传
const handleFileUpload = async (options: any) => {
  const { file } = options;
  try {
    // 检查是否已经达到上传限制
    const maxLimit = agentAppRecord.value?.model_config?.file_upload?.number_limits || 3;
    if (uploadedFiles.value.length >= maxLimit) {
      ElMessage.warning(`最多只能上传${maxLimit}个文件`);
      return;
    }

    // 使用封装好的uploadFile方法上传文件
    const response = await uploadFile<any>("/files/upload", file);
    if (response && response.id) {
      // 确定文件类型（简单判断，可以根据实际需求调整）
      const fileType = file.type.startsWith("image/") ? "image" : "document";
      // 创建文件对象并添加到列表中
      // 如果是图片，转换为base64用于预览
      let url = "";
      if (fileType === "image") {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = e => {
          if (e.target && e.target.result) {
            url = e.target.result as string;
            // 更新已上传文件的url
            const index = uploadedFiles.value.findIndex(f => f.upload_file_id === response.id);
            if (index !== -1) {
              uploadedFiles.value[index].fileUrl = url;
            }
          }
        };
      }

      // 创建上传文件对象
      const uploadedFile: UploadedFile = {
        transfer_method: "local_file",
        type: fileType,
        upload_file_id: response.id,
        url: url,
        fileUrl: fileType === "image" ? url : undefined,
        name: file.name,
        size: file.size,
      };

      // 添加到上传文件列表
      uploadedFiles.value.push(uploadedFile);
    } else {
      ElMessage.error("文件上传失败");
    }
  } catch (error) {
    ElMessage.error("文件上传失败，请重试");
  }
};

onUnmounted(() => {
  debouncedHighlight.cancel();
  debouncedScrollToBottom.cancel();
});

// 返回
const goBack = () => {
  router.go(-1);
};
</script>

<style lang="scss" scoped>
.chat-container {
  display: flex;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-image: url("@/assets/images/ic_qa_bg.png");
  background-size: cover;
  background-repeat: no-repeat;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.header .back {
  cursor: pointer;
  font-size: 20px;
  margin-right: 10px;
}

.header .back:hover {
  color: #409eff;
}

.header .title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.qa-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.conversation-list {
  width: 300px;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px 16px;
}

.new-chat-btn {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background: #ebf0ff;
  border-radius: 12px 12px 12px 12px;
  border: 1px solid #3a67f8;
  cursor: pointer;
  .new-chat-btn-img {
    width: 28px;
    height: 28px;
    overflow: hidden;
  }
  .new-chat-btn-title {
    font-weight: 400;
    font-size: 16px;
    color: #3a67f8;
    line-height: 19px;
    margin-left: 8px;
  }
}

.margin-line {
  width: 100%;
  height: 1px;
  background-color: #e8e5e5;
  margin: 24px 0;
}

.chat-list {
  flex: 1;
  overflow-y: auto;
}

.chat-desc {
  font-size: 14px;
  color: #676f83;
  margin-bottom: 12px;
  padding-left: 6px;
}

.chat-item {
  cursor: pointer;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
  &.active {
    background: #ffffff;
    box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
  }
}

.chat-item-content {
  padding: 16px;
  display: flex;
  align-items: center;
}

.chat-info {
  flex: 1;
  overflow: hidden;
  display: flex;
  align-items: center;
  .chat-img {
    width: 16px;
    height: 16px;
    overflow: hidden;
  }
  .chat-title {
    font-weight: 400;
    font-size: 14px;
    color: #4a4a4a;
    line-height: 16px;
    margin-left: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.more-btn {
  &:hover {
    background-color: #f0f2f5;
    border-radius: 4px;
  }

  :deep(.el-icon) {
    font-size: 18px;
    color: #666;
    transform: rotate(90deg);
  }
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 36px;
  padding-top: 24px;
}

.message-item {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.message-content {
  display: flex;
  .avatar {
    width: 40px;
    height: 40px;
    margin-top: 8px;
    img {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
    :deep(.el-icon) {
      font-size: 24px;
      margin-left: 8px;
    }
  }
  .content-box {
    flex: 1;
    overflow: hidden;
    display: flex;
    .content {
      display: inline-block;
      max-width: 100%;
      overflow: hidden;
      padding: 0 20px;
      font-weight: 400;
      font-size: 16px;
      color: #000000;
      line-height: 19px;
      border-radius: 12px;
      .reset-content * {
        all: revert;
      }
    }
  }
}
.query-file {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}
.content-file {
  width: 64px;
  height: 64px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
  .content-file-image {
    width: 64px;
    height: 64px;
    overflow: hidden;
  }
}

.question {
  align-items: flex-end;
  .message-content {
    flex-direction: row-reverse;
    .content-box {
      justify-content: flex-end;
      .content {
        padding: 20px;
        background-color: #3f56f0;
        margin-right: 16px;
        color: #fff;
      }
    }
  }
}

.answer {
  margin-top: 20px;
  align-items: flex-start;
  .content {
    background-color: #fff;
    margin-left: 16px;
  }
}

.input-area {
  margin-top: 20px;
}

.input-box {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 12px;
  background-color: #fff;
  margin-bottom: 16px;

  .file-box {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 10px;

    .file-item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      .file-item-image {
        width: 68px;
        height: 68px;
        background-color: #fff;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
        .file-image {
          width: 64px;
          height: 64px;
          object-fit: contain;
        }
      }
      .delete-icon {
        position: absolute;
        top: 2px;
        right: 2px;
        width: 16px;
        height: 16px;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        opacity: 0;
        transition: opacity 0.3s;

        :deep(.el-icon) {
          font-size: 12px;
          color: #fff;
        }
      }

      &:hover {
        .delete-icon {
          opacity: 1;
        }
      }
    }
  }

  :deep(.el-textarea) {
    .el-textarea__inner {
      border: none;
      padding: 0;
      font-size: 14px;
      line-height: 1.6;
      background-color: #fff;
      box-shadow: none;
      outline: none;
      &:focus {
        border-color: transparent;
        box-shadow: none;
        outline: none;
      }
    }
  }
}
.send-box {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .send-file-upload {
    :deep(.el-upload) {
      display: block;
    }
  }
  .send-file {
    padding: 8px;
    font-size: 20px;
    border: none;
    &:hover {
      box-shadow: 0px 0px 10px rgba(64, 158, 255, 0.2);
    }
  }
  .send-btn {
    width: 32px;
    height: 32px;
    overflow: hidden;
    cursor: pointer;
    margin-left: 12px;
  }
}

.loading-dots {
  display: inline-block;
  padding: 20px;
  font-weight: 400;
  font-size: 16px;
  color: #000000;
  line-height: 19px;
  span {
    display: inline-block;
    animation: dotFade 1.4s infinite;
    opacity: 0;
    &:nth-child(1) {
      animation-delay: 0.2s;
    }

    &:nth-child(2) {
      animation-delay: 0.4s;
    }

    &:nth-child(3) {
      animation-delay: 0.6s;
    }
  }
}

@keyframes dotFade {
  0%,
  100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

.open-box {
  width: 60%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  .open-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .open-text {
    margin-top: 12px;
    line-height: 20px;
    word-break: break-all;
  }
}

.suggested-question {
  width: 100%;
  display: flex;
  .suggested-question-left {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .suggested-question-right {
    flex: 1;
    overflow: hidden;
    margin-left: 12px;
    padding: 12px;
    background-color: #fff;
    border-radius: 10px;
    padding-top: 0;
    padding-bottom: 0;
  }
}

.suggest-box {
  padding: 0 12px;
  padding-top: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .suggest-title {
    font-size: 14px;
  }
}
:deep(.el-divider__text) {
  background-color: transparent;
}

.query-document {
  width: 144px;
  height: 68px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
  padding: 6px;
  display: flex;
  flex-direction: column;
  margin-right: 4px;
  .query-document-name {
    flex: 1;
    font-size: 12px;
    color: #333;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 4px;
    line-height: 1.2;
    max-height: 2.4em;
  }
  .query-document-details {
    display: flex;
    align-items: center;
    .query-document-icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
    .query-document-ext {
      font-size: 10px;
      color: #666;
      margin-right: 6px;
    }
    .query-document-size {
      font-size: 10px;
      color: #999;
    }
  }
}
.suggested-question-button {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-top: 8px;
}
.content-suggest-button {
  margin-right: 8px;
  margin-bottom: 8px;
  :deep(.el-button + .el-button) {
    margin-right: 0;
  }
}
</style>
