<template>
  <div class="tag-filter">
    <el-popover
      placement="bottom-start"
      trigger="click"
      :width="320"
      popper-class="tag-popover"
    >
      <template #reference>
        <el-button class="tag-button" size="large">
          <el-icon><Files /></el-icon>
          <span>标签</span>
          <el-icon v-if="modelValue.length > 0" class="tag-count">{{ modelValue.length }}</el-icon>
          <el-icon><ArrowDown /></el-icon>
        </el-button>
      </template>
      
      <div class="tag-filter-container">
        <div class="tag-filter-header">
          <span class="tag-filter-title">标签筛选</span>
          <el-button type="text" @click="clearTags">清除</el-button>
        </div>
        
        <div class="tag-filter-search">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索标签"
            :prefix-icon="Search"
            clearable
          />
        </div>
        
        <div class="tag-filter-content">
          <el-checkbox-group v-model="selectedTags" @change="handleTagChange">
            <div v-for="tag in filteredTags" :key="tag.id" class="tag-item">
              <el-checkbox :label="tag.id">{{ tag.name }}</el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
        
        <div v-if="filteredTags.length === 0" class="tag-empty">
          <el-empty description="无匹配标签" :image-size="60" />
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Search, Files, ArrowDown } from '@element-plus/icons-vue';

defineOptions({
  name: 'TagFilter',
});

const props = defineProps<{
  tags: any[];
  modelValue: string[];
}>();

const emits = defineEmits<{
  (e: 'update:modelValue', value: string[]): void;
  (e: 'change', value: string[]): void;
}>();

const searchKeyword = ref('');
const selectedTags = ref<string[]>([]);

// 监听外部modelValue变化
watch(() => props.modelValue, (newVal) => {
  selectedTags.value = [...newVal];
}, { immediate: true });

// 监听内部selectedTags变化，同步到外部
watch(() => selectedTags.value, (newVal) => {
  emits('update:modelValue', newVal);
  emits('change', newVal);
});

// 过滤标签列表
const filteredTags = computed(() => {
  if (!searchKeyword.value) return props.tags;
  return props.tags.filter(tag => 
    tag.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  );
});

// 清除所有选中标签
const clearTags = () => {
  selectedTags.value = [];
};

// 标签变化处理
const handleTagChange = (values: string[]) => {
  selectedTags.value = values;
};
</script>

<style scoped lang="scss">
.tag-filter {
  .tag-button {
    display: flex;
    align-items: center;
    gap: 4px;
    
    .tag-count {
      margin-left: 4px;
      font-size: 14px;
      padding: 0 4px;
      background-color: #e6f4ff;
      color: #4080ff;
      border-radius: 10px;
      min-width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.tag-filter-container {
  padding: 12px;
  
  .tag-filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .tag-filter-title {
      font-weight: 500;
      font-size: 16px;
    }
  }
  
  .tag-filter-search {
    margin-bottom: 16px;
  }
  
  .tag-filter-content {
    max-height: 240px;
    overflow-y: auto;
    
    .tag-item {
      margin-bottom: 8px;
    }
  }
  
  .tag-empty {
    padding: 20px 0;
  }
}
</style>

<style>
.tag-popover {
  padding: 0;
}
</style>
