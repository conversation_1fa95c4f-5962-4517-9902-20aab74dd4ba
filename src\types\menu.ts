import type { Component } from "vue";

export interface UserInfo {
  username: string;
  name?: string;
  [key: string]: any;
}

export interface SubMenu {
  title: string;
  path: string;
  icon: Component;
  url?: string;
  activeUrl?: string;
}

export interface MainMenu {
  id: string;
  title: string;
  icon?: Component;
  path?: string;
  children?: SubMenu[];
  url?: string;
  activeUrl?: string;
}
