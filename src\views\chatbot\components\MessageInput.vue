<template>
  <div class="input-area">
    <div class="input-box">
      <el-input
        v-model.trim="inputContent"
        type="textarea"
        :rows="3"
        placeholder="输入您的问题..."
        resize="none"
        :disabled="disabled || sending"
        @keydown.enter.prevent="handleSend"
      />
      <img
        class="send-btn"
        v-show="inputContent && !disabled && !sending"
        src="@/assets/images/ic_qasend_active.png"
        alt=""
        @click="handleSend"
      />
      <img 
        class="send-btn" 
        v-show="!inputContent || disabled || sending" 
        src="@/assets/images/ic_qasend.png" 
        alt="" 
      />
    </div>
  </div>
</template>

<script lang="ts">
import { ref, computed, onMounted, defineComponent } from 'vue';

export default defineComponent({
  name: 'MessageInput',
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['send'],
  setup(props, { emit }) {
    // 输入内容
    const inputContent = ref('');
    const sending = ref(false);

    // 是否可以发送
    const canSend = computed(() => {
      return inputContent.value.trim().length > 0 && !props.disabled && !sending.value;
    });

    // 发送消息
    const handleSend = async () => {
      if (!canSend.value) return;
      
      const content = inputContent.value.trim();
      if (!content) return;
      
      sending.value = true;
      try {
        emit('send', content);
        inputContent.value = '';
      } finally {
        sending.value = false;
      }
    };

    // 组件挂载后聚焦输入框
    onMounted(() => {
      const textarea = document.querySelector('.input-box textarea');
      if (textarea && !props.disabled) {
        (textarea as HTMLTextAreaElement).focus();
      }
    });

    return {
      inputContent,
      sending,
      handleSend
    };
  }
});
</script>

<style scoped>
.input-area {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
  background-color: #fff;
}

.input-box {
  position: relative;
}

.input-box :deep(.el-textarea__inner) {
  padding-right: 50px;
  border-radius: 8px;
  resize: none;
  border-color: #e5e7eb;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  font-size: 14px;
}

.input-box :deep(.el-textarea__inner:focus) {
  border-color: #409eff;
}

.send-btn {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  cursor: pointer;
}
</style>
