<template>
  <div class="structure-container">
    <!-- <div class="page-title">组织架构</div> -->
    <div class="page-content">
      <div class="page-left">
        <div class="org-tree-container">
          <div class="tree-header">
            <span class="tree-title">组织架构</span>
            <el-button type="primary" link :icon="Plus" @click="handleAdd" />
          </div>
          <div class="tree-content">
            <el-tree
              :data="treeData"
              node-key="id"
              :props="defaultProps"
              :expand-on-click-node="false"
              default-expand-all
              highlight-current
              :current-node-key="currentNodeKey"
              @node-click="handleNodeClick"
              empty-text="暂无数据"
            >
              <template #default="{ node, data }">
                <div class="custom-tree-node">
                  <span>{{ node.label }}</span>
                  <span class="node-actions">
                    <el-button type="primary" link :icon="Edit" @click="handleEdit(data)" />
                    <el-button type="primary" link :icon="Delete" @click="handleDelete(data)" />
                    <el-button type="primary" link :icon="Plus" @click="handleAdd(data)" />
                  </span>
                </div>
              </template>
            </el-tree>
          </div>
        </div>
      </div>
      <div class="page-right">
        <template v-if="currentOrg">
          <div class="structure-header">
            <img class="header-img" src="@/assets/images/logo.png" alt="logo" />
            <div class="header-title">{{ currentOrg?.name }}</div>
            <el-button type="primary" :icon="Plus" size="large" @click="handleOpen">添加</el-button>
          </div>
          <div class="structure-list">
            <el-table :data="tableData" style="width: 100%" empty-text="暂无数据">
              <el-table-column label="姓名" min-width="100">
                <template #default="scope">
                  <div class="name-box">
                    <Avatar :avatarUrl="scope.row.avatarUrl" :name="scope.row.name" />
                    <div class="name-right">
                      <div class="name-value">
                        {{ scope.row.name
                        }}<el-text class="ml-4" type="warning" v-if="scope.row.status === 'pending'">待定...</el-text>
                      </div>
                      <div class="email-value">{{ scope.row.email }}</div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="上次活动时间">
                <template #default="scope">
                  {{ formatRelativeTime(scope.row.last_active_at) }}
                </template>
              </el-table-column>
              <el-table-column label="角色">
                <template #default="scope">
                  <el-popover
                    placement="bottom"
                    :width="310"
                    trigger="click"
                    v-if="
                      (currentRole === 'owner' || currentRole === 'admin') &&
                      (scope.row.role == 'editor' || scope.row.role == 'normal')
                    "
                  >
                    <ul class="role-list">
                      <li
                        class="role-item"
                        v-for="item in roleList"
                        :key="item.id"
                        @click="handleChangeRole(scope.row, item)"
                      >
                        <el-icon color="#1890ff" :class="[scope.row.role == item.role ? 'is-show' : 'un-show']"
                          ><Check
                        /></el-icon>
                        <div class="role-item-right">
                          <div class="role-title">{{ item.title }}</div>
                          <div class="role-text">{{ item.text }}</div>
                        </div>
                      </li>
                    </ul>
                    <template #reference>
                      <el-button>
                        {{ RoleEnum[scope.row.role as keyof typeof RoleEnum] }}
                        <el-icon><ArrowDown /></el-icon>
                      </el-button>
                    </template>
                  </el-popover>
                  <div class="role-value" v-else>{{ RoleEnum[scope.row.role as keyof typeof RoleEnum] }}</div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </div>
    </div>
    <MemberAdd
      v-model:visible="dialogVisible"
      :organization-id="currentNodeKey"
      @refresh="() => currentNodeKey && getMemberList(currentNodeKey)"
    />
    <OrganizationDialog
      v-model:visible="orgDialogVisible"
      :is-edit="isEditOrg"
      :organization="currentOrg"
      :parent-id="parentId"
      :parent-name="parentName"
      @refresh="getOrganizationTree"
    />
  </div>
</template>

<script setup lang="ts">
import { Plus, ArrowDown, Check, Edit, Delete } from "@element-plus/icons-vue";
import { ref, onMounted, computed } from "vue";
import type { MemberItem, Organization, OrganizationResponse } from "./types";
import { get, del, put } from "@/utils/request";
import Avatar from "@/components/Avatar.vue";
import { formatRelativeTime } from "@/utils/time";
import { RoleEnum } from "./types";
import MemberAdd from "./components/MemberAdd.vue";
import OrganizationDialog from "./components/OrganizationDialog.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getUser } from "@/utils/user";

const tableData = ref<MemberItem[]>([]);
const organizations = ref<Organization[]>([]);
const userInfo: any = getUser();

const currentRole = computed(() => {
  return tableData.value.filter(item => item.id === userInfo.id)[0].role || "normal";
});

// 控制成员添加弹窗显示隐藏
const dialogVisible = ref(false);

// 控制组织架构弹窗显示隐藏
const orgDialogVisible = ref(false);
// 是否为编辑模式
const isEditOrg = ref(false);
// 当前编辑的组织
const currentOrg = ref<Organization | null>(null);
// 父组织ID
const parentId = ref<string | null>(null);
// 父组织名称
const parentName = ref<string | null>(null);

// 当前选中的节点
const currentNodeKey = ref<string | null>(null);

// 树形组件配置
const defaultProps = {
  children: "children",
  label: "name",
};

// 将组织数据转换为树形组件所需的格式
const treeData = computed(() => {
  return organizations.value;
});

// 处理节点点击事件
const handleNodeClick = (data: Organization) => {
  // 设置当前选中节点的ID
  currentNodeKey.value = data.id;
  // 设置当前选中的组织数据
  currentOrg.value = data;

  // 获取该组织的成员列表
  getMemberList(data.id);
};

// 编辑组织或部门
const handleEdit = (data: Organization) => {
  // 设置编辑模式
  isEditOrg.value = true;
  // 设置当前编辑的组织
  currentOrg.value = data;
  // 清空父组织信息（编辑模式不需要显示父组织）
  parentId.value = null;
  parentName.value = null;
  // 设置当前选中节点
  currentNodeKey.value = data.id;
  // 打开弹窗
  orgDialogVisible.value = true;
};

// 删除组织或部门
const handleDelete = async (data: Organization) => {
  try {
    // 使用 ElMessageBox 进行确认
    await ElMessageBox.confirm(`确定要删除组织「${data.name}」吗？删除后无法恢复。`, "删除确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    // 调用删除接口
    await del(`/workspaces/current/organizations/${data.id}`);
    ElMessage.success("删除成功");

    // 刷新组织架构树
    getOrganizationTree();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败:", error);
    }
  }
};

// 添加组织（可以是顶级组织或子组织）
const handleAdd = (data?: Organization) => {
  // 设置为添加模式
  isEditOrg.value = false;
  // 清空当前组织
  currentOrg.value = null;

  if (data) {
    // 如果有data参数，表示添加子组织
    parentId.value = data.id;
    parentName.value = data.name;
    // 设置当前选中节点
    currentNodeKey.value = data.id;
  } else {
    // 如果没有data参数，表示添加顶级组织
    parentId.value = null;
    parentName.value = null;
  }

  // 打开弹窗
  orgDialogVisible.value = true;
};

// 获取成员列表
const getMemberList = async (organizationId?: string) => {
  try {
    if (organizationId) {
      // 如果有组织ID，获取该组织的成员列表
      let res: { accounts: MemberItem[] } = await get(`/workspaces/current/organizations/${organizationId}/members`);
      tableData.value = res.accounts;
    } else {
      // 兼容原有代码，如果没有组织ID，获取所有成员
      let res: { accounts: MemberItem[] } = await get("/workspaces/current/members");
      tableData.value = res.accounts;
    }
  } catch (error) {
    console.error("获取成员列表失败:", error);
    tableData.value = [];
  }
};

// 获取组织架构树形数据
const getOrganizationTree = async () => {
  try {
    const res = await get<OrganizationResponse>("/workspaces/current/organizations/tree");
    organizations.value = res.organizations;

    // 初始化时不需要默认高亮第一个节点

    // 如果当前有选中的节点，更新currentOrg
    if (currentNodeKey.value) {
      // 递归查找当前选中的组织
      const findOrganization = (orgs: Organization[]): Organization | null => {
        for (const org of orgs) {
          if (org.id === currentNodeKey.value) {
            return org;
          }
          if (org.children && org.children.length > 0) {
            const found = findOrganization(org.children);
            if (found) return found;
          }
        }
        return null;
      };

      const updatedOrg = findOrganization(organizations.value);
      if (updatedOrg) {
        // 找到了对应的组织，更新currentOrg
        currentOrg.value = updatedOrg;
      } else {
        // 没有找到对应的组织（可能已被删除），清空currentOrg和currentNodeKey
        currentOrg.value = null;
        currentNodeKey.value = null;
        tableData.value = [];
      }
    }
  } catch (error) {
    console.error("获取组织架构树形数据失败：", error);
  }
};

// 打开添加成员弹窗
const handleOpen = () => {
  dialogVisible.value = true;
};

const roleList = [
  {
    id: 1,
    title: "编辑",
    text: "能够建立并编辑应用程序，不能管理团队设置",
    role: "editor",
  },
  {
    id: 2,
    title: "成员",
    text: "只能使用应用程序，不能建立应用程序",
    role: "normal",
  },
  {
    id: 3,
    title: "移出团队",
    text: "将取消团队访问",
  },
];

// 处理角色变更
const handleChangeRole = async (member: MemberItem, item: any) => {
  if (item.id == 3) {
    await del(`/workspaces/current/members/${member.id}`);
    ElMessage.success("修改成功");
    if (currentNodeKey.value) {
      getMemberList(currentNodeKey.value); // 刷新当前选中组织的成员列表
    }
  } else {
    await put(`/workspaces/current/members/${member.id}/update-role`, { role: item.role });
    ElMessage.success("修改成功");
    if (currentNodeKey.value) {
      getMemberList(currentNodeKey.value); // 刷新当前选中组织的成员列表
    }
  }
};

onMounted(() => {
  // 只获取组织架构树，不自动获取成员列表
  getOrganizationTree();

  // 清空成员列表
  tableData.value = [];
});
</script>

<style lang="scss" scoped>
.structure-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}
.page-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  background-color: #f8fafc;
  .page-left {
    width: 400px;
    padding: 16px;
    background-color: #fff;
    margin-right: 16px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .org-tree-container {
      height: 100%;
      display: flex;
      flex-direction: column;

      .tree-header {
        padding-bottom: 16px;
        border-bottom: 1px solid #ebeef5;
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .tree-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }

      .tree-content {
        flex: 1;
        overflow-y: auto;
      }
    }

    .custom-tree-node {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 15px;
      padding-right: 8px;
      height: 40px; /* 增加高度 */

      .node-actions {
        display: none;
      }

      &:hover .node-actions {
        display: inline-flex;
      }
    }

    /* 调整树形组件的样式 */
    :deep(.el-tree-node__content) {
      height: 44px;
      line-height: 44px;
      padding: 0 8px;
      border-radius: 4px;
      margin-bottom: 4px;

      &:hover {
        background-color: #f5f7fa;
      }
    }

    :deep(.el-tree-node__expand-icon) {
      font-size: 16px;
      padding: 8px;
    }

    :deep(.el-tree-node__label) {
      font-size: 15px;
      font-weight: 500;
    }

    :deep(.el-button.is-link) {
      font-size: 16px;
      margin-left: 8px;
    }
  }
  .page-right {
    padding: 16px;
    background-color: #fff;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
}
:deep(.el-card) {
  height: 100%;
  .el-card__body {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}
.structure-header {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #f5f7f9;
  border-radius: 10px;
  .header-img {
    width: 50px;
    height: 50px;
    overflow: hidden;
  }
  .header-title {
    margin: 0 20px;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    color: #354052;
    flex: 1;
  }
}
.structure-list {
  flex: 1;
  overflow-y: auto;
  margin-top: 20px;
}
.name-box {
  display: flex;
  align-items: center;
  .name-right {
    margin-left: 16px;
    .name-value {
      color: #354052;
      font-size: 14px;
    }
    .email-value {
      color: #676f83;
      font-size: 12px;
    }
  }
}
.ml-4 {
  margin-left: 4px;
}
.role-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  padding: 6px;
  cursor: pointer;
  &:hover {
    background-color: #f5f7f9;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .un-show {
    opacity: 0;
  }
  .is-show {
    opacity: 1;
  }
  .role-item-right {
    margin-left: 10px;
    .role-title {
      font-size: 14px;
      font-weight: 600;
    }
    .role-text {
      font-size: 12px;
      color: #676f83;
    }
  }
}
</style>
