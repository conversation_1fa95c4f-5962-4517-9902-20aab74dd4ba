import { getItem, removeItem, setItem } from "./storage";
import { StorageKey } from "@/enums/storage";

// 用户信息
export const getUser = () => getItem(StorageKey.USER);
export const setUser = (user: any) => setItem(StorageKey.USER, user);
export const removeUser = () => removeItem(StorageKey.USER);

// 令牌
export const getToken = () => getItem(StorageKey.TOKEN);
export const setToken = (token: string) => setItem(StorageKey.TOKEN, token);
export const removeToken = () => removeItem(StorageKey.TOKEN);

// 刷新令牌
export const getRefreshToken = () => getItem(StorageKey.REFRESH_TOKEN);
export const setRefreshToken = (refreshToken: string) => setItem(StorageKey.REFRESH_TOKEN, refreshToken);
export const removeRefreshToken = () => removeItem(StorageKey.REFRESH_TOKEN);
