/**
 * 设置 localStorage
 * @param key 键
 * @param value 值
 */
export const setItem = <T>(key: string, value: T): void => {
  localStorage.setItem(key, JSON.stringify(value));
};

/**
 * 获取 localStorage
 * @param key 键
 * @returns 存储的值
 */
export const getItem = <T>(key: string): T | null => {
  const value = localStorage.getItem(key);
  if (!value) return null;

  try {
    return JSON.parse(value);
  } catch (error) {
    return null;
  }
};

/**
 * 移除 localStorage
 * @param key 键
 */
export const removeItem = (key: string): void => {
  localStorage.removeItem(key);
};

/**
 * 清空所有 localStorage
 */
export const clear = (): void => {
  localStorage.clear();
};

/**
 * 获取localStorage中所有的key
 * @returns 所有的key数组
 */
export const getAllKeys = (): string[] => {
  return Object.keys(localStorage);
};

/**
 * 判断是否存在某个key
 * @param key 键
 * @returns boolean
 */
export const hasItem = (key: string): boolean => {
  return localStorage.getItem(key) !== null;
};
