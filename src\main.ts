import { createApp } from "vue";
import "./styles/index.scss";
import App from "./App.vue";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import router from "./router";
import svgIconPlugin from "./plugins/svgIcon";
import { injectSvgStyles } from "./utils/svgProcessor";
import VueLinkify from "vue-linkify";

const app = createApp(App);
app.use(ElementPlus);
app.use(router);
app.use(svgIconPlugin);
app.directive("linkify", VueLinkify);

// 注入SVG样式，确保SVG图标能够继承颜色
injectSvgStyles();

app.mount("#app");
