import { ListPageResponse, ListPageRequest } from "@/types/common";

// 工作流列表请求参数
export interface WorkflowListRequest extends ListPageRequest {
  name?: string | null;
  is_created_by_me?: boolean | null;
  mode?: string | null;
}

// 工作流记录
export interface WorkflowRecord {
  created_at: number;
  created_by: string;
  description: string | null;
  icon: string | null;
  icon_background: string;
  icon_type: string;
  icon_url: string | null;
  id: string;
  max_active_requests: string | null;
  mode: string;
  name: string;
  updated_at: number;
  updated_by: string;
  use_icon_as_answer_icon: boolean;
  workflow: unknown;
  model_config: unknown;
  tags: unknown;
}

// 工作流列表响应
export type WorkflowListResponse = ListPageResponse<WorkflowRecord[]>;

// 创建工作流请求参数
export interface CreateWorkflowRequest {
  name: string;
  description?: string;
  icon?: string;
  icon_background?: string;
  icon_type: string;
  mode: string;
}

// 文件上传响应
export interface FileUploadResponse {
  id: string;
  name: string;
  size: number;
  url: string;
  type: string;
}