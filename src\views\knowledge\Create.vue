<template>
  <div class="create-knowledge-container">
    <div class="header">
      <div class="back" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
      </div>
      <div class="title">创建知识库</div>
    </div>

    <div class="steps-container">
      <el-steps :active="currentStep" finish-status="success">
        <el-step title="上传数据" />
        <el-step title="数据分析集成" />
        <el-step title="完成创建" />
      </el-steps>
    </div>

    <div v-if="currentStep === 1" class="step-content">
      <div class="upload-options">
        <div class="option-card active">
          <div class="icon-container">
            <el-icon><Document /></el-icon>
          </div>
          <div class="option-title">上传本地文档</div>
        </div>
        <!-- <div class="option-card">
          <div class="icon-container notion-icon">
            <span class="notion-logo">N</span>
          </div>
          <div class="option-title">同步 Notion 内容</div>
        </div>
        <div class="option-card">
          <div class="icon-container">
            <el-icon><Link /></el-icon>
          </div>
          <div class="option-title">同步 Web 站点内容</div>
        </div> -->
        <div class="option-card" @click="createEmptyKnowledgeBase">
          <div class="icon-container">
            <el-icon><Plus /></el-icon>
          </div>
          <div class="option-title">创建空知识库</div>
        </div>
      </div>

      <div class="upload-section">
        <div class="quick-upload">
          <div class="section-title">快速上传</div>
          <div 
            class="upload-area" 
            :class="{ 'dragging': dragging }" 
            @click="triggerFileUpload" 
            @dragenter="handleDragEnter" 
            @dragover.prevent 
            @dragleave="handleDragLeave" 
            @drop="handleDrop"
          >
            <div class="upload-icon">
              <el-icon><Upload /></el-icon>
            </div>
            <div class="upload-tip">点击上传或将文件拖拽到此区域</div>
            <div class="upload-format">
              支持格式: PDF / DOCX / XLSX / PPTX / TXT / MD / JPG / PNG (单文件 &lt; 100MB)
            </div>
            <input 
              ref="fileInput" 
              type="file" 
              multiple 
              accept=".pdf,.docx,.xlsx,.pptx,.txt,.md,.jpg,.png" 
              style="display: none" 
              @change="handleFileSelect"
            />
          </div>
        </div>

        <div class="uploaded-files">
          <div class="section-title">已选文件</div>
          <div class="file-list">
            <div v-for="(file, index) in fileList" :key="index" class="file-item">
              <div class="file-icon">
                <el-icon v-if="file.type.includes('pdf')"><Document color="#E74C3C" /></el-icon>
                <el-icon v-else-if="file.type.includes('doc')"><Document color="#3498DB" /></el-icon>
                <el-icon v-else-if="file.type.includes('xls')"><Document color="#2ECC71" /></el-icon>
                <el-icon v-else><Document /></el-icon>
              </div>
              <div class="file-info">
                <div class="file-name">{{ file.name }}</div>
                <div class="file-size">{{ formatFileSize(file.size) }}</div>
              </div>
              <div class="file-status">
                <el-tag type="success" v-if="file.status === 'success'">
                  <el-icon><Check /></el-icon> 已上传
                </el-tag>
                <el-tag type="warning" v-else-if="file.status === 'uploading'">
                  <el-icon><Loading /></el-icon> 上传中 {{ file.progress }}%
                </el-tag>
                <el-tag type="danger" v-else-if="file.status === 'error'">
                  <el-icon><Close /></el-icon> 上传失败
                </el-tag>
              </div>
              <div class="file-actions">
                <el-icon class="action-icon"><Document /></el-icon>
                <el-icon class="action-icon" @click="removeFile(file.id)"><Delete /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="currentStep === 2" class="step-content">
      <div class="config-container">
        <div class="left-panel">
          <div class="section-card">
            <div class="section-title">分段设置</div>
            <div class="tab-container">
              <div class="tab-header">
                <div 
                  class="tab-item" 
                  :class="{ active: processMode === 'custom' }"
                  @click="processMode = 'custom'"
                >
                  通用分段设置
                </div>
                <div 
                  class="tab-item" 
                  :class="{ active: processMode === 'hierarchical' }"
                  @click="processMode = 'hierarchical'"
                >
                  父子分段设置
                </div>
              </div>
              <div class="tab-content">
                <div v-if="processMode === 'custom'">
                  <div class="vertical-form-item">
                    <div class="item-label">分段标识符：</div>
                    <el-input v-model="segmentIdentifier"/>
                  </div>
                  <div class="vertical-form-item">
                    <div class="item-label">最大分段长度：</div>
                    <div class="input-with-suffix">
                      <el-input v-model="maxChunkLength" type="number" placeholder="500" />
                      <span class="suffix-text">/token</span>
                    </div>
                  </div>
                  <div class="vertical-form-item">
                    <div class="item-label">分段重叠长度：</div>
                    <div class="input-with-suffix">
                      <el-input v-model="overlap" type="number" placeholder="50" />
                      <span class="suffix-text">/token</span>
                    </div>
                  </div>

                  <div class="text-processing">
                    <div class="item-label">文本预处理规则：</div>
                    <div class="text-processing-options">
                      <el-checkbox v-model="cleanEmptyLines">去除多余空格、换行符和制表符</el-checkbox>
                      <el-checkbox v-model="removeUrls">删除所有的URL和电子邮件地址</el-checkbox>
                      <el-checkbox v-model="useQaSplit">使用 Q&A 分段</el-checkbox>
                    </div>
                    <div class="preview-actions">
                      <el-button size="small" type="primary" @click="handlePreviewText">文本预览</el-button>
                      <!-- <el-button size="small">重置</el-button> -->
                    </div>
                  </div>
                </div>
                <div v-else-if="processMode === 'hierarchical'">
                  <!-- 父子分段设置 -->
                  <div class="parent-child-wrapper">
                    <!-- 父节点用于上下文 -->
                    <div class="pc-title">父节点用于上下文</div>
                    <div class="pc-options-wrapper">
                      <div class="pc-option-left">
                        <el-radio v-model="parentType" label="semantic" class="parent-radio">快速</el-radio>
                        <div class="option-desc">此模式根据分隔符和最大块长度为主的分段规则，使用分文本作为索引的块</div>
                        
                        <div v-if="parentType === 'semantic'" class="input-fields">
                          <div class="field-group">
                            <div class="field-label">分段标识符：</div>
                            <el-input v-model="parentSegmentIdentifier" placeholder="\n" />
                            <div class="field-hint">支持正则表达式</div>
                          </div>
                          
                          <div class="field-group">
                            <div class="field-label">分段最大长度：</div>
                            <div class="input-with-suffix">
                              <el-input v-model="parentMaxChunkLength" type="number" placeholder="500" />
                              <span class="suffix-text">tokens</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div class="pc-option-right">
                        <el-radio v-model="parentType" label="full" class="parent-radio">全文</el-radio>
                        <div class="option-desc">整个文档作为父文件处理，对于整理好的文档，建议选择此选项；若单个文档超过 10000 个标记的文本，建议使用自动截断</div>
                      </div>
                    </div>
                    
                    <!-- 子段用于检索 -->
                    <div class="pc-title">子段用于检索</div>
                    <div class="child-content">
                      <div class="field-group">
                        <div class="field-label">分段标识符：</div>
                        <el-input v-model="childSegmentIdentifier" placeholder="\n" />
                        <div class="field-hint">支持正则表达式</div>
                      </div>
                      
                      <div class="field-group">
                        <div class="field-label">分段最大长度：</div>
                        <div class="input-with-suffix">
                          <el-input v-model="childMaxChunkLength" type="number" placeholder="200" />
                          <span class="suffix-text">tokens</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="preview-actions">
                      <el-button size="small" type="primary" @click="handlePreviewText">文本预览</el-button>
                      <!-- <el-button size="small">重置</el-button> -->
                    </div>
                </div>
              </div>
            </div>
          </div>
          <div class="section-card">
            <div class="section-title">索引方式设置</div>
            <div class="index-options">
              <div 
                class="index-option-card" 
                :class="{ active: indexType === 'high_quality' }" 
                @click="indexType = 'high_quality'"
              >
                <div class="icon-container crown">
                  <el-icon><Star /></el-icon>
                </div>
                <div class="option-name">高质量模式</div>
                <div class="option-desc">适合需要精确匹配</div>
              </div>
              <div 
                class="index-option-card" 
                :class="{ active: indexType === 'economy' }" 
                @click="indexType = 'economy'"
              >
                <div class="icon-container lightning">
                  <el-icon><Lightning /></el-icon>
                </div>
                <div class="option-name">经济模式</div>
                <div class="option-desc">适合快速检索</div>
              </div>
            </div>
          </div>
          <div class="section-card">
            <div class="section-title">Embedding 模型</div>
            <el-select 
              v-model="embeddingModel" 
              placeholder="请选择Embedding模型" 
              class="full-width-select"
              value-key="model"
              filterable
            >
              <el-option-group
                v-for="provider in embeddingModelList"
                :key="provider.provider"
                :label="provider.label.zh_Hans || provider.label.en_US"
              >
                <el-option
                  v-for="model in provider.models"
                  :key="model.model"
                  :label="model.model"
                  :value="{provider: provider.provider, model: model.model}"
                />
              </el-option-group>
            </el-select>
          </div>
          <div class="section-card">
            <div class="section-title">检索策略设置</div>
            <div class="retrieval-strategy">
              <div class="strategy-tabs">
                <template v-if="indexType === 'high_quality'">
                  <div 
                    class="strategy-tab" 
                    :class="{ active: retrievalStrategy === 'vector' }"
                    @click="retrievalStrategy = 'vector'"
                  >
                    <el-icon><Compass /></el-icon>
                    <span>向量检索</span>
                    <div class="tab-desc">基于语义相似度的智能匹配</div>
                  </div>
                  <div 
                    class="strategy-tab" 
                    :class="{ active: retrievalStrategy === 'fulltext' }"
                    @click="retrievalStrategy = 'fulltext'"
                  >
                    <el-icon><Search /></el-icon>
                    <span>全文检索</span>
                    <div class="tab-desc">基于关键词的精确匹配</div>
                  </div>
                  <div 
                    class="strategy-tab" 
                    :class="{ active: retrievalStrategy === 'hybrid' }"
                    @click="retrievalStrategy = 'hybrid'"
                  >
                    <el-icon><Connection /></el-icon>
                    <span>混合检索</span>
                    <div class="tab-desc">综合语义与关键词的平衡检索</div>
                  </div>
                </template>
                <template v-else>
                  <div class="strategy-tab active">
                    <el-icon><Document /></el-icon>
                    <span>倒排索引</span>
                    <div class="tab-desc">倒排索引是一种用于高效检索的结构，按关键词索引文档或网页</div>
                  </div>
                </template>
              </div>

              <div class="retrieval-settings">
                <!-- 高质量模式下的设置 -->
                <template v-if="indexType === 'high_quality'">
                  <!-- 混合检索模式下的Tab切换 -->
                  <div v-if="retrievalStrategy === 'hybrid'" class="settings-tabs">
                    <div class="settings-header">
                      <div 
                        class="settings-tab"
                        :class="{ active: activeSettingsTab === 'weight' }"
                        @click="activeSettingsTab = 'weight'"
                      >
                        权重设置
                      </div>
                      <div 
                        class="settings-tab"
                        :class="{ active: activeSettingsTab === 'rerank' }"
                        @click="activeSettingsTab = 'rerank'"
                      >
                        Rerank设置
                      </div>
                    </div>
                    
                    <div class="settings-content">
                      <div v-if="activeSettingsTab === 'weight'">
                        <div class="weights-display-container">
                          <div class="weights-bar">
                            <div class="weight-display-item">
                              <div class="weight-label">语义权重</div>
                              <div class="weight-value">{{ semanticWeight.toFixed(1) }}</div>
                            </div>
                            <div class="weight-display-item">
                              <div class="weight-label">关键词权重</div>
                              <div class="weight-value">{{ keywordWeight.toFixed(1) }}</div>
                            </div>
                          </div>
                        </div>
                        
                        <div class="slider-section">
                          <div class="slider-with-value">
                            <el-slider 
                              v-model="semanticWeight" 
                              :min="0" 
                              :max="1" 
                              :step="0.1" 
                              :format-tooltip="formatWeightTooltip"
                              @input="updateWeights" 
                            />
                          </div>
                          
                          <div class="threshold-label">相似度阈值：{{ Math.round(scoreThreshold * 100) / 100 }}</div>
                          <div class="slider-with-value">
                            <el-slider v-model="scoreThreshold" :min="0" :max="1" :step="0.1" :format-tooltip="formatThresholdTooltip" />
                            <el-input-number v-model="scoreThreshold" :min="0" :max="1" :step="0.1" controls-position="right" size="small" :precision="1" />
                          </div>
                        </div>
                        
                        <div class="slider-section top-k-section">
                          <div class="threshold-label">Top K 结果数</div>
                          <div class="slider-with-value">
                            <el-slider v-model="topK" :min="1" :max="50" :step="1" />
                            <el-input-number v-model="topK" :min="1" :max="50" controls-position="right" size="small" />
                          </div>
                        </div>
                      </div>
                      
                      <div v-else>
                        <div class="slider-section">
                          <div class="threshold-label">相似度阈值：{{ Math.round(scoreThreshold * 100) / 100 }}</div>
                          <div class="slider-with-value">
                            <el-slider v-model="scoreThreshold" :min="0" :max="1" :step="0.1" :format-tooltip="formatThresholdTooltip" />
                            <el-input-number v-model="scoreThreshold" :min="0" :max="1" :step="0.1" controls-position="right" size="small" :precision="1" />
                          </div>
                        </div>
                        <div class="slider-section top-k-section">
                          <div class="threshold-label">Top K 结果数</div>
                          <div class="slider-with-value">
                            <el-slider v-model="topK" :min="1" :max="50" :step="1" />
                            <el-input-number v-model="topK" :min="1" :max="50" controls-position="right" size="small" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 非混合检索模式的相似度阈值设置 -->
                  <div v-else class="single-setting">
                    <div class="option-item">
                      <div class="threshold-label">相似度阈值：{{ Math.round(scoreThreshold * 100) / 100 }}</div>
                      <div class="slider-with-value">
                        <el-slider v-model="scoreThreshold" :min="0" :max="1" :step="0.1" :format-tooltip="formatThresholdTooltip" />
                        <el-input-number v-model="scoreThreshold" :min="0" :max="1" :step="0.1" controls-position="right" size="small" :precision="1" />
                      </div>
                    </div>
                  </div>
                  
                  <!-- 所有检索模式下都显示的Top K设置 -->
                  <div v-if="retrievalStrategy !== 'hybrid'" class="single-setting settings-row top-k-row">
                    <div class="top-k-settings">
                      <div class="threshold-label">Top K 结果数</div>
                      <div class="slider-with-value">
                        <el-slider v-model="topK" :min="1" :max="50" :step="1" />
                        <el-input-number v-model="topK" :min="1" :max="50" controls-position="right" size="small" />
                      </div>
                    </div>
                  </div>
                </template>
                
                <!-- 经济模式下只显示Top K设置 -->
                <template v-else>
                  <div class="single-setting settings-row top-k-row">
                    <div class="top-k-settings">
                      <div class="threshold-label">Top K <el-tooltip content="检索返回结果的最大数量" placement="top"><el-icon class="info-icon"><InfoFilled /></el-icon></el-tooltip></div>
                      <div class="slider-with-value">
                        <el-slider v-model="topK" :min="1" :max="50" :step="1" />
                        <el-input-number v-model="topK" :min="1" :max="50" controls-position="right" size="small" />
                      </div>
                    </div>
                  </div>
                </template>
                
                <!-- Rerank模型选择，只在高质量模式下显示 -->
                <div class="single-setting settings-row rerank-model top-k-row" v-if="indexType === 'high_quality' && (retrievalStrategy !== 'hybrid' || activeSettingsTab !== 'weight')">
                  <div class="threshold-label">Rerank 模型</div>
                  <el-select 
                    v-model="rerankModel" 
                    placeholder="请选择Rerank模型" 
                    class="full-width-select"
                    value-key="model"
                    filterable
                  >
                    <el-option-group
                      v-for="provider in rerankModelList"
                      :key="provider.provider"
                      :label="provider.label.zh_Hans || provider.label.en_US"
                    >
                      <el-option
                        v-for="model in provider.models"
                        :key="model.model"
                        :label="model.model"
                        :value="{provider: provider.provider, model: model.model}"
                      />
                    </el-option-group>
                  </el-select>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 右侧预览面板 -->
        <div class="right-panel">
          <div class="preview-content">
            <div class="panel-header">
            <div class="panel-title">文本预览</div>
            <div class="text-count">文本块数: {{ textPreview.total_segments || 0 }}</div>
          </div>
            <template v-if="previewLoading">
              <div class="loading-container">
                <el-icon class="loading-icon"><Loading /></el-icon>
                <div class="loading-text">正在加载预览内容...</div>
              </div>
            </template>
            <template v-else-if="textPreview.preview && textPreview.preview.length">
              <div class="chunks-container">
                <div v-for="(chunk, index) in textPreview.preview" :key="index" class="chunk-item">
                  <div class="chunk-header">:::分段-{{ index + 1 }} · {{ countChunkCharacters(chunk.content) }} characters</div>
                  <div class="chunk-content">{{ chunk.content }}</div>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="empty-preview">
                <el-icon><Document /></el-icon>
                <div class="empty-text">点击左侧"文本预览"按钮加载内容</div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>

    <div v-if="currentStep === 3" class="step-content">
      <div class="completion-container">
        <div class="completion-header">
          <div class="success-icon">
            <el-icon class="success-check"><CircleCheck /></el-icon>
          </div>
          <div class="success-title">知识库创建成功!</div>
          <div class="success-message">已创建知识库，并完成文档索引。您可以查看详情或立即开始使用。</div>
        </div>
        
        <div class="completion-details">
          <div class="detail-row">
            <div class="detail-label">知识库名称:</div>
            <div class="detail-value">{{ knowledgeName }}</div>
          </div>
          <div class="detail-row">
            <div class="detail-label">文档数量:</div>
            <div class="detail-value">{{ documents.length }}</div>
          </div>
          <div class="detail-row">
            <div class="detail-label">总索引分段:</div>
            <div class="detail-value">{{ totalChunks }}</div>
          </div>
          <div class="detail-row">
            <div class="detail-label">检索策略:</div>
            <div class="detail-value">{{ retrievalStrategyLabel }}</div>
          </div>
          <div class="detail-row">
            <div class="detail-label">模型:</div>
            <div class="detail-value">{{ embeddingModelLabel }}</div>
          </div>
        </div>
        
        <div class="completion-actions">
          <!-- <el-button type="primary" @click="goToKnowledgeDetail">查看详情</el-button> -->
          <el-button @click="goToKnowledgeList">返回列表</el-button>
        </div>
      </div>
    </div>

    <div class="footer">
      <el-button type="primary" @click="nextStep" v-if="currentStep < 3">下一步</el-button>
      <el-button type="primary" @click="finishCreate" v-else>完成</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  Document, 
  ArrowLeft, 
  Upload, 
  Check, 
  Close, 
  Loading, 
  Delete, 
  Link, 
  Plus, 
  Lightning, 
  Star, 
  Compass, 
  Search, 
  Connection, 
  CircleCheck 
} from '@element-plus/icons-vue';
import { 
  fetchDefaultProcessRule, 
  fetchRerankModelList,
  fetchTextEmbeddingModelList,
  fetchDefaultRerankModel,
  fetchDefaultTextEmbeddingModel,
  createFirstDocument,
  ProcessRule,
  ModelListResponse,
  DefaultModelResponse,
  CreateDocumentReq
} from '@/api/knowledge';
import { uploadFile } from '@/api/knowledge';
import { 
  fetchDatasetList, 
  createEmptyDataset, 
  indexingEstimate2,
  IndexingEstimateRequest,
  IndexingEstimateResponse
} from '@/api/knowledge';

// Define interfaces for better type safety
interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  status: 'uploading' | 'success' | 'error';
  progress: number;
  error?: string | null;
}

interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  chunks?: number;
}

const router = useRouter();
const route = useRoute();
const currentStep = ref(1);
const uploadType = ref('local');
const fileInput = ref<HTMLInputElement | null>(null);
const fileUploading = ref(false);
const fileList = ref<UploadedFile[]>([]);
const dragging = ref(false);

// Step 1 data
const knowledgeName = ref('');
const knowledgeDescription = ref('');
const documents = ref<Document[]>([]);

// Step 2 data
const processMode = ref('custom');
const segmentIdentifier = ref('\n\n');
const maxChunkLength = ref(500);
const overlap = ref(50);
const cleanEmptyLines = ref(true);
const removeUrls = ref(false);
const useQaSplit = ref(false);
const indexType = ref('high_quality');
const embeddingModel = ref<{provider: string, model: string}>({provider: '', model: ''});
const topK = ref(3);
const scoreThreshold = ref(0.7);
const rerankModel = ref<{provider: string, model: string}>({provider: '', model: ''});
const enableReranking = ref(true);
const enableRag = ref(true);
const retrievalStrategy = ref('vector');
const semanticWeight = ref(0.5);
const keywordWeight = ref(0.5);
const crossEncoderThreshold = ref(0.5);
const activeSettingsTab = ref('weight');
const parentType = ref('semantic');
const parentSegmentIdentifier = ref('\n');
const parentMaxChunkLength = ref(500);
const childSegmentIdentifier = ref('\n');
const childMaxChunkLength = ref(200);
const totalChunks = ref(0);

// 添加API数据状态
const loading = ref({
  defaultProcessRule: false,
  indexingEstimate: false,
  rerankModelList: false,
  embeddingModelList: false
});
const embeddingModelList = ref<any[]>([]);
const rerankModelList = ref<any[]>([]);
const defaultEmbeddingModel = ref<any>(null);
const defaultRerankModel = ref<any>(null);
const indexingEstimate = ref<any>(null);

// 添加文本预览相关数据
const showPreview = ref(false);
const textPreview = ref<any>({});
const previewLoading = ref(false);

// Computed properties
const retrievalStrategyLabel = computed(() => {
  const labels: Record<string, string> = {
    vector: '向量检索',
    fulltext: '全文检索',
    hybrid: '混合检索'
  };
  return labels[retrievalStrategy.value] || '向量检索';
});

const embeddingModelLabel = computed(() => {
  if (!embeddingModel.value || !embeddingModelList.value.length) return '';
  
  // 查找选中的模型提供商和名称
  for (const provider of embeddingModelList.value) {
    const found = provider.models.find((model: string) => model === embeddingModel.value.model);
    if (found) {
      return `${provider.provider_name || provider.provider} ${found}`;
    }
  }
  return embeddingModel.value.model;
});

// 获取模型的中文标签
const getModelLabel = (providerName: string, modelName: string) => {
  // 查找该提供商下的模型
  const provider = embeddingModelList.value.find((p: any) => p.provider === providerName);
  if (!provider) return modelName;
  
  // 获取模型在models数组中的索引
  const index = provider.models.findIndex((m: string) => m === modelName);
  if (index >= 0 && provider[index] && provider[index].label && provider[index].label.zh_Hans) {
    return provider[index].label.zh_Hans;
  }
  
  return modelName;
};

// 获取处理规则对象
const getProcessRule = (): ProcessRule => {
  // 将UI中的索引类型映射到API所需的格式
  const mappedIndexType = indexType.value === 'high_quality' ? 'QUALIFIED' : 'ECONOMICAL';
  
  // 修正mode值，将UI中的parent-child映射为API需要的parent_child
  let mode = processMode.value;
  
  return {
    rules: {
      pre_processing_rules: [
        {
          id: "remove_extra_spaces",
          enabled: cleanEmptyLines.value
        },
        {
          id: "remove_urls_emails",
          enabled: removeUrls.value
        }
      ],
      segmentation: {
        separator: convertEscapedToActual(segmentIdentifier.value || "\\n\\n"),
        max_tokens: maxChunkLength.value || 500,
        chunk_overlap: overlap.value || 50
      }
    },
    mode: mode // 使用固定的 custom 模式
  };
};

// 将转义的字符串转换为实际控制字符
const convertEscapedToActual = (str) => {
  // 处理常见的转义序列
  return str
    .replace(/\\n/g, '\n')  // 将 \n 转换为实际的换行符
    .replace(/\\t/g, '\t')  // 将 \t 转换为实际的制表符
    .replace(/\\r/g, '\r'); // 将 \r 转换为实际的回车符
};

// 获取默认处理规则
const fetchDefaultProcessRuleData = async () => {
  loading.value.defaultProcessRule = true;
  try {
    const response = await fetchDefaultProcessRule();
    const data = response;
    console.log("response", response);
    // // 设置处理模式
    // processMode.value = data.mode === 'custom' ? 'general' : data.mode || 'general';
    
    // 统一处理数据结构，不管返回的是什么mode
    if (data && data.rules) {
      // 处理分段设置
      if (data.rules.segmentation) {
        // 段落分隔符设置
        segmentIdentifier.value = "\\n\\n";
        // 区块长度设置
        maxChunkLength.value = data.rules.segmentation.max_tokens || 500;
        // 重叠长度设置
        overlap.value = data.rules.segmentation.chunk_overlap || 50;
      }
      
      console.log("rules", data.rules);
      // 处理预处理规则
      if (data.rules.pre_processing_rules && Array.isArray(data.rules.pre_processing_rules)) {
        // 查找特定规则
        const removeExtraSpaces = data.rules.pre_processing_rules.find(rule => rule.id === 'remove_extra_spaces');
        const removeUrlsEmails = data.rules.pre_processing_rules.find(rule => rule.id === 'remove_urls_emails');
        
      console.log("removeExtraSpaces", removeExtraSpaces);

        // 更新清除空行选项
        cleanEmptyLines.value = removeExtraSpaces?.enabled || false;
        
        // 更新移除URL选项
        removeUrls.value = removeUrlsEmails?.enabled || false;
      }
      
      // 父子结构相关设置(如果模式为parent_child)
      if (processMode.value === 'hierarchical') {
        // 参照参考项目的hierarchical模式处理
        if (data.rules.subchunk_segmentation) {
          // 子级分段设置
          childSegmentIdentifier.value = data.rules.subchunk_segmentation.separator || "\\n";
          // 子级区块长度设置
          childMaxChunkLength.value = data.rules.subchunk_segmentation.max_tokens || 200;
        }
        
        // 父级分段设置
        if (data.rules.segmentation) {
          parentSegmentIdentifier.value = data.rules.segmentation.separator || "\\n\\n";
          parentMaxChunkLength.value = data.rules.segmentation.max_tokens || 500;
        }
        
        // 父级模式
        if (data.rules.parent_mode) {
          // 如果您的项目中有对应的处理逻辑，可以在这里添加
          console.log('父级模式:', data.rules.parent_mode);
        }
      }
      
      // 设置索引类型（参照图1：高质量模式/经济模式）
      if (data.rules.indexing_technique) {
        // 映射索引技术到UI中的值
        // QUALIFIED对应高质量，ECONOMICAL对应经济模式
        if (data.rules.indexing_technique.toUpperCase() === 'QUALIFIED') {
          indexType.value = 'high_quality';
        } else if (data.rules.indexing_technique.toUpperCase() === 'ECONOMICAL') {
          indexType.value = 'economy';
        }
      }
      
      // 设置检索策略（参照图2：向量检索/全文检索/混合检索）
      if (data.rules.search_strategy) {
        // 参照图2设置搜索策略
        if (data.rules.search_strategy === 'vector') {
          retrievalStrategy.value = 'vector';
        } else if (data.rules.search_strategy === 'fulltext') {
          retrievalStrategy.value = 'fulltext';
        } else if (data.rules.search_strategy === 'hybrid') {
          retrievalStrategy.value = 'hybrid';
        }
        
        // 如果有相似度阈值设置
        if (data.rules.similarity_threshold !== undefined) {
          scoreThreshold.value = data.rules.similarity_threshold;
        }
        
        // 如果有topK设置
        if (data.rules.top_k !== undefined) {
          topK.value = data.rules.top_k;
        }
      }
    }
  } catch (error) {
    console.error('获取默认处理规则失败:', error);
  } finally {
    loading.value.defaultProcessRule = false;
  }
};

// 获取Rerank模型列表
const fetchRerankModelListData = async () => {
  loading.value.rerankModelList = true;
  try {
    const response = await fetchRerankModelList();
    if (response.data && Array.isArray(response.data)) {
      rerankModelList.value = response.data;
    }
  } catch (error) {
    console.error('获取Rerank模型列表失败:', error);
  } finally {
    loading.value.rerankModelList = false;
  }
};

// 获取文本嵌入模型列表
const fetchTextEmbeddingModelListData = async () => {
  loading.value.embeddingModelList = true;
  try {
    const response = await fetchTextEmbeddingModelList();
    if (response.data && Array.isArray(response.data)) {
      embeddingModelList.value = response.data;
      // embeddingModel.value = embeddingModelList.value.model
    }
  } catch (error) {
    console.error('获取文本嵌入模型列表失败:', error);
  } finally {
    loading.value.embeddingModelList = false;
  }
};

// 获取默认Rerank模型
const fetchDefaultRerankModelData = async () => {
  try {
    const response = await fetchDefaultRerankModel();
    if (response.data) {
      rerankModel.value =  {provider: response.data.provider.provider, model: response.data.model};
      // // 如果当前没有选择模型，使用默认模型
      // if (!rerankModel.value.model && defaultRerankModel.value.model) {
      //   rerankModel.value = {provider: defaultRerankModel.value.provider, model: defaultRerankModel.value.model};
      // }
    }
  } catch (error) {
    console.error('获取默认Rerank模型失败:', error);
  }
};

// 获取默认文本嵌入模型
const fetchDefaultTextEmbeddingModelData = async () => {
  try {
    const response = await fetchDefaultTextEmbeddingModel();
    if (response.data) {
      embeddingModel.value = {provider: response.data.provider.provider, model: response.data.model};
      
      // // 如果当前没有选择模型，使用默认模型
      // if (!embeddingModel.value.model && defaultEmbeddingModel.value.model) {
      //   embeddingModel.value = {provider: defaultEmbeddingModel.value.provider, model: defaultEmbeddingModel.value.model};
      // }
    }
  } catch (error) {
    console.error('获取默认文本嵌入模型失败:', error);
  }
};

// 初始化API数据
const initApiData = () => {
  fetchDefaultProcessRuleData();
  fetchRerankModelListData();
  fetchTextEmbeddingModelListData();
  fetchDefaultRerankModelData();
  fetchDefaultTextEmbeddingModelData();
};

// 监听流程变化
const handleStepChange = () => {
  if (currentStep.value === 2) {
    // 步骤2时加载必要的数据
    if (!defaultRerankModel.value || !defaultEmbeddingModel.value) {
      initApiData();
    }
    
    // 如果有文档，获取索引估算
    // if (documents.value.length > 0) {
    //   fetchFileIndexingEstimateData();
    // }
  }
};

// 监听处理规则变化，重新获取索引估算
// const handleProcessRuleChange = () => {
//   if (currentStep.value === 2 && documents.value.length > 0) {
//     fetchFileIndexingEstimateData();
//   }
// };

// Format threshold tooltip
const formatThresholdTooltip = (val: number) => {
  return val.toFixed(1);
};

// Format weight tooltip
const formatWeightTooltip = (val: number) => {
  return `语义: ${val.toFixed(1)} / 关键词: ${(1-val).toFixed(1)}`;
};

// Format file size
const formatFileSize = (size: number): string => {
  if (size < 1024) return size + ' B';
  const kb = size / 1024;
  if (kb < 1024) return kb.toFixed(1) + ' KB';
  const mb = kb / 1024;
  return mb.toFixed(1) + ' MB';
};

// 触发文件上传
const triggerFileUpload = () => {
  if (fileInput.value) {
    fileInput.value.click();
  }
};

// 处理文件选择
const handleFileSelect = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    uploadFiles(input.files);
  }
};

// 处理拖拽进入
const handleDragEnter = (event: DragEvent) => {
  event.preventDefault();
  dragging.value = true;
};

// 处理拖拽离开
const handleDragLeave = (event: DragEvent) => {
  event.preventDefault();
  dragging.value = false;
};

// 处理拖拽放置
const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  dragging.value = false;
  if (event.dataTransfer && event.dataTransfer.files.length > 0) {
    uploadFiles(event.dataTransfer.files);
  }
};

// 处理文件上传
const uploadFiles = async (files: FileList) => {
  fileUploading.value = true;
  console.log("正在处理上传文件", files);
  
  // 使用Promise.all并发处理所有文件上传
  const uploadPromises = [];
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const tempId = `temp-${Date.now()}-${i}`;
    
    // 添加到文件列表，使用临时ID
    fileList.value.push({
      id: tempId,
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'uploading',
      progress: 0,
      error: null
    });
    
    // 创建上传承诺
    const uploadPromise = uploadSingleFile(file, tempId);
    uploadPromises.push(uploadPromise);
  }
  
  // 等待所有上传完成
  try {
    await Promise.all(uploadPromises);
    fileUploading.value = false;
  } catch (error) {
    console.error('文件上传过程中发生错误:', error);
    fileUploading.value = false;
    ElMessage.error('上传文件时发生错误');
  }
};

// 上传单个文件
const uploadSingleFile = async (file: File, tempId: string) => {
  try {
    // 实际上传文件到服务器
    const response = await uploadFile(file);
    
    console.log('文件上传成功，服务器响应:', response);
    
    // 获取后端返回的真实文件ID
    const realFileId = response.data.id; // 使用后端返回的ID
    
    // 模拟上传完成，设置进度为100%
    const index = fileList.value.findIndex((f) => f.id === tempId);
    if (index !== -1) {
      // 更新文件状态
      fileList.value[index].status = 'success';
      fileList.value[index].progress = 100;
      fileList.value[index].id = realFileId; // 更新为真实ID
      
      // 添加到文档列表，使用真实ID
      documents.value.push({
        id: realFileId,
        name: fileList.value[index].name,
        type: fileList.value[index].type,
        size: fileList.value[index].size
      });
    }
    console.log("documents.value", documents.value);
    return realFileId;
  } catch (error) {
    console.error('上传文件失败:', error);
    
    // 更新文件状态为错误
    const index = fileList.value.findIndex((f) => f.id === tempId);
    if (index !== -1) {
      fileList.value[index].status = 'error';
      fileList.value[index].error = '上传失败';
    }
    
    throw error;
  }
};

// 移除文件
const removeFile = (id: string) => {
  const index = fileList.value.findIndex((file) => file.id === id);
  if (index !== -1) {
    fileList.value.splice(index, 1);
  }
  
  const docIndex = documents.value.findIndex((doc) => doc.id === id);
  if (docIndex !== -1) {
    documents.value.splice(docIndex, 1);
  }
};

// 返回上一步
const goBack = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  } else {
    // 获取当前应用的基础URL
    // const baseUrl = window.location.origin;
    // 使用完整URL进行导航，而不是相对路径
    // window.location.href = '/knowledge#' + new Date().getTime();
  router.push('/knowledge');
  }
};

// 下一步
const nextStep = async () => {
  if (currentStep.value === 1 && documents.value.length === 0 && uploadType.value !== 'empty') {
    ElMessage.warning('请上传至少一个文件');
    return;
  }
  
  // 当当前步骤是第二步，并且即将进入第三步时，调用创建知识库接口
  if (currentStep.value === 2) {
    try {
      // 1. 首先获取处理规则
      const originalRule = getProcessRule();
      
      // 2. 转换处理规则为接口期望的格式
      const processRule = {
        rules: {
          pre_processing_rules: [
            {
              id: "remove_extra_spaces",
              enabled: cleanEmptyLines.value
            },
            {
              id: "remove_urls_emails",
              enabled: removeUrls.value
            }
          ],
          segmentation: originalRule.rules.segmentation
        },
        mode: processMode.value // 使用固定的 custom 模式
      };
      
      // 打印出分隔符信息以便调试
      console.log('分隔符:', documents.value.map((doc: any) => doc.id));
      

      // 3. 准备创建知识库的参数
      const params: CreateDocumentReq = {
        space_id: "",
        data_source: {
          type: 'upload_file', // 使用 upload_file 而非 file
          info_list: {
            data_source_type: 'upload_file', // 使用 upload_file 而非 file
            file_info_list: {
              file_ids: documents.value.map((doc: any) => doc.id)
            }
          }
        },
        indexing_technique: indexType.value, // high_quality 或 economy
        process_rule: processRule as any, // 使用类型断言处理类型不匹配问题
        doc_form: 'text_model', // 使用 text_model 而非 text
        doc_language: 'Chinese', // 使用 Chinese 而非 zh-Hans
        retrieval_model: {
          search_method: retrievalStrategy.value === 'vector' ? 'semantic_search' : 
                       retrievalStrategy.value === 'keyword' ? 'keyword_search' : 'hybrid_search',
          reranking_enable: enableReranking.value,
          reranking_model: {
            reranking_provider_name: rerankModel.value?.provider || '',
            reranking_model_name: rerankModel.value?.model || ''
          },
          reranking_mode: "weighted_score",
          top_k: topK.value,
          score_threshold_enabled: enableRag.value,
          score_threshold: scoreThreshold.value,
        },
        embedding_model: embeddingModel.value.model,
        embedding_model_provider: embeddingModel.value.provider
      };
      
      // 如果是混合检索模式，添加权重设置到retrieval_model内部
      if (retrievalStrategy.value === 'hybrid') {
        params.retrieval_model.weights = {
          weight_type: "customized",
          vector_setting: {
            vector_weight: parseFloat(semanticWeight.value.toFixed(1)),
            embedding_provider_name: "",
            embedding_model_name: ""
          },
          keyword_setting: {
            keyword_weight: parseFloat(keywordWeight.value.toFixed(1))
          }
        };
      }
      
      // 检查URL中是否有spaceId参数，如果有则添加到请求中
      const spaceId = route.query.spaceId || "";
      console.log('spaceIdspaceIdspaceIdspaceId:', spaceId);
      if (spaceId) {
        (params as any).space_id = spaceId || "";
      }
      
      console.log('创建知识库参数:', params);
      
      // 4. 调用初始化接口
      const result = await createFirstDocument(params);
      console.log('创建知识库成功:', result);
      
      // 创建成功后进入下一步
      currentStep.value++;
      
    } catch (error: any) {
      console.error('创建知识库失败:', error);
      ElMessage.error(`创建知识库失败: ${error.message || '未知错误'}`);
      return;
    }
  } else {
    // 直接进入下一步
    currentStep.value++;
  }
  
  // 当进入步骤2时，初始化API数据
  if (currentStep.value === 2) {
    handleStepChange();
  }
};

// 完成创建
const finishCreate = () => {
  ElMessage.success('知识库创建成功');
  router.push('/knowledge');
};

// 前往知识库详情页面
const goToKnowledgeDetail = () => {
  router.push('/knowledge/detail/1');
};

// 返回知识库列表
const goToKnowledgeList = () => {
  router.push('/knowledge');
};

// 创建空知识库
const createEmptyKnowledgeBase = () => {
  uploadType.value = 'empty';
  ElMessage.info('已选择创建空知识库');
  
  // Clear any uploaded files
  fileList.value = [];
  documents.value = [];
  
  // Automatically go to next step
  nextStep();
};

// 更新权重
const updateWeights = (val: number) => {
  semanticWeight.value = parseFloat(val.toFixed(1));
  keywordWeight.value = parseFloat((1-val).toFixed(1));
};

// 文本预览处理函数
const handlePreviewText = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先上传文件');
    return;
  }
  
  previewLoading.value = true;
  showPreview.value = true;
  
  try {
    // 调用API获取文本预览数据
    const previewData = await getPreviewData();
    textPreview.value = previewData;
  } catch (error) {
    console.error('获取文本预览数据失败:', error);
    ElMessage.error('获取文本预览数据失败');
  } finally {
    previewLoading.value = false;
  }
};

// 获取文本预览数据
const getPreviewData = async () => {
  // 构建请求参数
  const fileIds = fileList.value
    .filter(file => file.status === 'success' && file.id)
    .map(file => file.id);
  
  if (fileIds.length === 0) {
    return { total_segments: 0, preview: [], qa_preview: null };
  }
  
  // 1. 首先获取处理规则
  const originalRule = getProcessRule();

  console.log('获取到的处理规则:', originalRule);
  // 构造API请求参数
  const requestData: IndexingEstimateRequest = {
    info_list: {
      data_source_type: "upload_file",
      file_info_list: {
        file_ids: fileIds
      }
    },
    indexing_technique: "high_quality",
    process_rule: {
        rules: {
          pre_processing_rules: [
            {
              id: "remove_extra_spaces",
              enabled: cleanEmptyLines.value
            },
            {
              id: "remove_urls_emails",
              enabled: removeUrls.value
            }
          ],
          segmentation: originalRule.rules.segmentation
        },
        mode: processMode.value // 使用固定的 custom 模式
      },
    doc_form: "text_model", 
    doc_language: "Chinese"
  };
  
  // 调用API
  const response = await indexingEstimate2(requestData);
  return response;
};

// 计算文本块字符数
const countChunkCharacters = (content: string) => {
  return content.length;
};

// 模拟获取文本预览数据
// const getPreviewData = async () => {
//   // 模拟数据
//   const previewData = {
//     total_segments: 10,
//     preview: [
//       { content: '文本块1内容' },
//       { content: '文本块2内容' },
//       { content: '文本块3内容' },
//     ]
//   };
  
//   // 模拟异步获取数据
//   await new Promise(resolve => setTimeout(resolve, 1000));
  
//   return previewData;
// };

// 在组件挂载时初始化数据
onMounted(() => {
  // 如果直接进入步骤2，初始化数据
  if (currentStep.value === 2) {
    initApiData();
  }
});
</script>

<style scoped>
.create-knowledge-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.back {
  cursor: pointer;
  margin-right: 20px;
}

.title {
  font-size: 24px;
  font-weight: bold;
}

.steps-container {
  margin-bottom: 40px;
}

.step-content {
  flex: 1;
  margin-bottom: 40px;
}

.upload-options {
  display: flex;
  justify-content: flex-start;
  gap: 20px;
  margin-bottom: 30px;
}

.option-card {
  width: 50%;
  height: 120px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.option-card:hover {
  border-color: #409eff;
  box-shadow: 0 0 10px rgba(0, 123, 255, 0.1);
}

.option-card.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.icon-container {
  margin: 0 auto;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}

.icon-container i {
  font-size: 24px;
  color: #409eff;
}

.notion-icon {
  background-color: #000;
  color: #fff;
}

.notion-logo {
  font-size: 24px;
  font-weight: bold;
}

.option-title {
  font-size: 14px;
  color: #333;
}

.upload-section {
  display: flex;
  gap: 20px;
}

.quick-upload {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
}

.uploaded-files {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
}

.upload-area {
  height: 200px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 40px;
  color: #909399;
  margin-bottom: 10px;
}

.upload-tip {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.upload-format {
  font-size: 12px;
  color: #909399;
}

.file-list {
  max-height: 300px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.file-item:last-child {
  border-bottom: none;
}

.file-icon {
  margin-right: 15px;
  font-size: 20px;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 14px;
  color: #303133;
  margin-bottom: 5px;
  word-break: break-all;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.file-status {
  margin-right: 15px;
}

.file-actions {
  display: flex;
  gap: 10px;
}

.action-icon {
  cursor: pointer;
  font-size: 18px;
  color: #606266;
}

.action-icon:hover {
  color: #409eff;
}

.footer {
  margin-top: auto;
  padding: 20px 0;
  display: flex;
  justify-content: center;
}

.footer .el-button {
  min-width: 120px;
  padding: 12px 20px;
  height: auto;
  font-size: 14px;
}

.config-container {
  display: flex;
  gap: 20px;
}

.left-panel {
  flex: 1.5;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background-color: #fff;
}

.tab-container {
  overflow: hidden;
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.tab-header {
  display: flex;
  background-color: #f5f7fa;
}

.tab-item {
  flex: 1;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.tab-item.active {
  color: #409eff;
  background-color: #ecf5ff;
  border-bottom: 2px solid #409eff;
}

.tab-item:hover {
  color: #409eff;
}

.tab-content {
  padding: 20px;
}

.vertical-form-item {
  margin-bottom: 20px;
}

.item-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.full-width-select {
  width: 100%;
}

.text-processing {
  margin-top: 20px;
}

.text-processing-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.preview-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.index-options {
  display: flex;
  gap: 20px;
  margin-top: 10px;
}

.index-option-card {
  flex: 1;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.index-option-card:hover {
  border-color: #409eff;
}

.index-option-card.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.option-name {
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0;
}

.option-desc {
  font-size: 12px;
  color: #909399;
}

.crown {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.lightning {
  background-color: #f0f9eb;
  color: #67c23a;
}

.strategy-tabs {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.strategy-tab {
  flex: 1;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.strategy-tab:hover {
  border-color: #409eff;
}

.strategy-tab.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.tab-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.settings-tabs {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.settings-header {
  display: flex;
  background-color: #f5f7fa;
}

.settings-tab {
  flex: 1;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.settings-tab.active {
  color: #409eff;
  background-color: #ecf5ff;
  border-bottom: 2px solid #409eff;
}

.settings-content {
  padding: 15px;
}

.weights-display-container {
  margin-bottom: 20px;
}

.weights-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.weight-display-item {
  display: flex;
  align-items: center;
}

.weight-label {
  font-size: 12px;
  color: #606266;
  margin-right: 5px;
}

.weight-value {
  font-size: 12px;
  font-weight: bold;
}

.weight-progress-container {
  margin-top: 10px;
}

.weight-progress {
  height: 24px;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
}

.semantic-progress {
  height: 100%;
  background-color: #409eff;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.keyword-progress {
  height: 100%;
  background-color: #67c23a;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.slider-section {
  margin-top: 15px;
}

.slider-with-value {
  display: flex;
  align-items: center;
  width: 100%;
}

.slider-with-value .el-slider {
  flex: 1;
  margin-right: 15px;
}

.threshold-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.option-item {
  margin-bottom: 15px;
}

.single-setting {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

.top-k-row {
  margin-top: 20px;
}

.top-k-section {
  margin-top: 20px;
}

.document-name-section {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background-color: #fff;
}

.document-count {
  font-size: 14px;
  color: #606266;
  margin-bottom: 15px;
}

.document-list-preview {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
}

.preview-text, .more-text {
  font-size: 12px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 10px;
}

.more-text {
  opacity: 0.7;
}

.document-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.enhanced-options {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.input-with-suffix {
  display: flex;
  align-items: center;
  width: 100%;
}

.input-with-suffix .el-input {
  max-width: 180px;
}

.suffix-text {
  margin-left: 8px;
  color: #909399;
}

.completion-container {
  padding: 40px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.completion-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
}

.success-icon {
  font-size: 40px;
  color: #67c23a;
  margin-bottom: 15px;
}

.success-title {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.success-message {
  font-size: 14px;
  color: #909399;
}

.completion-details {
  margin-bottom: 30px;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.detail-label {
  font-size: 14px;
  color: #606266;
  margin-right: 10px;
}

.detail-value {
  font-size: 14px;
  color: #303133;
}

.completion-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.parent-child-wrapper {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.pc-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.pc-options-wrapper {
  display: flex;
  gap: 20px;
}

.pc-option-left, .pc-option-right {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
}

.parent-radio {
  margin-bottom: 10px;
  font-weight: bold;
}

.field-group {
  margin-bottom: 15px;
}

.field-label {
  font-size: 13px;
  color: #606266;
  margin-bottom: 5px;
}

.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 3px;
}

.input-fields {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.custom-segmentation {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.custom-segmentation-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.custom-segmentation-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.custom-segmentation-content {
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
}

.custom-segmentation-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.custom-segmentation-form-item {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.custom-segmentation-form-item-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.upload-area.dragging {
  border-color: #409eff;
  background-color: #ecf5ff;
}

/* Preview panel styles */
.right-panel {
  display: flex;
  flex-direction: column;
  height: 1450px; /* 设置最大高度 */
}

.panel-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.text-count {
  font-size: 14px;
  color: #606266;
  margin-left: 10px;
}

.preview-content {
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fff;
  flex: 1;
  overflow: hidden; /* 确保内容不会溢出 */
  display: flex;
  flex-direction: column;
}

.chunks-container {
  overflow-y: auto; /* 添加垂直滚动条 */
  max-height: 100%; /* 使用100%高度 */
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.loading-icon {
  font-size: 24px;
  color: #909399;
  margin-bottom: 10px;
}

.loading-text {
  font-size: 14px;
  color: #606266;
}

.chunk-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.chunk-header {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.chunk-content {
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
  white-space: pre-wrap; /* 保留文本格式 */
  word-break: break-word; /* 长词断行 */
}

.empty-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.empty-text {
  font-size: 14px;
  color: #606266;
  margin-top: 10px;
}

.panel-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}
</style>
