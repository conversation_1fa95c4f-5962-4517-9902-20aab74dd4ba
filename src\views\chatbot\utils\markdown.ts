// 聊天消息处理工具
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import { debounce } from 'lodash-es';
import hljs from 'highlight.js';
import { nextTick } from 'vue';

// 初始化 Marked 配置
marked.use({
  breaks: true,
  gfm: true,
});

// 安全解析Markdown内容
export const safeMarkdownParse = (content: string): string => {
  if (!content) return '';
  
  try {
    const rawHtml = marked.parse(content) as string;
    const sanitized = DOMPurify.sanitize(rawHtml, {
      ALLOWED_TAGS: [
        'p', 'pre', 'code', 'strong', 'em', 'blockquote',
        'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'span', 'a', 'br', 'table', 'thead', 'tbody', 'tr', 'th', 'td'
      ],
      ALLOWED_ATTR: ['class', 'href', 'target', 'rel'],
      FORBID_ATTR: ['style', 'onerror'],
    });
    return sanitized;
  } catch (error) {
    console.error('Markdown解析错误:', error);
    return content.replace(/\n/g, '<br/>');
  }
};

// 处理聊天消息格式
export const processMessage = (message: any): any => {
  if (!message.content) return message;

  // 确保所有消息都有rawContent属性用于存储原始文本
  if (!message.rawContent) {
    message.rawContent = message.content;
  }

  // 对所有消息使用一致的解析方法
  message.content = safeMarkdownParse(message.rawContent);
  return message;
};

// 防抖高亮函数
export const debouncedHighlight = debounce(() => {
  nextTick(() => {
    document.querySelectorAll('pre code').forEach(block => {
      if (!(block as HTMLElement).classList.contains('hljs')) {
        hljs.highlightElement(block as HTMLElement);
      }
    });
  });
}, 50);

// 添加防抖的滚动函数
export const debouncedScrollToBottom = debounce((container: HTMLElement | null) => {
  if (container) {
    container.scrollTop = container.scrollHeight;
  }
}, 100);
