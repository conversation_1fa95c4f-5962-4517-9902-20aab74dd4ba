export interface LoginForm {
  email: string;
  password: string;
  token?: string;
  name?: string;
  interface_language?: string;
  timezone?: string;
}

export interface LoginResponse {
  data: {
    access_token: string;
    refresh_token: string;
  };
}

export interface LoginRequestData {
  [key: string]: unknown;
  email: string;
  password: string;
  language: string;
  remember_me: boolean;
}
