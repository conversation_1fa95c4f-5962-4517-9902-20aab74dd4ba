<template>
  <div class="home-container">
    <!-- 顶部区域 -->
    <div class="page-header">
      <h1 class="page-title">智能体市场</h1>
      <el-input v-model="searchQuery" placeholder="搜索" class="search-input" :prefix-icon="Search" />
    </div>

    <!-- 分类导航 -->
    <div class="category-nav">
      <div
        v-for="category in categories"
        :key="category.id"
        class="category-item"
        :class="{ active: currentCategory === category.id }"
        @click="currentCategory = category.id"
      >
        {{ category.name }}
      </div>
    </div>

    <!-- 智能体列表 -->
    <div class="home-content">
      <div class="agents-grid">
        <div v-for="(agent, index) in agentsList" :key="index" class="agent-card">
          <div class="card-main">
            <div class="agent-icon" :style="{ backgroundColor: agent.icon_background }">
              <span>{{ agent.icon }}</span>
            </div>
            <h3 class="agent-name">{{ agent.name }}</h3>
            <p class="agent-desc">{{ agent.description }}</p>
          </div>
          <div class="card-footer">
            <div class="agent-category">{{ AgentMode[agent.mode as keyof typeof AgentMode] }}</div>
            <!-- <span class="usage-count">
              <el-icon><View /></el-icon>
              {{ agent.usageCount }}
            </span> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { Search, View } from "@element-plus/icons-vue";
import { get } from "@/utils/request";
import type { AppsRecord, AppsListPageResponse } from "./types";
import { AgentMode } from "./types";

// 分类数据
const categories = [
  { id: "hot", name: "热门推荐" },
  { id: "education", name: "教育服务" },
  { id: "social", name: "泛互联网" },
  { id: "medical", name: "医疗健康" },
  { id: "government", name: "公共服务" },
  { id: "general", name: "通用" },
  { id: "other", name: "其他" },
];

const currentCategory = ref("hot");
const searchQuery = ref("");

const agentsList = ref<AppsRecord[]>([]);

// 获取智能体列表
const getAgentsList = async () => {
  let res = await get<AppsListPageResponse>("/apps", {
    page: 1,
    limit: 100,
    is_created_by_me: false,
    mode: "agent-chat",
  });
  agentsList.value = res.data;
};
onMounted(() => {
  getAgentsList();
});
</script>

<style scoped lang="scss">
.home-container {
  background-color: #f5f7fa;
  height: 100%;
  display: flex;
  flex-direction: column;
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .page-title {
      font-size: 24px;
      font-weight: 500;
      color: #333;
      margin: 0;
    }

    .search-input {
      width: 240px;

      :deep(.el-input__wrapper) {
        border-radius: 20px;
        background-color: #fff;
        border: 1px solid #dcdfe6;
        box-shadow: none;

        &.is-focus {
          border-color: #4080ff;
          box-shadow: none;
        }

        .el-input__prefix {
          color: #909399;
        }
      }

      :deep(.el-input__inner) {
        &::placeholder {
          color: #909399;
        }
      }
    }
  }

  .category-nav {
    display: flex;
    gap: 32px;
    margin-bottom: 24px;

    .category-item {
      font-size: 14px;
      color: #606266;
      cursor: pointer;
      padding: 8px 16px;

      &:hover {
        color: #4080ff;
      }

      &.active {
        color: #4080ff;
        font-weight: 500;
        background-color: #ebf2ff;
        border-radius: 4px;
      }
    }
  }
  .home-content {
    flex: 1;
    overflow-y: auto;
  }
  .agents-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    padding: 0;

    .agent-card {
      background: #fff;
      border-radius: 8px;
      cursor: pointer;
      border: 1px solid #e4e7ed;
      overflow: hidden;
      .card-main {
        padding: 20px 16px;
        .agent-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .agent-name {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
          line-height: 24px;
          margin: 16px 0;
        }

        .agent-category {
          font-size: 12px;
          color: #909399;
        }

        .agent-desc {
          width: 100%;
          margin: 0;
          font-size: 13px;
          color: #909399;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .card-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 14px 16px;
        border-top: 1px solid #ebeef5;
        font-size: 13px;
        color: #909399;
        .usage-count {
          display: flex;
          align-items: center;

          .el-icon {
            font-size: 16px;
            opacity: 0.5;
            margin-right: 4px;
          }
        }
      }

      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }
    }
  }
}
</style>
