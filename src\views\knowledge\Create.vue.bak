<template>
  <!-- ... -->
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { Document, ArrowLeft, Upload, Check, Close, Loading, Delete, Link, Plus, Lightning, Star, Compass, Search, Connection, ArrowRight } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

const router = useRouter();

const currentStep = ref(1);
const fileInput = ref<HTMLInputElement | null>(null);
const uploadedFiles = ref<any[]>([
  {
    name: '产品需求说明文档.pdf',
    size: 2.5 * 1024 * 1024,
    type: 'application/pdf',
    status: 'uploaded',
    progress: 100
  },
  {
    name: '技术架构设计方案.docx',
    size: 1.8 * 1024 * 1024,
    type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    status: 'uploading',
    progress: 80
  },
  {
    name: '数据分析报告.xlsx',
    size: 3.2 * 1024 * 1024,
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    status: 'failed',
    progress: 0
  }
]);

const segmentationType = ref('general');
const segmentIdentifier = ref('\\n\\n');
const maxChunkLength = ref(500);
const overlap = ref(50);
const cleanEmptyLines = ref(true);
const removeUrls = ref(false);
const useQaSplit = ref(false);
const indexType = ref('high_quality');
const embeddingModel = ref('text-embedding-3-large');
const topK = ref(20);
const scoreThreshold = ref(0.7);
const rerankModel = ref('rerank-v3.0');
const enableReranking = ref(true);
const enableRag = ref(true);
const retrievalStrategy = ref('vector');
const semanticWeight = ref(0.7);
const keywordWeight = ref(0.3);
const crossEncoderThreshold = ref(0.3);
const activeSettingsTab = ref('weight');
const parentType = ref('semantic');
const parentSegmentIdentifier = ref('\\n');
const parentMaxChunkLength = ref(500);
const childSegmentIdentifier = ref('\\n');
const childMaxChunkLength = ref(200);

// 触发文件上传
const triggerFileUpload = () => {
  if (fileInput.value) {
    fileInput.value.click();
  }
};

// 处理文件选择
const handleFileChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    const file = target.files[0];
    // 这里后续处理文件上传逻辑
    const fileObj = {
      name: file.name,
      size: file.size,
      type: file.type,
      file: file,
      status: 'uploading',
      progress: 0
    };
    
    uploadedFiles.value.push(fileObj);
    
    // 模拟上传过程
    simulateUpload(uploadedFiles.value.length - 1);
  }
};

// 模拟文件上传
const simulateUpload = (fileIndex: number) => {
  const interval = setInterval(() => {
    if (uploadedFiles.value[fileIndex].progress < 100) {
      uploadedFiles.value[fileIndex].progress += 10;
    } else {
      uploadedFiles.value[fileIndex].status = 'uploaded';
      clearInterval(interval);
    }
  }, 300);
};

// 移除文件
const removeFile = (index: number) => {
  uploadedFiles.value.splice(index, 1);
};

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) return size + ' B';
  const kb = size / 1024;
  if (kb < 1024) return kb.toFixed(1) + ' KB';
  const mb = kb / 1024;
  return mb.toFixed(1) + ' MB';
};

// 下一步
const nextStep = () => {
  if (currentStep.value < 3) {
    currentStep.value++;
  }
};

// 完成创建
const finishCreate = () => {
  ElMessage.success('知识库创建成功');
  router.push('/knowledge');
};

// 返回
const goBack = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  } else {
    router.push('/knowledge');
  }
};

// 创建空知识库
const createEmptyKnowledgeBase = () => {
  ElMessage.info('创建空知识库功能待实现');
  // 后续实现创建空知识库的逻辑
};

const formatThresholdTooltip = (val: number) => {
  return `${Math.round(val * 100)}%`;
};

const goToApi = () => {
  // 后续实现跳转到 API 页面的逻辑
};
</script>

<style scoped>
.create-knowledge-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.back {
  cursor: pointer;
  margin-right: 20px;
}

.title {
  font-size: 24px;
  font-weight: bold;
}

.steps-container {
  margin-bottom: 40px;
}

.step-content {
  flex: 1;
  margin-bottom: 40px;
}

.upload-options {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.option-card {
  width: 22%;
  height: 120px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.option-card:hover {
  border-color: #409eff;
  box-shadow: 0 0 10px rgba(0, 123, 255, 0.1);
}

.option-card.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.icon-container {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}

.icon-container i {
  font-size: 24px;
  color: #409eff;
}

.notion-icon {
  background-color: #000;
  color: #fff;
}

.notion-logo {
  font-size: 24px;
  font-weight: bold;
}

.option-title {
  font-size: 14px;
  color: #333;
}

.upload-section {
  display: flex;
  gap: 20px;
}

.quick-upload {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
}

.uploaded-files {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
}

.upload-area {
  height: 200px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 40px;
  color: #909399;
  margin-bottom: 10px;
}

.upload-tip {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.upload-format {
  font-size: 12px;
  color: #909399;
}

.file-list {
  max-height: 300px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.file-item:last-child {
  border-bottom: none;
}

.file-icon {
  margin-right: 15px;
  font-size: 20px;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 14px;
  color: #303133;
  margin-bottom: 5px;
  word-break: break-all;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.file-status {
  margin-right: 15px;
}

.file-actions {
  display: flex;
  gap: 10px;
}

.action-icon {
  cursor: pointer;
  font-size: 18px;
  color: #606266;
}

.action-icon:hover {
  color: #409eff;
}

.footer {
  margin-top: auto;
  padding: 20px 0;
  display: flex;
  justify-content: center;
}

.footer .el-button {
  min-width: 120px;
  padding: 12px 20px;
  height: auto;
  font-size: 14px;
}

.config-container {
  display: flex;
  gap: 20px;
}

.left-panel {
  flex: 3;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background-color: #fff;
}

.tab-container {
  overflow: hidden;
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.tab-header {
  display: flex;
  background-color: #f5f7fa;
}

.tab-item {
  padding: 10px 20px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s;
}

.tab-item.active {
  color: #409eff;
  background-color: #fff;
  border-bottom: 2px solid #409eff;
}

.tab-item:hover {
  color: #409eff;
}

.tab-content {
  padding: 20px;
}

.vertical-form-item {
  margin-bottom: 20px;
}

.item-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.full-width-select {
  width: 100%;
}

.text-processing {
  margin-top: 20px;
}

.text-processing-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.preview-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.index-options {
  display: flex;
  gap: 20px;
  margin-top: 10px;
}

.index-option-card {
  flex: 1;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.index-option-card:hover {
  border-color: #409eff;
}

.index-option-card.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.option-name {
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0;
}

.option-desc {
  font-size: 12px;
  color: #909399;
}

.crown {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.lightning {
  background-color: #f0f9eb;
  color: #67c23a;
}

.strategy-tabs {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.strategy-tab {
  flex: 1;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.strategy-tab:hover {
  border-color: #409eff;
}

.strategy-tab.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.tab-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.settings-tabs {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.settings-header {
  display: flex;
  background-color: #f5f7fa;
}

.settings-tab {
  flex: 1;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.settings-tab.active {
  color: #409eff;
  background-color: #ecf5ff;
  border-bottom: 2px solid #409eff;
}

.settings-content {
  padding: 15px;
}

.weights-display-container {
  margin-bottom: 20px;
}

.weights-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.weight-display-item {
  display: flex;
  align-items: center;
}

.weight-label {
  font-size: 12px;
  color: #606266;
  margin-right: 5px;
}

.weight-value {
  font-size: 12px;
  font-weight: bold;
}

.weight-progress-container {
  margin-top: 10px;
}

.weight-progress {
  height: 24px;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
}

.semantic-progress {
  height: 100%;
  background-color: #409eff;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.keyword-progress {
  height: 100%;
  background-color: #67c23a;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.slider-section {
  margin-top: 15px;
}

.slider-with-value {
  display: flex;
  align-items: center;
  width: 100%;
}

.slider-with-value .el-slider {
  flex: 1;
  margin-right: 15px;
}

.threshold-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.option-item {
  margin-bottom: 15px;
}

.single-setting {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

.top-k-row {
  margin-top: 20px;
}

.top-k-section {
  margin-top: 20px;
}

.document-name-section {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background-color: #fff;
}

.document-count {
  font-size: 14px;
  color: #606266;
  margin-bottom: 15px;
}

.document-list-preview {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
}

.preview-text, .more-text {
  font-size: 12px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 10px;
}

.more-text {
  opacity: 0.7;
}

.document-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.enhanced-options {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.input-with-suffix {
  display: flex;
  align-items: center;
  width: 100%;
}

.input-with-suffix .el-input {
  max-width: 180px;
}

.suffix-text {
  margin-left: 8px;
  color: #909399;
}

.final-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  max-width: 800px;
  margin: 0 auto;
}

.kb-created {
  text-align: center;
  margin-bottom: 50px;
  width: 100%;
}

.creation-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #303133;
}

.creation-desc {
  font-size: 14px;
  color: #909399;
  margin-bottom: 40px;
}

.uploaded-file-list {
  margin-bottom: 50px;
  width: 100%;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #ffffff;
}

.final-actions {
  display: flex;
  gap: 15px;
}

.api-btn, .kb-btn {
  min-width: 120px;
  padding: 12px 20px;
  height: auto;
  font-size: 14px;
}

.btn-icon {
  margin-left: 5px;
}

.parent-child-wrapper {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.pc-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.pc-options-wrapper {
  display: flex;
  gap: 20px;
}

.pc-option-left, .pc-option-right {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
}

.parent-radio {
  margin-bottom: 10px;
  font-weight: bold;
}

.field-group {
  margin-bottom: 15px;
}

.field-label {
  font-size: 13px;
  color: #606266;
  margin-bottom: 5px;
}

.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 3px;
}

.input-fields {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
</style>
