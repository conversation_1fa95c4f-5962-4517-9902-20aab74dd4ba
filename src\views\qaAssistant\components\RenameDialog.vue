<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="emit('update:modelValue', $event)"
    title="重命名会话"
    width="400px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="rename-form">
      <div class="form-item">
        <div class="label">会话名称</div>
        <el-input v-model="chatName" placeholder="请输入会话名称" clearable />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import type { IConversation } from "../types";

const props = defineProps<{
  modelValue: boolean;
  chat: IConversation;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
  (e: "confirm", value: string): void;
}>();

const chatName = ref("");

// 监听chat属性的变化
watch(
  () => props.chat,
  newChat => {
    chatName.value = newChat.name;
  },
  { immediate: true }
);

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  val => {
    if (val) {
      chatName.value = props.chat.name;
    }
  }
);

const handleClose = () => {
  emit("update:modelValue", false);
};

const handleConfirm = () => {
  if (!chatName.value.trim()) {
    return;
  }
  emit("confirm", chatName.value.trim());
  handleClose();
};
</script>

<style lang="scss" scoped>
.rename-form {
  padding: 20px 0;
  .form-item {
    .label {
      font-size: 14px;
      color: #606266;
      margin-bottom: 8px;
    }
  }
}
</style>
