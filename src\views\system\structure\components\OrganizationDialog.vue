<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑组织架构' : '添加组织架构'"
    width="500px"
    :before-close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" label-position="right">
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入组织架构名称" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入组织架构描述" />
      </el-form-item>
      <el-form-item v-if="!isEdit && parentName" label="上级组织">
        <el-input v-model="parentName" disabled />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import { post, put } from "@/utils/request";
import type { Organization } from "../types";

// 定义props
const props = defineProps<{
  visible: boolean;
  isEdit?: boolean;
  organization?: Organization | null;
  parentId?: string | null;
  parentName?: string | null;
}>();

// 定义emit
const emit = defineEmits<{
  "update:visible": [value: boolean];
  refresh: [];
}>();

// 表单ref
const formRef = ref<FormInstance>();
// 加载状态
const loading = ref(false);

// 计算属性：控制弹窗显示隐藏
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit("update:visible", value),
});

// 计算属性：是否为编辑模式
const isEdit = computed(() => props.isEdit || false);

// 计算属性：父组织名称
const parentName = computed(() => props.parentName || "");

// 表单数据
const form = reactive({
  name: "",
  description: "",
  parent_id: props.parentId || null,
});

// 表单验证规则
const rules = reactive<FormRules>({
  name: [{ required: true, message: "请输入组织架构名称", trigger: "blur" }],
});

// 初始化表单数据
const initForm = () => {
  if (isEdit.value && props.organization) {
    form.name = props.organization.name;
    form.description = props.organization.description || "";
  } else {
    form.name = "";
    form.description = "";
    form.parent_id = props.parentId || null;
  }
};

// 监听对话框打开状态
watch(
  () => dialogVisible.value,
  newVal => {
    if (newVal) {
      // 对话框打开时，初始化表单数据
      initForm();
    }
  }
);

// 重置表单为空值
const resetForm = () => {
  form.name = "";
  form.description = "";
  // 保留 parent_id 不变，因为它是由当前上下文决定的
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (valid) {
      loading.value = true;
      try {
        if (isEdit.value && props.organization) {
          // 编辑组织架构
          await put(`/workspaces/current/organizations/${props.organization.id}`, form);
          ElMessage.success("编辑成功");
        } else {
          // 添加组织架构
          await post("/workspaces/current/organizations", form);
          ElMessage.success("添加成功");
        }
        emit("refresh");
        handleClose();
        dialogVisible.value = false;
      } catch (error) {
        console.error("操作失败:", error);
      } finally {
        loading.value = false;
      }
    }
  });
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
