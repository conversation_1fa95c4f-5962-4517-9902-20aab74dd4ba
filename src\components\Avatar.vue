<template>
  <el-avatar v-if="avatarUrl" :src="avatarUrl" :size="size" />
  <div class="avatar-name" :style="{ width: size + 'px', height: size + 'px', backgroundColor }" v-else>
    {{ name.slice(0, 1).toUpperCase() }}
  </div>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    avatarUrl: string | null | undefined;
    name: string;
    size?: number | string;
    backgroundColor?: string;
  }>(),
  {
    avatarUrl: "",
    name: "",
    size: 40,
    backgroundColor: "#155eef",
  }
);
</script>

<style scoped lang="scss">
.avatar-name {
  border-radius: 50%;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}
</style>
