<template>
  <div class="app-layout">
    <Header
      class="layout-header"
      :main-menus="mainMenus"
      :active-main-menu="activeMainMenu"
      @menu-click="handleMainMenuClick"
    />
    <div class="layout-container">
      <Sidebar class="layout-sidebar" :sub-menus="activeSubMenus" />
      <div class="layout-content">
        <div class="content-wrapper">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, shallowRef } from "vue";
import { useRouter } from "vue-router";
import Header from "./components/Header.vue";
import Sidebar from "./components/Sidebar.vue";
import { mainMenus as menuConfig } from "@/config/menu";
import type { MainMenu, SubMenu } from "@/types/menu";

defineOptions({
  name: "Layout",
});

const router = useRouter();
// 使用 shallowRef 处理菜单数据，避免深层响应式
const mainMenus = shallowRef<MainMenu[]>(menuConfig);
const activeMainMenu = ref<string>("discover");
const activeSubMenus = shallowRef<SubMenu[]>([]);

// 初始化时根据当前路由设置激活的一级菜单
const initActiveMenu = () => {
  const currentPath = router.currentRoute.value.path;

  for (const menu of mainMenus.value) {
    // 检查直接路径
    if (menu.path && currentPath.startsWith(menu.path)) {
      activeMainMenu.value = menu.id;
      activeSubMenus.value = [];
      return;
    }

    // 检查子菜单
    if (menu.children && menu.children.length > 0) {
      const found = menu.children.find(sub => currentPath.startsWith(sub.path));
      if (found) {
        activeMainMenu.value = menu.id;
        activeSubMenus.value = menu.children;
        return;
      }
    }
  }
};

// 处理一级菜单点击
const handleMainMenuClick = (menu: MainMenu) => {
  // 更新激活的主菜单
  activeMainMenu.value = menu.id;

  // 如果菜单有直接路径，则直接跳转
  if (menu.path) {
    activeSubMenus.value = []; // 清空子菜单
    router.push(menu.path);
    return;
  }

  // 更新子菜单
  if (menu.children && menu.children.length > 0) {
    activeSubMenus.value = menu.children;
    // 跳转到第一个子菜单的路由
    router.push(menu.children[0].path);
  } else {
    activeSubMenus.value = [];
  }
};

// 初始化
onMounted(() => {
  initActiveMenu();
});
</script>

<style scoped lang="scss">
.app-layout {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f9fafb;

  .layout-header {
    flex-shrink: 0;
    z-index: 100;
  }

  .layout-container {
    flex: 1;
    display: flex;
    overflow: hidden;

    .layout-sidebar {
      flex-shrink: 0;
      height: 100%;
      z-index: 99;
    }

    .layout-content {
      flex: 1;
      overflow: hidden;
      position: relative;

      .content-wrapper {
        height: 100%;
        overflow: auto;
      }
    }
  }
}

// 路由切换动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
