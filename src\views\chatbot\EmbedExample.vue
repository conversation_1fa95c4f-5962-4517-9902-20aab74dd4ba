<template>
  <div class="embed-example">
    <h1>嵌入式聊天机器人示例</h1>
    
    <div class="example-container">
      <div class="example-section">
        <h2>1. 添加iframe到您的网页</h2>
        <p>将以下代码复制到您的网页中，替换<code>YOUR_TOKEN</code>为您的聊天机器人token：</p>
        <div class="code-block">
          <pre><code>&lt;iframe
  src="http://your-domain.com/chatbot/YOUR_TOKEN"
  style="width: 100%; height: 600px; border: none;"
  allow="microphone"
  frameborder="0"
&gt;&lt;/iframe&gt;</code></pre>
          <el-button size="small" type="primary" @click="copyIframeCode">
            <el-icon><CopyDocument /></el-icon> 复制代码
          </el-button>
        </div>
      </div>
      
      <div class="example-section">
        <h2>2. 预览效果</h2>
        <p>以下是嵌入聊天机器人的效果预览：</p>
        <div class="iframe-container">
          <iframe
            :src="`${baseUrl}/chatbot/${demoToken}`"
            frameborder="0"
            allow="microphone"
          ></iframe>
        </div>
      </div>
      
      <div class="example-section">
        <h2>3. 自定义选项</h2>
        <p>您可以通过修改iframe的样式来自定义聊天机器人的外观：</p>
        <ul>
          <li><strong>宽度和高度</strong>：修改<code>width</code>和<code>height</code>属性</li>
          <li><strong>边框</strong>：使用<code>border</code>属性设置边框样式</li>
          <li><strong>圆角</strong>：使用<code>border-radius</code>添加圆角效果</li>
          <li><strong>阴影</strong>：使用<code>box-shadow</code>添加阴影效果</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { CopyDocument } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

// 基础URL，根据环境变量设置
const baseUrl = ref(import.meta.env.VITE_APP_BASE_URL || window.location.origin);

// 示例token，实际使用时应替换为真实token
const demoToken = ref('demo12345');

// 复制iframe代码
const copyIframeCode = () => {
  const code = `<iframe
  src="${baseUrl.value}/chatbot/${demoToken.value}"
  style="width: 100%; height: 600px; border: none;"
  allow="microphone"
  frameborder="0"
></iframe>`;

  // 使用clipboard API复制代码
  navigator.clipboard.writeText(code)
    .then(() => {
      ElMessage.success('代码已复制到剪贴板');
    })
    .catch(() => {
      ElMessage.error('复制失败，请手动复制');
    });
};
</script>

<style scoped>
.embed-example {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px 16px;
}

h1 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #1f2937;
  text-align: center;
}

.example-container {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.example-section {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

h2 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #374151;
}

p {
  margin-bottom: 16px;
  line-height: 1.6;
  color: #4b5563;
}

.code-block {
  background-color: #f3f4f6;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
}

.code-block pre {
  margin: 0;
  overflow-x: auto;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
}

.code-block button {
  position: absolute;
  top: 8px;
  right: 8px;
}

.iframe-container {
  width: 100%;
  height: 500px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.iframe-container iframe {
  width: 100%;
  height: 100%;
}

ul {
  padding-left: 24px;
  margin-bottom: 16px;
}

li {
  margin-bottom: 8px;
  line-height: 1.6;
  color: #4b5563;
}

code {
  font-family: monospace;
  background-color: #f3f4f6;
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 14px;
  color: #374151;
}

@media (max-width: 768px) {
  .embed-example {
    padding: 16px;
  }
  
  .example-section {
    padding: 16px;
  }
  
  .iframe-container {
    height: 400px;
  }
}
</style>
