<template>
  <el-drawer v-model="visible" title="快速配置" direction="rtl" size="600px" :destroy-on-close="true">
    <div class="config-drawer" v-loading="loading">
      <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
        <template v-if="credentialSchemas?.length">
          <el-form-item
            v-for="schema in credentialSchemas"
            :key="schema.variable"
            :label="schema.label.zh_Hans"
            :prop="schema.variable"
          >
            <el-input
              v-model="formData[schema.variable]"
              :placeholder="schema.placeholder?.zh_Hans"
              :type="schema.type === 'secret' ? 'password' : 'text'"
              size="large"
            />
          </el-form-item>
        </template>
      </el-form>

      <div class="button-group">
        <el-button
          type="danger"
          plain
          size="large"
          block
          @click="handleRemove"
          v-if="currentProvider?.custom_configuration?.status === 'active'"
          >移除</el-button
        >
        <el-button plain size="large" block @click="handleCancel">取消</el-button>
        <el-button type="primary" size="large" block @click="handleSave">保存</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { PropType } from "vue";
import type { ProviderRecord } from "../types";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessageBox, ElMessage } from "element-plus";
import { get, del, post } from "@/utils/request";

// 定义接口返回类型
interface CredentialsResponse {
  credentials: Record<string, string>;
}

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  currentProvider: {
    type: Object as PropType<ProviderRecord | null>,
    default: null,
  },
});

const emit = defineEmits(["update:modelValue", "refresh"]);
const formRef = ref<FormInstance>();

const visible = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emit("update:modelValue", val),
});

const credentialSchemas = computed(() => {
  return props.currentProvider?.provider_credential_schema?.credential_form_schemas || [];
});

const formData = ref<Record<string, string>>({});
const rules = ref<FormRules>({});

const loading = ref(false);

// 初始化表单数据和规则
const initFormData = (schemas: any[]) => {
  const newFormData: Record<string, string> = {};
  const newRules: FormRules = {};

  schemas.forEach(schema => {
    // 初始化表单数据
    newFormData[schema.variable] = "";

    // 生成校验规则
    newRules[schema.variable] = [
      {
        required: schema.required ?? false,
        message: `请输入${schema.label.zh_Hans}`,
        trigger: ["blur", "change"],
      },
    ];
  });

  formData.value = newFormData;
  rules.value = newRules;
};

// 获取配置详情
const getConfigurationDetail = async () => {
  if (props.currentProvider?.custom_configuration?.status === "active") {
    try {
      // 使用封装的get方法发起请求
      const { credentials } = await get<CredentialsResponse>(
        `/workspaces/current/model-providers/${props.currentProvider?.provider}/credentials`
      );
      // 更新表单数据
      Object.keys(formData.value).forEach(key => {
        if (credentials[key]) {
          formData.value[key] = credentials[key];
        }
      });
    } catch (error) {
      console.error("获取配置详情失败", error);
    }
  }
};

// 统一监听抽屉显示状态和表单schema变化
watch(
  [visible, credentialSchemas],
  async ([newVisible, newSchemas]) => {
    if (newSchemas?.length) {
      // 初始化表单
      initFormData(newSchemas);

      // 如果是抽屉打开，则获取详情
      if (newVisible) {
        await getConfigurationDetail();
      }
    }
  },
  { immediate: true }
);

const handleSave = async () => {
  if (!formRef.value) return;
  try {
    loading.value = true;
    await formRef.value.validate(async valid => {
      if (valid) {
        await post(`/workspaces/current/model-providers/${props.currentProvider?.provider}`, {
          config_from: "predefined-model",
          credentials: formData.value,
          load_balancing: {
            enabled: false,
            configs: [],
          },
        });
        ElMessage.success("保存成功");
        emit("refresh");
        visible.value = false;
      }
    });
  } catch (error) {
    ElMessage.error("保存失败");
    console.error("保存失败:", error);
  } finally {
    loading.value = false;
  }
};

const handleCancel = () => {
  formRef.value?.resetFields();
  visible.value = false;
};

const handleRemove = async () => {
  try {
    await ElMessageBox.confirm("确认删除？", "提示", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
      confirmButtonClass: "el-button--danger",
    });
    try {
      loading.value = true;
      await del(`/workspaces/current/model-providers/${props.currentProvider?.provider}`);
      ElMessage.success("删除成功");
      emit("refresh");
      emit("remove");
      visible.value = false;
    } catch (error) {
      ElMessage.error("删除失败");
      console.error("删除失败:", error);
    } finally {
      loading.value = false;
    }
  } catch {
    // 用户取消操作，不做任何处理
  }
};
</script>

<style scoped lang="scss">
.config-drawer {
  padding: 0 20px;
  position: relative;
  min-height: 200px;

  .config-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
    }
  }

  .button-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 40px;

    :deep(.el-button) {
      width: 100%;
      margin: 0;
      padding-left: 0;
      padding-right: 0;
    }
  }

  .related-docs {
    margin-top: 40px;

    .docs-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
    }

    .docs-list {
      .docs-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        color: #666;
        cursor: pointer;

        &:hover {
          color: #4972f7;
        }

        .el-icon {
          margin-right: 8px;
        }
      }
    }
  }
}
</style>
