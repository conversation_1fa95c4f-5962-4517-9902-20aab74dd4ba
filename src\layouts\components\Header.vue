<template>
  <div class="header">
    <div class="left">
      <img src="@/assets/images/logo.png" alt="logo" class="logo" />
      <h1 class="title">LeAgent乐睿智能体平台</h1>
      <!-- <el-button type="primary" class="create-btn" title="创建智能体" size="large">
        <img src="@/assets/images/ic_agent_white.png" alt="" class="create-btn-icon" />
        <div class="create-btn-title">创建智能体</div>
      </el-button> -->
    </div>

    <div class="main-menu">
      <div
        v-for="menu in mainMenus"
        :key="menu.id"
        class="menu-item"
        :class="{ active: activeMainMenu === menu.id }"
        @click="$emit('menu-click', menu)"
      >
        <template v-if="menu.url">
          <img class="menu-img" :src="menu.activeUrl" alt="" v-if="activeMainMenu === menu.id" />
          <img class="menu-img" :src="menu.url" alt="" v-else />
        </template>
        <el-icon v-else-if="menu.icon"><component :is="menu.icon" /></el-icon>
        <div class="menu-title">{{ menu.title }}</div>
      </div>
    </div>

    <div class="right">
      <el-dropdown trigger="click">
        <div class="user-info">
          <Avatar
            :avatar-url="userInfo?.avatar"
            :name="userInfo?.name || 'admin'"
            size="32"
            background-color="#3F56F0"
          />
          <div class="username">{{ userInfo?.name || "admin" }}</div>
          <el-icon :size="16" color="#000"><ArrowDownBold /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleLogout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";
import { get } from "@/utils/request";
import { getUser } from "@/utils/user";
import type { MainMenu, UserInfo } from "@/types/menu";
import { Plus, ArrowDownBold } from "@element-plus/icons-vue";
import Avatar from "@/components/Avatar.vue";

defineOptions({
  name: "Header",
});

// Props 定义
defineProps<{
  mainMenus: MainMenu[];
  activeMainMenu: string;
}>();

// Emits 定义
defineEmits<{
  "menu-click": [menu: MainMenu];
}>();

const router = useRouter();
const userInfo = ref<UserInfo | null>(getUser() as UserInfo | null);

const handleLogout = async () => {
  await get("/logout");
  localStorage.clear();
  router.push("/login");
};
</script>

<style scoped lang="scss">
.header {
  padding: 0 32px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    display: flex;
    align-items: center;
    min-width: 240px;
    .logo {
      width: 32px;
      height: 32px;
      overflow: hidden;
    }
    .title {
      font-weight: 400;
      font-size: 24px;
      color: #060606;
      line-height: 32px;
      margin-left: 12px;
    }
    .create-btn {
      display: flex;
      align-items: center;
      padding: 8px;
      color: #fff;
      background: linear-gradient(15deg, #4671fc 0%, #ad8fff 100%);
      border-radius: 4px 4px 4px 4px;
      margin-left: 42px;
      .create-btn-icon {
        width: 20px;
        height: 20px;
        overflow: hidden;
      }
      .create-btn-title {
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        line-height: 20px;
        margin-left: 6px;
      }
    }
  }

  .main-menu {
    flex: 1;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 0;
    .menu-item {
      display: flex;
      align-items: center;
      border-radius: 3px;
      cursor: pointer;
      transition: all 0.3s;
      padding: 12px 25px;
      margin-right: 32px;
      &:last-child {
        margin-right: 0;
      }
      .menu-img {
        width: 20px;
        height: 20px;
        overflow: hidden;
      }
      .menu-title {
        font-weight: 400;
        font-size: 16px;
        color: #1a1a1a;
        margin-left: 2px;
      }
      .el-icon {
        font-size: 16px;
      }
      &:hover {
        background: #eff6fe;
      }
      &.active {
        background: #eff6fe;
        .menu-title {
          font-weight: 600;
          color: #3a67f8;
        }
      }
    }
  }

  .right {
    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: background-color 0.3s;
      &:hover {
        background-color: #f5f7fa;
      }
      .avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
      }
      .username {
        font-weight: 400;
        font-size: 18px;
        color: #000000;
        margin: 0 10px;
      }
    }
  }
}
</style>
